#!/usr/bin/env python3
"""
VLC检查工具
用于诊断VLC安装和配置问题
"""
import sys
import os
import platform
import subprocess
import logging

def check_vlc_installation():
    """检查VLC安装状态"""
    print("=" * 60)
    print("VLC安装状态检查")
    print("=" * 60)
    
    results = {
        'python_vlc': False,
        'vlc_binary': False,
        'vlc_libs': False,
        'system_info': {}
    }
    
    # 系统信息
    results['system_info'] = {
        'platform': platform.system(),
        'architecture': platform.machine(),
        'python_version': sys.version,
        'is_packaged': hasattr(sys, '_MEIPASS')
    }
    
    print(f"系统: {results['system_info']['platform']} {results['system_info']['architecture']}")
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"打包环境: {'是' if results['system_info']['is_packaged'] else '否'}")
    print()
    
    # 1. 检查python-vlc模块
    print("1. 检查python-vlc模块...")
    try:
        import vlc
        print("✓ python-vlc模块已安装")
        print(f"  版本: {vlc.__version__ if hasattr(vlc, '__version__') else '未知'}")
        results['python_vlc'] = True
    except ImportError as e:
        print(f"✗ python-vlc模块未安装: {e}")
        print("  解决方案: pip install python-vlc")
    
    # 2. 检查VLC可执行文件
    print("\n2. 检查VLC可执行文件...")
    vlc_commands = ['vlc', 'cvlc']
    if platform.system() == "Windows":
        vlc_commands = ['vlc.exe']
    
    for cmd in vlc_commands:
        try:
            result = subprocess.run([cmd, '--version'], 
                                  capture_output=True, 
                                  text=True, 
                                  timeout=10)
            if result.returncode == 0:
                print(f"✓ 找到VLC可执行文件: {cmd}")
                version_line = result.stdout.split('\n')[0] if result.stdout else "版本未知"
                print(f"  {version_line}")
                results['vlc_binary'] = True
                break
        except (FileNotFoundError, subprocess.TimeoutExpired):
            continue
    
    if not results['vlc_binary']:
        print("✗ 未找到VLC可执行文件")
        if platform.system() == "Linux":
            print("  解决方案: sudo apt-get install vlc")
        elif platform.system() == "Windows":
            print("  解决方案: 从 https://www.videolan.org/vlc/ 下载安装VLC")
        elif platform.system() == "Darwin":
            print("  解决方案: brew install --cask vlc")
    
    # 3. 检查VLC库文件
    print("\n3. 检查VLC库文件...")
    if results['python_vlc']:
        try:
            import vlc
            # 尝试创建VLC实例
            instance = vlc.Instance(['--intf=dummy', '--quiet'])
            if instance:
                print("✓ VLC库文件正常，可以创建实例")
                instance.release()
                results['vlc_libs'] = True
            else:
                print("✗ 无法创建VLC实例")
        except Exception as e:
            print(f"✗ VLC库文件问题: {e}")
            if "libvlc" in str(e).lower():
                if platform.system() == "Linux":
                    print("  解决方案: sudo apt-get install libvlc-dev")
                elif platform.system() == "Windows":
                    print("  解决方案: 确保VLC安装目录在PATH中")
    
    # 4. 环境变量检查
    print("\n4. 检查环境变量...")
    vlc_plugin_path = os.environ.get('VLC_PLUGIN_PATH')
    if vlc_plugin_path:
        print(f"VLC_PLUGIN_PATH: {vlc_plugin_path}")
    else:
        print("VLC_PLUGIN_PATH: 未设置")
    
    # 5. 总结和建议
    print("\n" + "=" * 60)
    print("检查结果总结")
    print("=" * 60)
    
    all_good = all([results['python_vlc'], results['vlc_binary'], results['vlc_libs']])
    
    if all_good:
        print("✓ VLC环境配置正常，应该可以正常使用广播功能")
    else:
        print("✗ VLC环境存在问题，需要修复以下项目：")
        if not results['python_vlc']:
            print("  - 安装python-vlc模块: pip install python-vlc")
        if not results['vlc_binary']:
            print("  - 安装VLC媒体播放器")
        if not results['vlc_libs']:
            print("  - 修复VLC库文件问题")
    
    return results

def provide_installation_guide():
    """提供安装指南"""
    print("\n" + "=" * 60)
    print("VLC安装指南")
    print("=" * 60)
    
    system = platform.system()
    
    if system == "Linux":
        print("""
Ubuntu/Debian系统:
1. 更新包列表: sudo apt-get update
2. 安装VLC: sudo apt-get install vlc
3. 安装开发库: sudo apt-get install libvlc-dev
4. 安装Python模块: pip install python-vlc

CentOS/RHEL系统:
1. 启用EPEL: sudo yum install epel-release
2. 安装VLC: sudo yum install vlc
3. 安装Python模块: pip install python-vlc
        """)
    
    elif system == "Windows":
        print("""
Windows系统:
1. 访问 https://www.videolan.org/vlc/
2. 下载适合您系统的VLC安装包
3. 运行安装程序，使用默认设置
4. 安装Python模块: pip install python-vlc
5. 确保VLC安装目录在系统PATH中

注意: 建议安装64位版本的VLC
        """)
    
    elif system == "Darwin":
        print("""
macOS系统:
1. 使用Homebrew: brew install --cask vlc
2. 或从官网下载: https://www.videolan.org/vlc/
3. 安装Python模块: pip install python-vlc
        """)

def main():
    """主函数"""
    print("智慧课堂VLC环境检查工具")
    
    # 执行检查
    results = check_vlc_installation()
    
    # 提供安装指南
    provide_installation_guide()
    
    # 返回状态码
    all_good = all([results['python_vlc'], results['vlc_binary'], results['vlc_libs']])
    return 0 if all_good else 1

if __name__ == "__main__":
    sys.exit(main())
