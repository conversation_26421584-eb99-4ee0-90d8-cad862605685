"""
网络配置管理器
自动获取和管理网络地址，避免硬编码
"""
import socket
import subprocess
import platform
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging
from resource_manager import path_manager

class NetworkConfigManager:
    """网络配置管理器"""
    
    def __init__(self, config_file: str = "network_config.yaml"):
        self.logger = logging.getLogger(__name__)
        # 使用统一的路径管理器
        self.config_file = Path(path_manager.get_config_path(config_file))
        self.config_file.parent.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "auto_detect": True,
            "fallback_addresses": {
                "local_ip": "127.0.0.1",
                "backend_host": "localhost",
                "mediamtx_host": "localhost"
            },
            "ports": {
                "backend_http": 5000,
                "backend_https": 5000,
                "mediamtx_rtsp": 8554,
                "mediamtx_rtmp": 1935,
                "mediamtx_hls": 8888,
                "mediamtx_api": 9997,
                "udp_discovery": 8888,
                "tcp_communication": 8889,
                "http_api": 8890,
                "websocket": 8891
            },
            "protocols": {
                "backend_use_https": True,
                "mediamtx_use_rtsp": True
            }
        }
        
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    user_config = yaml.safe_load(f) or {}
                self.config = self._merge_config(self.default_config, user_config)
            else:
                self.config = self.default_config.copy()
                self._save_config()
        except Exception as e:
            self.logger.error(f"加载网络配置失败: {e}")
            self.config = self.default_config.copy()
    
    def _save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        except Exception as e:
            self.logger.error(f"保存网络配置失败: {e}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            if not self.config.get("auto_detect", True):
                return self.config["fallback_addresses"]["local_ip"]
            
            # 方法1: 连接外部地址获取本地IP
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                    s.connect(("*******", 80))
                    local_ip = s.getsockname()[0]
                    if self._is_valid_ip(local_ip):
                        return local_ip
            except:
                pass
            
            # 方法2: 获取主机名对应的IP
            try:
                hostname = socket.gethostname()
                local_ip = socket.gethostbyname(hostname)
                if self._is_valid_ip(local_ip) and not local_ip.startswith("127."):
                    return local_ip
            except:
                pass
            
            # 方法3: 遍历网络接口
            try:
                import netifaces
                for interface in netifaces.interfaces():
                    addresses = netifaces.ifaddresses(interface)
                    if netifaces.AF_INET in addresses:
                        for addr_info in addresses[netifaces.AF_INET]:
                            ip = addr_info.get('addr')
                            if (self._is_valid_ip(ip) and 
                                not ip.startswith("127.") and 
                                not ip.startswith("169.254.")):
                                return ip
            except ImportError:
                pass
            
            # 方法4: 使用系统命令
            local_ip = self._get_ip_from_system_command()
            if local_ip:
                return local_ip
            
            # 回退到默认值
            return self.config["fallback_addresses"]["local_ip"]
            
        except Exception as e:
            self.logger.error(f"获取本地IP失败: {e}")
            return self.config["fallback_addresses"]["local_ip"]
    
    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址是否有效"""
        try:
            socket.inet_aton(ip)
            return True
        except socket.error:
            return False
    
    def _get_ip_from_system_command(self) -> Optional[str]:
        """通过系统命令获取IP地址"""
        try:
            system = platform.system().lower()
            
            if system == "linux":
                # Linux: 使用ip命令
                result = subprocess.run(
                    ["ip", "route", "get", "*******"], 
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'src' in line:
                            parts = line.split()
                            src_index = parts.index('src')
                            if src_index + 1 < len(parts):
                                return parts[src_index + 1]
            
            elif system == "darwin":  # macOS
                result = subprocess.run(
                    ["route", "get", "default"], 
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if 'interface:' in line:
                            interface = line.split(':')[1].strip()
                            # 获取接口IP
                            ifconfig_result = subprocess.run(
                                ["ifconfig", interface], 
                                capture_output=True, text=True, timeout=5
                            )
                            if ifconfig_result.returncode == 0:
                                for ifconfig_line in ifconfig_result.stdout.split('\n'):
                                    if 'inet ' in ifconfig_line and 'netmask' in ifconfig_line:
                                        ip = ifconfig_line.split()[1]
                                        if self._is_valid_ip(ip) and not ip.startswith("127."):
                                            return ip
            
            elif system == "windows":
                # Windows: 使用ipconfig
                result = subprocess.run(
                    ["ipconfig"], 
                    capture_output=True, text=True, timeout=5
                )
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for i, line in enumerate(lines):
                        if 'IPv4' in line and '192.168.' in line:
                            ip = line.split(':')[1].strip()
                            if self._is_valid_ip(ip):
                                return ip
            
            return None
            
        except Exception as e:
            self.logger.error(f"通过系统命令获取IP失败: {e}")
            return None
    
    def get_backend_url(self, use_https: Optional[bool] = None) -> str:
        """获取后端URL"""
        try:
            host = self.get_local_ip()
            
            if use_https is None:
                use_https = self.config["protocols"]["backend_use_https"]
            
            if use_https:
                port = self.config["ports"]["backend_https"]
                protocol = "https"
            else:
                port = self.config["ports"]["backend_http"]
                protocol = "http"
            
            return f"{protocol}://{host}:{port}"
            
        except Exception as e:
            self.logger.error(f"获取后端URL失败: {e}")
            fallback_host = self.config["fallback_addresses"]["backend_host"]
            fallback_port = self.config["ports"]["backend_https"]
            return f"https://{fallback_host}:{fallback_port}"
    
    def get_mediamtx_urls(self) -> Dict[str, str]:
        """获取MediaMTX相关URL"""
        try:
            host = self.get_local_ip()
            ports = self.config["ports"]
            
            return {
                "rtsp": f"rtsp://{host}:{ports['mediamtx_rtsp']}",
                "rtmp": f"rtmp://{host}:{ports['mediamtx_rtmp']}",
                "hls": f"http://{host}:{ports['mediamtx_hls']}",
                "api": f"http://{host}:{ports['mediamtx_api']}"
            }
            
        except Exception as e:
            self.logger.error(f"获取MediaMTX URLs失败: {e}")
            fallback_host = self.config["fallback_addresses"]["mediamtx_host"]
            ports = self.config["ports"]
            return {
                "rtsp": f"rtsp://{fallback_host}:{ports['mediamtx_rtsp']}",
                "rtmp": f"rtmp://{fallback_host}:{ports['mediamtx_rtmp']}",
                "hls": f"http://{fallback_host}:{ports['mediamtx_hls']}",
                "api": f"http://{fallback_host}:{ports['mediamtx_api']}"
            }
    
    def get_stream_url(self, stream_key: str, protocol: str = "rtsp") -> str:
        """获取流URL"""
        try:
            mediamtx_urls = self.get_mediamtx_urls()
            base_url = mediamtx_urls.get(protocol.lower())
            
            if not base_url:
                raise ValueError(f"不支持的协议: {protocol}")
            
            return f"{base_url}/{stream_key}"
            
        except Exception as e:
            self.logger.error(f"获取流URL失败: {e}")
            fallback_host = self.config["fallback_addresses"]["mediamtx_host"]
            fallback_port = self.config["ports"]["mediamtx_rtsp"]
            return f"rtsp://{fallback_host}:{fallback_port}/{stream_key}"
    
    def get_discovery_config(self) -> Dict[str, Any]:
        """获取设备发现配置"""
        return {
            "udp_port": self.config["ports"]["udp_discovery"],
            "tcp_port": self.config["ports"]["tcp_communication"],
            "local_ip": self.get_local_ip()
        }
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        return {
            "http_port": self.config["ports"]["http_api"],
            "websocket_port": self.config["ports"]["websocket"],
            "local_ip": self.get_local_ip()
        }
    
    def update_config(self, updates: Dict[str, Any]):
        """更新配置"""
        try:
            def update_nested_dict(d, u):
                for k, v in u.items():
                    if isinstance(v, dict):
                        d[k] = update_nested_dict(d.get(k, {}), v)
                    else:
                        d[k] = v
                return d
            
            update_nested_dict(self.config, updates)
            self._save_config()
            self.logger.info("网络配置已更新")
            
        except Exception as e:
            self.logger.error(f"更新配置失败: {e}")
    
    def test_connectivity(self) -> Dict[str, Any]:
        """测试网络连通性"""
        results = {}
        
        try:
            # 测试本地IP获取
            local_ip = self.get_local_ip()
            results["local_ip"] = {
                "address": local_ip,
                "reachable": self._test_ip_reachable(local_ip)
            }
            
            # 测试后端连接
            backend_url = self.get_backend_url()
            results["backend"] = {
                "url": backend_url,
                "reachable": self._test_url_reachable(backend_url)
            }
            
            # 测试MediaMTX连接
            mediamtx_urls = self.get_mediamtx_urls()
            results["mediamtx"] = {}
            for protocol, url in mediamtx_urls.items():
                if protocol == "api":
                    results["mediamtx"][protocol] = {
                        "url": url,
                        "reachable": self._test_url_reachable(f"{url}/v3/config/global/get")
                    }
                else:
                    results["mediamtx"][protocol] = {
                        "url": url,
                        "reachable": False  # 流协议需要特殊测试
                    }
            
            return results
            
        except Exception as e:
            self.logger.error(f"测试网络连通性失败: {e}")
            return {"error": str(e)}
    
    def _test_ip_reachable(self, ip: str) -> bool:
        """测试IP是否可达"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(2)
                result = s.connect_ex((ip, 80))
                return result == 0
        except:
            return False
    
    def _test_url_reachable(self, url: str) -> bool:
        """测试URL是否可达"""
        try:
            import requests
            response = requests.get(url, timeout=5)
            return response.status_code < 400
        except:
            return False
    
    def get_network_info(self) -> Dict[str, Any]:
        """获取网络信息摘要"""
        return {
            "local_ip": self.get_local_ip(),
            "backend_url": self.get_backend_url(),
            "mediamtx_urls": self.get_mediamtx_urls(),
            "discovery_config": self.get_discovery_config(),
            "api_config": self.get_api_config(),
            "auto_detect_enabled": self.config.get("auto_detect", True)
        }

# 全局实例
network_config = NetworkConfigManager()