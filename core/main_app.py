"""
主应用程序 - 改造后的Core教学工具启动入口
"""
import sys
import os
import warnings
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from PyQt5.QtWebEngineWidgets import QWebEngineView

# 过滤PyQt5的弃用警告
warnings.filterwarnings("ignore", category=DeprecationWarning, module="PyQt5")

# 确保DISPLAY环境变量设置正确
if 'DISPLAY' not in os.environ:
    os.environ['DISPLAY'] = ':0'

# 设置高DPI支持 - 必须在创建QApplication之前设置
QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
QApplication.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from auth_manager import AuthManager
from login_dialog import LoginDialog
from module_manager import ModuleManager
from main_window import MainWindow
from resource_manager import initialize_qtawesome

class CoreApplication(QApplication):
    """Core教学工具主应用程序"""

    def __init__(self, argv):
        super().__init__(argv)

        initialize_qtawesome()

        # 设置应用程序属性
        self.setApplicationName("智慧课堂教学工具")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("Mastaji")
        
        # 初始化管理器
        self.auth_manager = AuthManager()
        self.module_manager = ModuleManager()
        self.main_window = None
        
        # 连接信号
        self.setup_signals()
        
        # 设置应用程序不会因为最后一个窗口关闭而退出
        self.setQuitOnLastWindowClosed(False)
    
    def setup_signals(self):
        """设置信号连接"""
        # 认证管理器信号
        self.auth_manager.login_success.connect(self.on_login_success)
        self.auth_manager.login_failed.connect(self.on_login_failed)
        self.auth_manager.logout_success.connect(self.on_logout_success)
        
        # 模块管理器信号
        self.module_manager.module_loaded.connect(self.on_module_loaded)
        self.module_manager.module_failed.connect(self.on_module_failed)
    
    def start(self):
        """启动应用程序"""
        # 尝试加载缓存的令牌
        if self.auth_manager.load_cached_token():
            # 已有有效令牌，直接启动主界面
            user_info = self.auth_manager.get_current_user()
            self.on_login_success(user_info)
        else:
            # 显示登录对话框
            self.show_login_dialog()
    
    def show_login_dialog(self):
        """显示登录对话框"""
        dialog = LoginDialog()
        
        if dialog.exec_() == QDialog.Accepted:
            result = dialog.get_result()
            
            if result['mode'] == 'login':
                # 教师登录模式
                self.perform_login(result['username'], result['password'])
            elif result['mode'] == 'guest':
                # 游客模式（仅通用功能）
                self.start_guest_mode()
        else:
            # 用户取消登录，退出应用
            self.quit()
    
    def perform_login(self, username: str, password: str):
        """执行登录"""
        # 显示加载对话框
        loading_dialog = self.create_loading_dialog("正在登录...")
        loading_dialog.show()
        
        # 创建工作线程进行登录
        self.login_thread = LoginThread(self.auth_manager, username, password)
        self.login_thread.finished.connect(loading_dialog.close)
        self.login_thread.start()
    
    def start_guest_mode(self):
        """启动游客模式"""
        # 设置模块管理器为未认证状态
        self.module_manager.set_auth_status(False, 'guest')

        # 创建并显示主窗口
        self.create_main_window()

        # 自动加载基础模块（游客模式也需要设备发现）
        self.main_window.auto_load_essential_modules()

        # 显示提示信息
        QMessageBox.information(
            self.main_window,
            "游客模式",
            "仅使用基础功能。"
        )
    
    def create_loading_dialog(self, message: str) -> QDialog:
        """创建加载对话框"""
        dialog = QDialog()
        dialog.setWindowTitle("请稍候")
        dialog.setFixedSize(300, 100)
        dialog.setWindowFlags(Qt.Dialog | Qt.CustomizeWindowHint | Qt.WindowTitleHint)
        dialog.setModal(True)
        
        layout = QVBoxLayout(dialog)
        
        # 进度条
        progress = QProgressBar()
        progress.setRange(0, 0)  # 无限进度条
        layout.addWidget(progress)
        
        # 消息标签
        label = QLabel(message)
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        return dialog
    
    def create_main_window(self):
        """创建主窗口"""
        self.main_window = MainWindow(
            auth_manager=self.auth_manager,
            module_manager=self.module_manager
        )
        self.main_window.show()
    
    def on_login_success(self, user_info: dict):
        """登录成功处理"""
        # 设置模块管理器认证状态
        self.module_manager.set_auth_manager(self.auth_manager)
        self.module_manager.set_auth_status(True, user_info.get('role', 'teacher'))
        
        # 创建并显示主窗口
        self.create_main_window()
        self.main_window.on_login_success(user_info)
        
    
    def on_login_failed(self, error_message: str):
        """登录失败处理"""
        QMessageBox.warning(None, "登录失败", error_message)
        
        # 重新显示登录对话框
        QTimer.singleShot(100, self.show_login_dialog)
    
    def on_logout_success(self):
        """登出成功处理"""
        # 关闭主窗口
        if self.main_window:
            self.main_window.close()
            self.main_window.deleteLater()
            self.main_window = None
        
        # 重置模块管理器状态
        self.module_manager.set_auth_status(False, 'guest')
        self.module_manager.close_all_modules()
        
        # 显示登录对话框
        self.show_login_dialog()
    
    def on_module_loaded(self, module_name: str):
        """模块加载成功处理"""
        print(f"模块 {module_name} 加载成功")
    
    def on_module_failed(self, module_name: str, error_message: str):
        """模块加载失败处理"""
        print(f"模块 {module_name} 加载失败: {error_message}")
        
        if self.main_window:
            QMessageBox.warning(
                self.main_window,
                "模块加载失败",
                f"无法加载模块 {module_name}:\n{error_message}"
            )

class LoginThread(QThread):
    """登录线程"""
    
    def __init__(self, auth_manager: AuthManager, username: str, password: str):
        super().__init__()
        self.auth_manager = auth_manager
        self.username = username
        self.password = password
    
    def run(self):
        """执行登录"""
        self.auth_manager.login_with_credentials(self.username, self.password)

def main():
    """主函数"""
    try:
        # 创建应用程序实例
        app = CoreApplication(sys.argv)

        # 设置应用程序图标
        from resource_manager import get_logo_path
        icon_path = get_logo_path()
        if os.path.exists(icon_path):
            app.setWindowIcon(QIcon(icon_path))
        else:
            print(f"Warning: Logo file not found at {icon_path}")

        # 设置应用程序样式
        app.setStyle('Fusion')  # 使用现代样式

        # 启动应用程序
        app.start()

        # 运行事件循环
        return app.exec_()

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())