#!/usr/bin/env python3
"""
PyInstaller hook for VLC libraries
确保VLC库文件被正确打包
"""

import os
import shutil
from PyInstaller.utils.hooks import collect_data_files

# VLC库文件路径
vlc_lib_paths = [
    '/usr/lib/x86_64-linux-gnu/libvlc.so.5.6.0',
    '/usr/lib/x86_64-linux-gnu/libvlccore.so.9.0.0'
]

binaries = []

# 添加VLC库文件到binaries
for lib_path in vlc_lib_paths:
    if os.path.exists(lib_path):
        binaries.append((lib_path, '.'))
        print(f"VLC Hook: 添加库文件 {lib_path}")
    else:
        print(f"VLC Hook: 库文件不存在 {lib_path}")

# 创建符号链接的后处理函数
def post_install_hook(dist_dir):
    """在安装后创建符号链接"""
    try:
        # 创建符号链接
        vlc_main = os.path.join(dist_dir, 'libvlc.so.5.6.0')
        vlccore_main = os.path.join(dist_dir, 'libvlccore.so.9.0.0')
        
        if os.path.exists(vlc_main):
            # 创建libvlc.so.5符号链接
            vlc_link = os.path.join(dist_dir, 'libvlc.so.5')
            if not os.path.exists(vlc_link):
                os.symlink('libvlc.so.5.6.0', vlc_link)
                print(f"VLC Hook: 创建符号链接 {vlc_link}")
            
            # 创建libvlc.so符号链接
            vlc_link2 = os.path.join(dist_dir, 'libvlc.so')
            if not os.path.exists(vlc_link2):
                os.symlink('libvlc.so.5.6.0', vlc_link2)
                print(f"VLC Hook: 创建符号链接 {vlc_link2}")
        
        if os.path.exists(vlccore_main):
            # 创建libvlccore.so.9符号链接
            vlccore_link = os.path.join(dist_dir, 'libvlccore.so.9')
            if not os.path.exists(vlccore_link):
                os.symlink('libvlccore.so.9.0.0', vlccore_link)
                print(f"VLC Hook: 创建符号链接 {vlccore_link}")
            
            # 创建libvlccore.so符号链接
            vlccore_link2 = os.path.join(dist_dir, 'libvlccore.so')
            if not os.path.exists(vlccore_link2):
                os.symlink('libvlccore.so.9.0.0', vlccore_link2)
                print(f"VLC Hook: 创建符号链接 {vlccore_link2}")
                
    except Exception as e:
        print(f"VLC Hook: 创建符号链接失败: {e}")

print(f"VLC Hook: 找到 {len(binaries)} 个VLC库文件")
