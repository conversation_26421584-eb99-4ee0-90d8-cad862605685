# 广播播放器配置文件 - 基于VLC的版本
# 专门用于小组接收教师屏幕广播

# 基本设置
basic_settings:
  auto_reconnect: true          # 自动重连
  max_reconnect_attempts: 5     # 最大重连次数
  reconnect_interval: 3         # 重连间隔（秒）

# 教师广播流设置
teacher_broadcast:
  stream_key: "desktop"          # 教师屏幕广播流键（与screen_broadcast.py一致）
  protocol: "rtsp"               # 使用RTSP协议
  default_port: 8554             # 默认端口
  manual_ip: ""                  # 手动设置教师IP（留空则自动发现）
  
# 设备发现设置
device_discovery:
  enabled: true                  # 启用设备发现
  udp_port: 8888                # UDP广播端口
  discovery_timeout: 5          # 发现超时（秒）
  cache_timeout: 300            # 缓存超时（秒）
  network_scan_enabled: true    # 启用网络扫描
  scan_timeout: 15              # 扫描超时（秒）
  periodic_refresh: true        # 启用定期刷新
  refresh_interval: 30          # 刷新间隔（秒）
  debug_mode: true              # 启用调试模式
  
# 网络质量监控
network_monitoring:
  enabled: true                 # 启用网络监控
  ping_interval: 10            # ping间隔（秒）
  quality_thresholds:          # 质量阈值（毫秒）
    excellent: 50
    good: 100
    fair: 200

# VLC设置 - 低延迟优化
vlc_settings:
  # 极低延迟缓存设置（毫秒）
  network_caching: 50          # 网络缓存50ms
  rtsp_caching: 50            # RTSP缓存50ms
  live_caching: 50            # 直播缓存50ms
  tcp_caching: 50             # TCP缓存50ms
  
  # 播放选项
  no_audio: true              # 禁用音频
  no_video_title_show: true   # 不显示视频标题
  quiet_mode: true            # 安静模式
  rtsp_tcp: true             # 强制RTSP使用TCP
  
  # 解码优化
  hardware_decoding: true     # 启用硬件解码
  fast_decode: true          # 快速解码
  skip_loop_filter: true     # 跳过循环滤波器
  hurry_up: true            # 加速解码
  
  # 时钟同步
  clock_jitter: 0           # 禁用时钟抖动
  clock_synchro: 0          # 禁用时钟同步
  
  # 其他优化选项
  keep_output: true         # 保持输出
  no_stats: true           # 不显示统计信息
  no_overlay: true         # 禁用覆盖
  no_video_deco: true      # 禁用视频装饰

# 显示设置
display:
  background_color: "#000000"    # 背景颜色
  show_connection_status: true   # 显示连接状态

# 界面设置
ui:
  window_title: "接收教师广播"
  default_size: [800, 600]
  min_size: [640, 480]