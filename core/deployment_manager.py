"""
部署管理器
开发应用配置管理系统，实现一键部署和更新机制，创建用户使用文档和帮助系统
"""
import sys
import os
import json
import yaml
import shutil
import subprocess
import zipfile
import hashlib
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
from resource_manager import path_manager

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = None):
        # 使用统一的路径管理器
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            self.config_dir = Path(path_manager.config_dir)
        self.config_dir.mkdir(exist_ok=True)

        self.app_config_file = Path(path_manager.get_config_path("app_config.yaml"))
        self.user_config_file = Path(path_manager.get_config_path("user_config.yaml"))
        self.module_config_dir = Path(path_manager.get_config_path("modules"))
        self.module_config_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_app_config = {
            "app": {
                "name": "智慧课堂教学工具",
                "version": "1.0.0",
                "author": "智慧课堂团队",
                "description": "面向教师的智慧课堂教学工具集合"
            },
            "system": {
                "log_level": "INFO",
                "log_file": "logs/app.log",
                "max_log_size": "10MB",
                "backup_count": 5,
                "auto_save_interval": 300,  # 秒
                "check_update_interval": 86400  # 秒
            },
            "network": {
                "udp_port": 8888,
                "tcp_port": 8889,
                "http_port": 8890,
                "websocket_port": 8891,
                "timeout": 10,
                "retry_count": 3
            },
            "ui": {
                "theme": "default",
                "language": "zh_CN",
                "font_size": 12,
                "window_state": {},
                "responsive_enabled": True,
                "animation_enabled": True,
                "accessibility_enabled": False
            },
            "modules": {
                "auto_load": True,
                "load_order": [
                    "udp_discovery",
                    "device_dashboard",
                    "random_picker",
                    "whiteboard_collaboration",
                    "device_control",
                    "dual_screen",
                    "virtual_keyboard",
                    "group_screen_monitor"
                ]
            }
        }
        
        self.default_user_config = {
            "user": {
                "name": "",
                "role": "teacher",
                "preferences": {
                    "auto_login": False,
                    "remember_password": False,
                    "startup_modules": [],
                    "workspace_layout": "default"
                }
            },
            "recent": {
                "courses": [],
                "students": [],
                "files": []
            }
        }
    
    def load_app_config(self) -> Dict[str, Any]:
        """加载应用配置"""
        try:
            if self.app_config_file.exists():
                with open(self.app_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    # 合并默认配置
                    return self._merge_config(self.default_app_config, config)
            else:
                # 创建默认配置文件
                self.save_app_config(self.default_app_config)
                return self.default_app_config.copy()
        except Exception as e:
            self.logger.error(f"加载应用配置失败: {e}")
            return self.default_app_config.copy()
    
    def save_app_config(self, config: Dict[str, Any]):
        """保存应用配置"""
        try:
            with open(self.app_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info("应用配置已保存")
        except Exception as e:
            self.logger.error(f"保存应用配置失败: {e}")
    
    def load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        try:
            if self.user_config_file.exists():
                with open(self.user_config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                    return self._merge_config(self.default_user_config, config)
            else:
                self.save_user_config(self.default_user_config)
                return self.default_user_config.copy()
        except Exception as e:
            self.logger.error(f"加载用户配置失败: {e}")
            return self.default_user_config.copy()
    
    def save_user_config(self, config: Dict[str, Any]):
        """保存用户配置"""
        try:
            with open(self.user_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info("用户配置已保存")
        except Exception as e:
            self.logger.error(f"保存用户配置失败: {e}")
    
    def load_module_config(self, module_name: str) -> Dict[str, Any]:
        """加载模块配置"""
        try:
            config_file = self.module_config_dir / f"{module_name}.yaml"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            return {}
        except Exception as e:
            self.logger.error(f"加载模块配置失败 {module_name}: {e}")
            return {}
    
    def save_module_config(self, module_name: str, config: Dict[str, Any]):
        """保存模块配置"""
        try:
            config_file = self.module_config_dir / f"{module_name}.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            self.logger.info(f"模块配置已保存: {module_name}")
        except Exception as e:
            self.logger.error(f"保存模块配置失败 {module_name}: {e}")
    
    def _merge_config(self, default: Dict[str, Any], user: Dict[str, Any]) -> Dict[str, Any]:
        """合并配置"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        try:
            self.save_app_config(self.default_app_config)
            self.save_user_config(self.default_user_config)
            
            # 清理模块配置
            for config_file in self.module_config_dir.glob("*.yaml"):
                config_file.unlink()
            
            self.logger.info("配置已重置为默认值")
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
    
    def export_config(self, export_path: str) -> bool:
        """导出配置"""
        try:
            export_dir = Path(export_path)
            export_dir.mkdir(exist_ok=True)
            
            # 复制配置文件
            if self.app_config_file.exists():
                shutil.copy2(self.app_config_file, export_dir)
            if self.user_config_file.exists():
                shutil.copy2(self.user_config_file, export_dir)
            
            # 复制模块配置
            module_export_dir = export_dir / "modules"
            if self.module_config_dir.exists():
                shutil.copytree(self.module_config_dir, module_export_dir, dirs_exist_ok=True)
            
            self.logger.info(f"配置已导出到: {export_path}")
            return True
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, import_path: str) -> bool:
        """导入配置"""
        try:
            import_dir = Path(import_path)
            if not import_dir.exists():
                return False
            
            # 备份当前配置
            backup_dir = self.config_dir / f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_dir.mkdir(exist_ok=True)
            
            if self.app_config_file.exists():
                shutil.copy2(self.app_config_file, backup_dir)
            if self.user_config_file.exists():
                shutil.copy2(self.user_config_file, backup_dir)
            if self.module_config_dir.exists():
                shutil.copytree(self.module_config_dir, backup_dir / "modules", dirs_exist_ok=True)
            
            # 导入新配置
            app_config_import = import_dir / "app_config.yaml"
            if app_config_import.exists():
                shutil.copy2(app_config_import, self.app_config_file)
            
            user_config_import = import_dir / "user_config.yaml"
            if user_config_import.exists():
                shutil.copy2(user_config_import, self.user_config_file)
            
            module_import_dir = import_dir / "modules"
            if module_import_dir.exists():
                shutil.copytree(module_import_dir, self.module_config_dir, dirs_exist_ok=True)
            
            self.logger.info(f"配置已从 {import_path} 导入")
            return True
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False

class UpdateManager:
    """更新管理器"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # 更新服务器配置
        self.update_server_url = "https://api.example.com/updates"
        self.current_version = "1.0.0"
        self.update_check_file = Path("last_update_check.json")
    
    def check_for_updates(self) -> Optional[Dict[str, Any]]:
        """检查更新"""
        try:
            # 检查是否需要检查更新
            if not self._should_check_update():
                return None
            
            # 请求更新信息
            response = requests.get(
                f"{self.update_server_url}/check",
                params={"version": self.current_version},
                timeout=10
            )
            
            if response.status_code == 200:
                update_info = response.json()
                
                # 记录检查时间
                self._record_update_check()
                
                if update_info.get("has_update", False):
                    self.logger.info(f"发现新版本: {update_info.get('latest_version')}")
                    return update_info
                else:
                    self.logger.info("当前已是最新版本")
                    return None
            else:
                self.logger.error(f"检查更新失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.error(f"检查更新失败: {e}")
            return None
    
    def download_update(self, update_info: Dict[str, Any], download_path: str) -> bool:
        """下载更新"""
        try:
            download_url = update_info.get("download_url")
            if not download_url:
                return False
            
            # 下载更新包
            response = requests.get(download_url, stream=True, timeout=30)
            response.raise_for_status()
            
            download_file = Path(download_path)
            download_file.parent.mkdir(exist_ok=True)
            
            with open(download_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # 验证文件完整性
            if self._verify_file_integrity(download_file, update_info.get("checksum")):
                self.logger.info(f"更新包下载完成: {download_path}")
                return True
            else:
                self.logger.error("更新包校验失败")
                download_file.unlink(missing_ok=True)
                return False
                
        except Exception as e:
            self.logger.error(f"下载更新失败: {e}")
            return False
    
    def install_update(self, update_package_path: str) -> bool:
        """安装更新"""
        try:
            update_file = Path(update_package_path)
            if not update_file.exists():
                return False
            
            # 创建备份
            backup_dir = Path(f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            self._create_backup(backup_dir)
            
            # 解压更新包
            with zipfile.ZipFile(update_file, 'r') as zip_ref:
                zip_ref.extractall("temp_update")
            
            # 应用更新
            temp_update_dir = Path("temp_update")
            self._apply_update(temp_update_dir)
            
            # 清理临时文件
            shutil.rmtree(temp_update_dir, ignore_errors=True)
            update_file.unlink(missing_ok=True)
            
            self.logger.info("更新安装完成")
            return True
            
        except Exception as e:
            self.logger.error(f"安装更新失败: {e}")
            return False
    
    def _should_check_update(self) -> bool:
        """检查是否应该检查更新"""
        try:
            if not self.update_check_file.exists():
                return True
            
            with open(self.update_check_file, 'r') as f:
                check_info = json.load(f)
            
            last_check = datetime.fromisoformat(check_info.get("last_check", "1970-01-01"))
            check_interval = self.config_manager.load_app_config().get("system", {}).get("check_update_interval", 86400)
            
            return (datetime.now() - last_check).total_seconds() > check_interval
            
        except Exception:
            return True
    
    def _record_update_check(self):
        """记录更新检查时间"""
        try:
            check_info = {"last_check": datetime.now().isoformat()}
            with open(self.update_check_file, 'w') as f:
                json.dump(check_info, f)
        except Exception as e:
            self.logger.error(f"记录更新检查时间失败: {e}")
    
    def _verify_file_integrity(self, file_path: Path, expected_checksum: str) -> bool:
        """验证文件完整性"""
        if not expected_checksum:
            return True
        
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest() == expected_checksum
        except Exception:
            return False
    
    def _create_backup(self, backup_dir: Path):
        """创建备份"""
        try:
            backup_dir.mkdir(exist_ok=True)
            
            # 备份主要文件
            important_files = [
                "main.py",
                "module_manager.py",
                "auth_manager.py",
                "communication_manager.py"
            ]
            
            for file_name in important_files:
                file_path = Path(file_name)
                if file_path.exists():
                    shutil.copy2(file_path, backup_dir)
            
            # 备份模块目录
            modules_dir = Path("modules")
            if modules_dir.exists():
                shutil.copytree(modules_dir, backup_dir / "modules", dirs_exist_ok=True)
            
            # 备份配置
            self.config_manager.export_config(str(backup_dir / "config"))
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
    
    def _apply_update(self, update_dir: Path):
        """应用更新"""
        try:
            # 复制新文件
            for item in update_dir.rglob("*"):
                if item.is_file():
                    relative_path = item.relative_to(update_dir)
                    target_path = Path(relative_path)
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    shutil.copy2(item, target_path)
            
        except Exception as e:
            self.logger.error(f"应用更新失败: {e}")
            raise

class DeploymentManager:
    """部署管理器"""
    
    def __init__(self):
        self.config_manager = ConfigManager()
        self.update_manager = UpdateManager(self.config_manager)
        self.logger = logging.getLogger(__name__)
        
        # MediaMTX安装器
        try:
            from .mediamtx_installer import mediamtx_installer
            self.mediamtx_installer = mediamtx_installer
        except ImportError:
            self.mediamtx_installer = None
            self.logger.warning("MediaMTX安装器不可用")
    
    def create_deployment_package(self, output_path: str) -> bool:
        """创建部署包"""
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(output_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加主要文件
                main_files = [
                    "main.py",
                    "module_manager.py",
                    "auth_manager.py",
                    "communication_manager.py",
                    "performance_optimizer.py",
                    "ui_optimizer.py",
                    "deployment_manager.py"
                ]
                
                for file_name in main_files:
                    file_path = Path(file_name)
                    if file_path.exists():
                        zipf.write(file_path, file_name)
                
                # 添加模块目录
                modules_dir = Path("modules")
                if modules_dir.exists():
                    for file_path in modules_dir.rglob("*"):
                        if file_path.is_file():
                            zipf.write(file_path, file_path)
                
                # 添加资源文件
                resources_dir = Path("resources")
                if resources_dir.exists():
                    for file_path in resources_dir.rglob("*"):
                        if file_path.is_file():
                            zipf.write(file_path, file_path)
                
                # 添加配置模板
                config_template = {
                    "deployment": {
                        "version": "1.0.0",
                        "created": datetime.now().isoformat(),
                        "platform": sys.platform
                    }
                }
                
                zipf.writestr("deployment_info.json", json.dumps(config_template, indent=2))
            
            self.logger.info(f"部署包已创建: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建部署包失败: {e}")
            return False
    
    def install_from_package(self, package_path: str, install_dir: str) -> bool:
        """从包安装"""
        try:
            package_file = Path(package_path)
            install_path = Path(install_dir)
            
            if not package_file.exists():
                return False
            
            # 创建安装目录
            install_path.mkdir(parents=True, exist_ok=True)
            
            # 解压安装包
            with zipfile.ZipFile(package_file, 'r') as zipf:
                zipf.extractall(install_path)
            
            # 初始化配置
            config_manager = ConfigManager(str(install_path / "config"))
            config_manager.load_app_config()  # 这会创建默认配置
            
            # 安装MediaMTX依赖
            if self.mediamtx_installer:
                self.logger.info("开始安装MediaMTX...")
                if self.mediamtx_installer.install_mediamtx():
                    self.logger.info("MediaMTX安装成功")
                else:
                    self.logger.warning("MediaMTX安装失败，某些功能可能不可用")
            
            self.logger.info(f"应用已安装到: {install_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"安装失败: {e}")
            return False
    
    def get_deployment_info(self) -> Dict[str, Any]:
        """获取部署信息"""
        try:
            app_config = self.config_manager.load_app_config()
            
            return {
                "app_info": app_config.get("app", {}),
                "system_info": {
                    "platform": sys.platform,
                    "python_version": sys.version,
                    "install_path": os.getcwd()
                },
                "config_status": {
                    "app_config_exists": self.config_manager.app_config_file.exists(),
                    "user_config_exists": self.config_manager.user_config_file.exists(),
                    "module_configs": len(list(self.config_manager.module_config_dir.glob("*.yaml")))
                }
            }
        except Exception as e:
            self.logger.error(f"获取部署信息失败: {e}")
            return {}
