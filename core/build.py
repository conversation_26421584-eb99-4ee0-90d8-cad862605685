#!/usr/bin/env python3
"""
智慧课堂教学工具打包脚本
使用PyInstaller将应用程序打包为可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_pyinstaller():
    """检查是否安装了PyInstaller"""
    try:
        subprocess.run([sys.executable, '-m', 'PyInstaller', '--version'], 
                      capture_output=True, check=True)
        print("PyInstaller已安装")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("PyInstaller未安装，请先安装PyInstaller:")
        print("pip install pyinstaller")
        return False

def build_application():
    """构建应用程序"""
    # 获取当前目录
    core_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(core_dir)
    
    print(f"当前工作目录: {core_dir}")
    
    # 检查spec文件
    spec_file = os.path.join(core_dir, 'build_app.spec')
    if not os.path.exists(spec_file):
        print("错误: 找不到build_app.spec文件")
        return False
    
    # 使用PyInstaller打包
    print("开始打包应用程序...")
    try:
        # 使用spec文件构建
        cmd = [sys.executable, '-m', 'PyInstaller', 'build_app.spec']
        result = subprocess.run(cmd, capture_output=False, text=True)
        
        if result.returncode == 0:
            print("打包成功完成!")
            print(f"可执行文件位置: {os.path.join(core_dir, 'dist', 'smart_classroom')}")

            # 手动复制VLC库文件
            print("\n正在复制VLC库文件...")
            try:
                dist_dir = os.path.join(core_dir, 'dist')

                vlc_libs = [
                    ('/usr/lib/x86_64-linux-gnu/libvlc.so.5.6.0', 'libvlc.so.5.6.0'),
                    ('/usr/lib/x86_64-linux-gnu/libvlc.so.5.6.0', 'libvlc.so.5'),
                    ('/usr/lib/x86_64-linux-gnu/libvlc.so.5.6.0', 'libvlc.so'),
                    ('/usr/lib/x86_64-linux-gnu/libvlccore.so.9.0.0', 'libvlccore.so.9.0.0'),
                    ('/usr/lib/x86_64-linux-gnu/libvlccore.so.9.0.0', 'libvlccore.so.9'),
                    ('/usr/lib/x86_64-linux-gnu/libvlccore.so.9.0.0', 'libvlccore.so'),
                ]

                for src_path, dst_name in vlc_libs:
                    if os.path.exists(src_path):
                        dst_path = os.path.join(dist_dir, dst_name)
                        if not os.path.exists(dst_path):
                            shutil.copy2(src_path, dst_path)
                            print(f"复制VLC库文件: {dst_name}")
                        else:
                            print(f"VLC库文件已存在: {dst_name}")
                    else:
                        print(f"VLC库文件不存在: {src_path}")

                print("VLC库文件复制完成!")

            except Exception as e:
                print(f"复制VLC库文件失败: {e}")

            return True
        else:
            print("打包失败!")
            return False
            
    except Exception as e:
        print(f"打包过程中出现错误: {e}")
        return False

def clean_build():
    """清理构建文件"""
    core_dir = os.path.dirname(os.path.abspath(__file__))
    build_dir = os.path.join(core_dir, 'build')
    dist_dir = os.path.join(core_dir, 'dist')
    
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
        print(f"已删除 {build_dir}")
    
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
        print(f"已删除 {dist_dir}")

def main():
    """主函数"""
    print("=" * 50)
    print("智慧课堂教学工具打包脚本")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        print("清理构建文件...")
        clean_build()
        return
    
    # 检查PyInstaller
    if not check_pyinstaller():
        return
    
    # 构建应用程序
    if build_application():
        print("\n打包完成!")
        print("可执行文件位于 dist/smart_classroom 目录中")
    else:
        print("\n打包失败!")
        sys.exit(1)

if __name__ == '__main__':
    main()