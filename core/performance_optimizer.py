"""
性能优化器
优化UDP设备发现的网络性能，改进屏幕广播的延迟和画质，优化白板协作的同步性能
"""
import sys
import os
import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
import logging

class PerformanceMetrics:
    """性能指标类"""
    
    def __init__(self):
        self.cpu_usage_history: List[float] = []
        self.memory_usage_history: List[float] = []
        self.network_io_history: List[Dict[str, int]] = []
        self.module_performance: Dict[str, Dict[str, Any]] = {}
        self.start_time = datetime.now()
        self.last_update = datetime.now()
    
    def update_system_metrics(self):
        """更新系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.cpu_usage_history.append(cpu_percent)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_usage_history.append(memory.percent)
            
            # 网络IO
            net_io = psutil.net_io_counters()
            self.network_io_history.append({
                'bytes_sent': net_io.bytes_sent,
                'bytes_recv': net_io.bytes_recv,
                'packets_sent': net_io.packets_sent,
                'packets_recv': net_io.packets_recv
            })
            
            # 保持历史记录在合理范围内
            max_history = 100
            if len(self.cpu_usage_history) > max_history:
                self.cpu_usage_history = self.cpu_usage_history[-max_history:]
            if len(self.memory_usage_history) > max_history:
                self.memory_usage_history = self.memory_usage_history[-max_history:]
            if len(self.network_io_history) > max_history:
                self.network_io_history = self.network_io_history[-max_history:]
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logging.error(f"更新系统指标失败: {e}")
    
    def get_average_cpu_usage(self, minutes: int = 5) -> float:
        """获取平均CPU使用率"""
        if not self.cpu_usage_history:
            return 0.0
        
        # 计算最近几分钟的平均值
        recent_samples = min(len(self.cpu_usage_history), minutes * 6)  # 假设每10秒采样一次
        return sum(self.cpu_usage_history[-recent_samples:]) / recent_samples
    
    def get_average_memory_usage(self, minutes: int = 5) -> float:
        """获取平均内存使用率"""
        if not self.memory_usage_history:
            return 0.0
        
        recent_samples = min(len(self.memory_usage_history), minutes * 6)
        return sum(self.memory_usage_history[-recent_samples:]) / recent_samples
    
    def get_network_throughput(self) -> Dict[str, float]:
        """获取网络吞吐量"""
        if len(self.network_io_history) < 2:
            return {'upload': 0.0, 'download': 0.0}
        
        current = self.network_io_history[-1]
        previous = self.network_io_history[-2]
        
        # 计算每秒字节数
        time_diff = 10  # 假设采样间隔为10秒
        upload_rate = (current['bytes_sent'] - previous['bytes_sent']) / time_diff
        download_rate = (current['bytes_recv'] - previous['bytes_recv']) / time_diff
        
        return {
            'upload': upload_rate / 1024,  # KB/s
            'download': download_rate / 1024  # KB/s
        }

class NetworkOptimizer:
    """网络优化器"""
    
    def __init__(self):
        self.udp_buffer_size = 65536  # 64KB
        self.tcp_buffer_size = 131072  # 128KB
        self.discovery_interval = 5.0  # 设备发现间隔（秒）
        self.heartbeat_interval = 30.0  # 心跳间隔（秒）
        self.connection_timeout = 10.0  # 连接超时（秒）
        self.max_concurrent_connections = 50
    
    def optimize_udp_discovery(self, udp_module) -> Dict[str, Any]:
        """优化UDP设备发现"""
        optimizations = {}
        
        try:
            # 动态调整发现间隔
            device_count = len(udp_module.discovered_devices) if hasattr(udp_module, 'discovered_devices') else 0
            
            if device_count < 5:
                # 设备较少时，增加发现频率
                new_interval = 3.0
            elif device_count < 20:
                # 中等数量设备，标准频率
                new_interval = 5.0
            else:
                # 设备较多时，降低发现频率
                new_interval = 10.0
            
            if hasattr(udp_module, 'discovery_interval'):
                udp_module.discovery_interval = new_interval
                optimizations['discovery_interval'] = new_interval
            
            # 优化UDP缓冲区大小
            if hasattr(udp_module, 'socket') and udp_module.socket:
                try:
                    udp_module.socket.setsockopt(socket.SOL_SOCKET, socket.SO_RCVBUF, self.udp_buffer_size)
                    udp_module.socket.setsockopt(socket.SOL_SOCKET, socket.SO_SNDBUF, self.udp_buffer_size)
                    optimizations['buffer_size'] = self.udp_buffer_size
                except Exception as e:
                    logging.warning(f"设置UDP缓冲区大小失败: {e}")
            
            # 优化心跳间隔
            if hasattr(udp_module, 'heartbeat_interval'):
                # 根据网络质量调整心跳间隔
                udp_module.heartbeat_interval = self.heartbeat_interval
                optimizations['heartbeat_interval'] = self.heartbeat_interval
            
        except Exception as e:
            logging.error(f"UDP发现优化失败: {e}")
        
        return optimizations
    
    def optimize_screen_broadcast(self, screen_module) -> Dict[str, Any]:
        """优化屏幕广播"""
        optimizations = {}
        
        try:
            # 动态调整视频质量
            if hasattr(screen_module, 'config'):
                current_quality = screen_module.get_config('video_quality', 'medium')
                
                # 根据网络状况调整质量
                metrics = PerformanceMetrics()
                metrics.update_system_metrics()
                cpu_usage = metrics.get_average_cpu_usage(1)
                
                if cpu_usage > 80:
                    # CPU使用率高，降低质量
                    new_quality = 'low'
                    new_fps = 15
                    new_bitrate = 1000  # kbps
                elif cpu_usage < 30:
                    # CPU使用率低，可以提高质量
                    new_quality = 'high'
                    new_fps = 30
                    new_bitrate = 3000  # kbps
                else:
                    # 中等使用率，标准质量
                    new_quality = 'medium'
                    new_fps = 24
                    new_bitrate = 2000  # kbps
                
                screen_module.set_config('video_quality', new_quality)
                screen_module.set_config('fps', new_fps)
                screen_module.set_config('bitrate', new_bitrate)
                
                optimizations.update({
                    'video_quality': new_quality,
                    'fps': new_fps,
                    'bitrate': new_bitrate
                })
            
            # 优化编码参数
            if hasattr(screen_module, 'encoder_settings'):
                screen_module.encoder_settings.update({
                    'preset': 'ultrafast',  # 快速编码
                    'tune': 'zerolatency',  # 零延迟调优
                    'profile': 'baseline'   # 基线配置文件
                })
                optimizations['encoder_optimized'] = True
            
        except Exception as e:
            logging.error(f"屏幕广播优化失败: {e}")
        
        return optimizations
    
    def optimize_whiteboard_sync(self, whiteboard_module) -> Dict[str, Any]:
        """优化白板同步"""
        optimizations = {}
        
        try:
            # 批量处理同步事件
            if hasattr(whiteboard_module, 'sync_batch_size'):
                whiteboard_module.sync_batch_size = 10  # 批量处理10个事件
                optimizations['sync_batch_size'] = 10
            
            # 优化同步间隔
            if hasattr(whiteboard_module, 'sync_interval'):
                whiteboard_module.sync_interval = 100  # 100ms同步一次
                optimizations['sync_interval'] = 100
            
            # 启用增量同步
            if hasattr(whiteboard_module, 'enable_incremental_sync'):
                whiteboard_module.enable_incremental_sync = True
                optimizations['incremental_sync'] = True
            
            # 压缩同步数据
            if hasattr(whiteboard_module, 'enable_compression'):
                whiteboard_module.enable_compression = True
                optimizations['compression_enabled'] = True
            
        except Exception as e:
            logging.error(f"白板同步优化失败: {e}")
        
        return optimizations

class MemoryOptimizer:
    """内存优化器"""
    
    def __init__(self):
        self.gc_threshold = 80.0  # 内存使用率阈值
        self.cache_size_limit = 100 * 1024 * 1024  # 100MB缓存限制
    
    def optimize_memory_usage(self) -> Dict[str, Any]:
        """优化内存使用"""
        optimizations = {}
        
        try:
            # 获取当前内存使用情况
            memory = psutil.virtual_memory()
            current_usage = memory.percent
            
            optimizations['memory_usage_before'] = current_usage
            
            # 如果内存使用率过高，执行清理
            if current_usage > self.gc_threshold:
                # 强制垃圾回收
                collected = gc.collect()
                optimizations['objects_collected'] = collected
                
                # 清理模块缓存
                self._clear_module_caches()
                optimizations['caches_cleared'] = True
                
                # 再次检查内存使用情况
                memory_after = psutil.virtual_memory()
                optimizations['memory_usage_after'] = memory_after.percent
                optimizations['memory_freed'] = current_usage - memory_after.percent
            
        except Exception as e:
            logging.error(f"内存优化失败: {e}")
        
        return optimizations
    
    def _clear_module_caches(self):
        """清理模块缓存"""
        try:
            # 清理图像缓存
            import sys
            for module_name in list(sys.modules.keys()):
                if hasattr(sys.modules[module_name], 'clear_cache'):
                    sys.modules[module_name].clear_cache()
            
            # 清理Qt缓存
            try:
                from PyQt5.QtGui import QPixmapCache
                QPixmapCache.clear()
            except ImportError:
                pass
            
        except Exception as e:
            logging.error(f"清理模块缓存失败: {e}")

class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, module_manager):
        self.module_manager = module_manager
        self.metrics = PerformanceMetrics()
        self.network_optimizer = NetworkOptimizer()
        self.memory_optimizer = MemoryOptimizer()
        
        self.optimization_thread = None
        self.is_optimizing = False
        self.optimization_interval = 30.0  # 30秒优化一次
        
        self.logger = logging.getLogger(__name__)
    
    def start_optimization(self):
        """开始性能优化"""
        if self.is_optimizing:
            return
        
        self.is_optimizing = True
        self.optimization_thread = threading.Thread(target=self._optimization_loop, daemon=True)
        self.optimization_thread.start()
        
        self.logger.info("性能优化器已启动")
    
    def stop_optimization(self):
        """停止性能优化"""
        self.is_optimizing = False
        if self.optimization_thread and self.optimization_thread.is_alive():
            self.optimization_thread.join(timeout=5)
        
        self.logger.info("性能优化器已停止")
    
    def _optimization_loop(self):
        """优化循环"""
        while self.is_optimizing:
            try:
                # 更新性能指标
                self.metrics.update_system_metrics()
                
                # 执行优化
                self._perform_optimizations()
                
                # 等待下次优化
                time.sleep(self.optimization_interval)
                
            except Exception as e:
                self.logger.error(f"优化循环出错: {e}")
                time.sleep(5)  # 出错时短暂等待
    
    def _perform_optimizations(self):
        """执行优化"""
        optimization_results = {}
        
        try:
            # 网络优化
            udp_module = self.module_manager.get_module_instance("udp_discovery")
            if udp_module:
                udp_opts = self.network_optimizer.optimize_udp_discovery(udp_module)
                optimization_results['udp_discovery'] = udp_opts
            
            screen_module = self.module_manager.get_module_instance("group_screen_monitor")
            if screen_module:
                screen_opts = self.network_optimizer.optimize_screen_broadcast(screen_module)
                optimization_results['screen_broadcast'] = screen_opts
            
            whiteboard_module = self.module_manager.get_module_instance("whiteboard_collaboration")
            if whiteboard_module:
                wb_opts = self.network_optimizer.optimize_whiteboard_sync(whiteboard_module)
                optimization_results['whiteboard_sync'] = wb_opts
            
            # 内存优化
            memory_opts = self.memory_optimizer.optimize_memory_usage()
            optimization_results['memory'] = memory_opts
            
            # 记录优化结果
            if optimization_results:
                self.logger.info(f"性能优化完成: {optimization_results}")
            
        except Exception as e:
            self.logger.error(f"执行优化失败: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            self.metrics.update_system_metrics()
            
            return {
                'system_metrics': {
                    'cpu_usage': self.metrics.get_average_cpu_usage(),
                    'memory_usage': self.metrics.get_average_memory_usage(),
                    'network_throughput': self.metrics.get_network_throughput(),
                    'uptime': str(datetime.now() - self.metrics.start_time)
                },
                'module_performance': self.metrics.module_performance,
                'optimization_status': {
                    'is_optimizing': self.is_optimizing,
                    'last_update': self.metrics.last_update.isoformat(),
                    'optimization_interval': self.optimization_interval
                }
            }
        except Exception as e:
            self.logger.error(f"获取性能报告失败: {e}")
            return {}
    
    def force_optimization(self) -> Dict[str, Any]:
        """强制执行优化"""
        try:
            self._perform_optimizations()
            return {"status": "success", "message": "强制优化已执行"}
        except Exception as e:
            return {"status": "error", "message": str(e)}
