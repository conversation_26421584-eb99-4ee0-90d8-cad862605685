#!/usr/bin/env python3
"""
VLC构建助手
用于在打包时动态检测和添加VLC库文件
"""
import os
import sys
import glob
import platform
import subprocess
from pathlib import Path

def find_vlc_libraries():
    """查找VLC库文件"""
    binaries = []
    system = platform.system()
    
    if system == "Linux":
        # 常见的VLC库文件位置
        lib_paths = [
            '/usr/lib/x86_64-linux-gnu',
            '/usr/lib64',
            '/usr/lib',
            '/lib/x86_64-linux-gnu',
            '/lib64',
            '/lib'
        ]
        
        # 查找libvlc和libvlccore
        for lib_path in lib_paths:
            if os.path.exists(lib_path):
                # 查找libvlc
                vlc_libs = glob.glob(os.path.join(lib_path, 'libvlc.so*'))
                vlccore_libs = glob.glob(os.path.join(lib_path, 'libvlccore.so*'))
                
                for lib in vlc_libs + vlccore_libs:
                    if os.path.isfile(lib) and not os.path.islink(lib):
                        binaries.append((lib, '.'))
                        print(f"找到VLC库文件: {lib}")
                
                # 只从第一个找到库文件的路径添加
                if vlc_libs or vlccore_libs:
                    break
        
        # 查找VLC插件目录
        plugin_paths = [
            '/usr/lib/vlc/plugins',
            '/usr/lib64/vlc/plugins',
            '/usr/local/lib/vlc/plugins'
        ]
        
        for plugin_path in plugin_paths:
            if os.path.exists(plugin_path):
                binaries.append((plugin_path, 'vlc/plugins'))
                print(f"找到VLC插件目录: {plugin_path}")
                break
    
    elif system == "Windows":
        # Windows VLC路径
        vlc_paths = [
            r"C:\Program Files\VideoLAN\VLC",
            r"C:\Program Files (x86)\VideoLAN\VLC"
        ]
        
        for vlc_path in vlc_paths:
            if os.path.exists(vlc_path):
                # 添加主要DLL文件
                dll_files = ['libvlc.dll', 'libvlccore.dll']
                for dll in dll_files:
                    dll_path = os.path.join(vlc_path, dll)
                    if os.path.exists(dll_path):
                        binaries.append((dll_path, '.'))
                        print(f"找到VLC库文件: {dll_path}")
                
                # 添加插件目录
                plugins_path = os.path.join(vlc_path, 'plugins')
                if os.path.exists(plugins_path):
                    binaries.append((plugins_path, 'vlc/plugins'))
                    print(f"找到VLC插件目录: {plugins_path}")
                
                break
    
    return binaries

def check_vlc_installation():
    """检查VLC是否安装"""
    try:
        import vlc
        print("python-vlc模块已安装")
        return True
    except ImportError:
        print("警告: python-vlc模块未安装")
        return False

def generate_vlc_spec_additions():
    """生成VLC相关的spec文件添加内容"""
    if not check_vlc_installation():
        print("跳过VLC库文件检测")
        return []
    
    binaries = find_vlc_libraries()
    
    if not binaries:
        print("警告: 未找到VLC库文件，广播播放功能可能无法正常工作")
    else:
        print(f"找到 {len(binaries)} 个VLC相关文件")
    
    return binaries

if __name__ == "__main__":
    print("VLC构建助手")
    print("=" * 40)
    
    binaries = generate_vlc_spec_additions()
    
    if binaries:
        print("\n建议添加到spec文件的binaries部分:")
        for src, dst in binaries:
            print(f"    ('{src}', '{dst}'),")
    else:
        print("\n未找到VLC库文件")
    
    print("\n完成")
