import hashlib
import sys

def generate_registration_code(hardware_fingerprint):
    """
    根据硬件指纹生成注册码。
    """
    # 确保输入是指纹的大写形式
    fingerprint = hardware_fingerprint.strip().upper()
    
    # 使用 MD5 哈希算法
    hash_value = hashlib.md5(fingerprint.encode()).hexdigest()
    
    # 格式化哈希值，前25个字符，每5个字符加一个连字符
    code = "-".join([hash_value[i:i+5].upper() for i in range(0, 25, 5)])
    
    return code

def main():
    """
    主函数，用于交互式生成注册码。
    """
    print("--- 注册码生成器 (开发者工具) ---")
    
    # 从命令行参数获取指纹，或者提示用户输入
    if len(sys.argv) > 1:
        hardware_fingerprint = sys.argv[1]
        print(f"从命令行参数获取硬件指纹: {hardware_fingerprint}")
    else:
        hardware_fingerprint = input("请输入从用户端获取的硬件指纹: ")

    if not hardware_fingerprint:
        print("错误：硬件指纹不能为空。")
        return

    # 生成注册码
    registration_code = generate_registration_code(hardware_fingerprint)

    # 显示结果
    print("\n-----------------------------------------")
    print(f"硬件指纹: {hardware_fingerprint.strip().upper()}")
    print(f"生成的注册码: {registration_code}")

if __name__ == "__main__":
    main()
