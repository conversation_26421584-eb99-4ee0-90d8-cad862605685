#!/usr/bin/env python3
"""
智慧课堂教学工具启动脚本
"""
import sys
import os
import logging
from pathlib import Path

def setup_logging():
    """设置日志"""
    try:
        from resource_manager import path_manager
        log_file = path_manager.get_log_path('smart_classroom.log')
    except:
        # 如果路径管理器还未初始化，使用临时路径
        log_file = '/tmp/smart_classroom.log'

    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    logging.info(f'Logging to {log_file}')
    logging.info(f'Current working directory: {os.getcwd()}')
    logging.info(f'Python executable: {sys.executable}')

def check_dependencies():
    """检查依赖"""
    required_modules = [
        'PyQt5',
        'yaml',
        'requests'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"缺少依赖模块: {', '.join(missing_modules)}")
        return False
    
    return True

def main():
    """主函数"""
    print("智慧课堂教学工具 v1.0.0")
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    # 初始化路径管理器并迁移旧文件
    try:
        from resource_manager import path_manager
        path_manager.migrate_old_files()
        logger.info("路径管理器初始化完成")
    except Exception as e:
        logger.error(f"路径管理器初始化失败: {e}")
        print(f"路径管理器初始化失败: {e}")

    # 检查依赖
    # if not check_dependencies():
    #     return 1
    
    # # 检查必要文件
    # required_files = [
    #     "main_app.py",
    #     "auth_manager.py",
    #     "module_manager.py",
    #     "communication_manager.py"
    # ]
    
    # missing_files = []
    # for file_name in required_files:
    #     if not os.path.exists(file_name):
    #         missing_files.append(file_name)
    
    # if missing_files:
    #     logger.error(f"缺少必要文件: {', '.join(missing_files)}")
    #     return 1
    
    try:
        # 导入并运行主应用程序
        from main_app import main as run_main_app
        logger.info("启动智慧课堂教学工具...")
        return run_main_app()
        
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        return 0
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
