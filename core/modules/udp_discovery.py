"""
UDP设备发现模块
实现局域网内设备的自动发现和状态维护
"""
import socket
import json
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QListWidget, QListWidgetItem, QPushButton, QTextEdit, QGroupBox
from .base_module import CommonModule
import uuid
import logging
from getmac import get_mac_address

class DeviceInfo:
    """设备信息类"""
    
    def __init__(self, device_id: str, device_type: str, device_name: str, 
                 ip_address: str, port: int, capabilities: List[str], mac_address: str = None):
        self.device_id = device_id
        self.device_type = device_type  # teacher, group
        self.device_name = device_name
        self.ip_address = ip_address
        self.port = port
        self.capabilities = capabilities
        self.mac_address = mac_address
        self.last_seen = datetime.now()
        self.status = "online"  # online, offline
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "device_id": self.device_id,
            "device_type": self.device_type,
            "device_name": self.device_name,
            "ip_address": self.ip_address,
            "port": self.port,
            "capabilities": self.capabilities,
            "mac_address": self.mac_address,
            "last_seen": self.last_seen.isoformat(),
            "status": self.status
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeviceInfo':
        """从字典创建设备信息"""
        device = cls(
            data["device_id"],
            data["device_type"],
            data["device_name"],
            data["ip_address"],
            data["port"],
            data.get("capabilities", []),
            data.get("mac_address")
        )
        if "last_seen" in data:
            device.last_seen = datetime.fromisoformat(data["last_seen"])
        if "status" in data:
            device.status = data["status"]
        return device
    
    def update_last_seen(self):
        """更新最后见到时间"""
        self.last_seen = datetime.now()
        self.status = "online"
    
    def is_expired(self, timeout_seconds: int = 30) -> bool:
        """检查设备是否过期（离线）"""
        return datetime.now() - self.last_seen > timedelta(seconds=timeout_seconds)

class UDPDiscoveryProtocol:
    """UDP发现协议"""
    
    # 消息类型
    MSG_DISCOVERY = "device_discovery"
    MSG_HEARTBEAT = "device_heartbeat"
    MSG_GOODBYE = "device_goodbye"
    
    # 默认端口
    DEFAULT_PORT = 8888
    BROADCAST_ADDRESS = "***************"
    
    @staticmethod
    def create_discovery_message(device_info: DeviceInfo) -> bytes:
        """创建设备发现消息"""
        message = {
            "type": UDPDiscoveryProtocol.MSG_DISCOVERY,
            "device_id": device_info.device_id,
            "device_type": device_info.device_type,
            "device_name": device_info.device_name,
            "ip_address": device_info.ip_address,
            "port": device_info.port,
            "mac_address": device_info.mac_address,
            "capabilities": device_info.capabilities,
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(message).encode('utf-8')
    
    @staticmethod
    def create_heartbeat_message(device_info: DeviceInfo) -> bytes:
        """创建心跳消息"""
        message = {
            "type": UDPDiscoveryProtocol.MSG_HEARTBEAT,
            "device_id": device_info.device_id,
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(message).encode('utf-8')
    
    @staticmethod
    def create_goodbye_message(device_id: str) -> bytes:
        """创建离线消息"""
        message = {
            "type": UDPDiscoveryProtocol.MSG_GOODBYE,
            "device_id": device_id,
            "timestamp": datetime.now().isoformat()
        }
        return json.dumps(message).encode('utf-8')
    
    @staticmethod
    def parse_message(data: bytes) -> Optional[Dict[str, Any]]:
        """解析接收到的消息"""
        try:
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError) as e:
            logging.getLogger("udp_discovery").error(f"解析消息失败: {e}")
            return None

class UDPDiscoveryWindow(QWidget):
    """UDP设备发现界面"""
    
    def __init__(self, module):
        super().__init__()
        self.module = module
        self.init_ui()
        
        # 连接模块信号
        self.module.device_discovered.connect(self.on_device_discovered)
        self.module.device_updated.connect(self.on_device_updated)
        self.module.device_offline.connect(self.on_device_offline)
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设备发现")
        self.setGeometry(100, 100, 800, 600)
        
        layout = QVBoxLayout()
        
        # 控制按钮
        control_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始发现")
        self.stop_btn = QPushButton("停止发现")
        self.refresh_btn = QPushButton("刷新列表")
        
        self.start_btn.clicked.connect(self.start_discovery)
        self.stop_btn.clicked.connect(self.stop_discovery)
        self.refresh_btn.clicked.connect(self.refresh_device_list)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.refresh_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 设备列表
        device_group = QGroupBox("发现的设备")
        device_layout = QVBoxLayout()
        
        self.device_list = QListWidget()
        device_layout.addWidget(self.device_list)
        
        device_group.setLayout(device_layout)
        layout.addWidget(device_group)
        
        # 日志显示
        log_group = QGroupBox("发现日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        self.setLayout(layout)
        
        # 初始状态
        self.stop_btn.setEnabled(False)
    
    def start_discovery(self):
        """开始设备发现"""
        if self.module.start_discovery():
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.add_log("设备发现已启动")
        else:
            self.add_log("启动设备发现失败")
    
    def stop_discovery(self):
        """停止设备发现"""
        self.module.stop_discovery()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("设备发现已停止")
    
    def refresh_device_list(self):
        """刷新设备列表"""
        self.device_list.clear()
        devices = self.module.get_discovered_devices()
        
        for device in devices.values():
            item_text = f"{device.device_name} ({device.device_type}) - {device.ip_address}:{device.port}"
            if device.status == "offline":
                item_text += " [离线]"
            
            item = QListWidgetItem(item_text)
            self.device_list.addItem(item)
        
        self.add_log(f"设备列表已刷新，共 {len(devices)} 个设备")
    
    def on_device_discovered(self, device_info):
        """设备发现事件"""
        self.add_log(f"发现新设备: {device_info.device_name} ({device_info.ip_address})")
        self.refresh_device_list()
    
    def on_device_updated(self, device_info):
        """设备更新事件"""
        self.add_log(f"设备更新: {device_info.device_name}")
        self.refresh_device_list()
    
    def on_device_offline(self, device_id):
        """设备离线事件"""
        self.add_log(f"设备离线: {device_id}")
        self.refresh_device_list()
    
    def add_log(self, message: str):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

class UDPDiscoveryModule(CommonModule):
    """UDP设备发现模块"""
    
    # 模块信号
    device_discovered = pyqtSignal(object)  # DeviceInfo
    device_updated = pyqtSignal(object)     # DeviceInfo
    device_offline = pyqtSignal(str)        # device_id
    
    def __init__(self, auth_manager=None):
        super().__init__("udp_discovery", "UDP设备发现", "1.0.0")
        
        # 认证管理器
        self.auth_manager = auth_manager

        # 设备信息
        self.local_device = None
        self.discovered_devices: Dict[str, DeviceInfo] = {}
        
        # 网络组件
        self.socket = None
        self.listen_thread = None
        self.heartbeat_timer = None
        self.cleanup_timer = None
        self.is_running = False
        
        # 配置
        self.discovery_port = UDPDiscoveryProtocol.DEFAULT_PORT
        self.heartbeat_interval = 10  # 秒
        self.device_timeout = 30      # 秒
        
        # 界面
        self.window = UDPDiscoveryWindow(self)
        self.geometry = None
    
    def get_widget(self):
        """返回模块的UI界面"""
        if self.window is None:
            self._initialize_module()
        return self.window
    
    def _initialize_module(self) -> bool:
        """初始化模块"""
        try:
            # 根据认证状态确定设备类型
            if self.auth_manager and self.auth_manager.is_authenticated():
                device_type = "teacher"
            else:
                device_type = "group"

            # 创建本地设备信息
            self.local_device = DeviceInfo(
                device_id=str(uuid.uuid4()),
                device_type=device_type,
                device_name=socket.gethostname(),
                ip_address=self._get_local_ip(),
                port=self.discovery_port,
                mac_address=get_mac_address(),
                capabilities=self.get_config("capabilities", ["whiteboard", "screen_share", "file_transfer"])
            )
            
            # 创建界面
            self.window = UDPDiscoveryWindow(self)
            
            # 设置定时器
            self.heartbeat_timer = QTimer()
            self.heartbeat_timer.timeout.connect(self._send_heartbeat)
            
            self.cleanup_timer = QTimer()
            self.cleanup_timer.timeout.connect(self._cleanup_offline_devices)
            self.cleanup_timer.start(5000)  # 每5秒清理一次
            
            self.logger.info("UDP设备发现模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"UDP设备发现模块初始化失败: {e}")
            return False
    
    def _cleanup_module(self):
        """清理模块资源"""
        self.stop_discovery()
        
        if self.cleanup_timer:
            self.cleanup_timer.stop()
        
        if self.window:
            self.window.close()
    
    def start_discovery(self) -> bool:
        """开始设备发现"""
        if self.is_running:
            return True
        
        try:
            # 创建UDP套接字
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            self.socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.socket.bind(('', self.discovery_port))
            
            # 启动监听线程
            self.is_running = True
            self.listen_thread = threading.Thread(target=self._listen_loop, daemon=True)
            self.listen_thread.start()
            
            # 启动心跳定时器
            self.heartbeat_timer.start(self.heartbeat_interval * 1000)
            
            # 发送初始发现消息
            self._send_discovery_message()
            
            self.logger.info("UDP设备发现已启动")
            return True
            
        except Exception as e:
            self.logger.error(f"启动UDP设备发现失败: {e}")
            self.is_running = False
            return False
    
    def stop_discovery(self):
        """停止设备发现"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 发送离线消息
        if self.socket and self.local_device:
            try:
                goodbye_msg = UDPDiscoveryProtocol.create_goodbye_message(self.local_device.device_id)
                self.socket.sendto(goodbye_msg, (UDPDiscoveryProtocol.BROADCAST_ADDRESS, self.discovery_port))
            except Exception as e:
                self.logger.error(f"发送离线消息失败: {e}")
        
        # 停止定时器
        if self.heartbeat_timer:
            self.heartbeat_timer.stop()
        
        # 关闭套接字
        if self.socket:
            try:
                self.socket.close()
            except Exception:
                pass
            self.socket = None
        
        # 等待监听线程结束
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=1)
        
        self.logger.info("UDP设备发现已停止")
    
    def get_discovered_devices(self) -> Dict[str, DeviceInfo]:
        """获取发现的设备列表"""
        return self.discovered_devices.copy()
    
    def get_device_by_id(self, device_id: str) -> Optional[DeviceInfo]:
        """根据ID获取设备信息"""
        return self.discovered_devices.get(device_id)
    
    def get_devices_by_type(self, device_type: str) -> List[DeviceInfo]:
        """根据类型获取设备列表"""
        return [device for device in self.discovered_devices.values() 
                if device.device_type == device_type and device.status == "online"]
    
    def show_window(self):
        """显示设备发现窗口"""
        if self.window:
            self.window.show()
            self.window.raise_()
            self.window.activateWindow()
    
    def _listen_loop(self):
        """监听循环"""
        while self.is_running:
            try:
                data, addr = self.socket.recvfrom(1024)
                self._handle_received_message(data, addr)
            except socket.timeout:
                continue
            except Exception as e:
                if self.is_running:
                    self.logger.error(f"接收消息失败: {e}")
                break
    
    def _handle_received_message(self, data: bytes, addr: tuple):
        """处理接收到的消息"""
        message = UDPDiscoveryProtocol.parse_message(data)
        if not message:
            return
        
        message_type = message.get("type")
        device_id = message.get("device_id")
        
        if not device_id or device_id == self.local_device.device_id:
            return  # 忽略自己的消息
        
        if message_type == UDPDiscoveryProtocol.MSG_DISCOVERY:
            self._handle_discovery_message(message, addr)
        elif message_type == UDPDiscoveryProtocol.MSG_HEARTBEAT:
            self._handle_heartbeat_message(message, addr)
        elif message_type == UDPDiscoveryProtocol.MSG_GOODBYE:
            self._handle_goodbye_message(message, addr)
    
    def _handle_discovery_message(self, message: Dict[str, Any], addr: tuple):
        """处理设备发现消息"""
        try:
            device_info = DeviceInfo(
                device_id=message["device_id"],
                device_type=message["device_type"],
                device_name=message["device_name"],
                ip_address=message["ip_address"],
                port=message["port"],
                mac_address=message.get("mac_address"),
                capabilities=message.get("capabilities", [])
            )
            
            device_id = device_info.device_id
            
            if device_id not in self.discovered_devices:
                # 新设备
                self.discovered_devices[device_id] = device_info
                self.device_discovered.emit(device_info)
                self.logger.info(f"发现新设备: {device_info.device_name} ({device_info.ip_address})")
                
                # 回复发现消息
                self._send_discovery_message()
            else:
                # 更新现有设备
                existing_device = self.discovered_devices[device_id]
                existing_device.update_last_seen()
                existing_device.device_type = device_info.device_type
                existing_device.device_name = device_info.device_name
                existing_device.ip_address = device_info.ip_address
                existing_device.port = device_info.port
                existing_device.capabilities = device_info.capabilities
                existing_device.status = "online"
                
                self.device_updated.emit(existing_device)
                
        except KeyError as e:
            self.logger.error(f"设备发现消息格式错误: {e}")
    
    def _handle_heartbeat_message(self, message: Dict[str, Any], addr: tuple):
        """处理心跳消息"""
        device_id = message.get("device_id")
        if device_id in self.discovered_devices:
            device = self.discovered_devices[device_id]
            device.update_last_seen()
            if device.status == "offline":
                device.status = "online"
                self.device_updated.emit(device)
    
    def _handle_goodbye_message(self, message: Dict[str, Any], addr: tuple):
        """处理离线消息"""
        device_id = message.get("device_id")
        if device_id in self.discovered_devices:
            device = self.discovered_devices[device_id]
            device.status = "offline"
            self.device_offline.emit(device_id)
            self.logger.info(f"设备离线: {device.device_name}")
    
    def _send_discovery_message(self):
        """发送设备发现消息"""
        if not self.socket or not self.local_device:
            return
        
        try:
            message = UDPDiscoveryProtocol.create_discovery_message(self.local_device)
            self.socket.sendto(message, (UDPDiscoveryProtocol.BROADCAST_ADDRESS, self.discovery_port))
        except Exception as e:
            self.logger.error(f"发送发现消息失败: {e}")
    
    def _send_heartbeat(self):
        """发送心跳消息"""
        if not self.socket or not self.local_device:
            return
        
        try:
            message = UDPDiscoveryProtocol.create_heartbeat_message(self.local_device)
            self.socket.sendto(message, (UDPDiscoveryProtocol.BROADCAST_ADDRESS, self.discovery_port))
        except Exception as e:
            self.logger.error(f"发送心跳消息失败: {e}")
    
    def _cleanup_offline_devices(self):
        """清理离线设备"""
        offline_devices = []
        
        for device_id, device in self.discovered_devices.items():
            if device.is_expired(self.device_timeout):
                if device.status == "online":
                    device.status = "offline"
                    self.device_offline.emit(device_id)
                    offline_devices.append(device_id)
        
        # 移除长时间离线的设备
        # for device_id in offline_devices:
        #     if self.discovered_devices[device_id].is_expired(self.device_timeout * 3):
        #         del self.discovered_devices[device_id]
    
    def _get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            # 创建一个临时套接字来获取本地IP
            temp_socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            temp_socket.connect(("*******", 80))
            local_ip = temp_socket.getsockname()[0]
            temp_socket.close()
            return local_ip
        except Exception:
            return "127.0.0.1"
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理模块间消息"""
        if message_type == "get_devices":
            # 返回设备列表
            response_data = {
                "devices": [device.to_dict() for device in self.discovered_devices.values()],
                "local_device": self.local_device.to_dict() if self.local_device else None
            }
            self.send_message(sender, "devices_response", response_data)
        
        elif message_type == "start_discovery":
            success = self.start_discovery()
            self.send_message(sender, "discovery_started", {"success": success})
        
        elif message_type == "stop_discovery":
            self.stop_discovery()
            self.send_message(sender, "discovery_stopped", {"success": True})
        
        elif message_type == "show_window":
            self.show_window()
