"""
设备看板模块 - 教师专用设备监控和管理功能
结合UDP设备发现实现设备状态监控，提供可视化界面
"""
import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QTableWidget, QTableWidgetItem, QPushButton, 
                            QGroupBox, QProgressBar, QTextEdit, QSplitter,
                            QHeaderView, QFrame, QGridLayout, QMessageBox,
                            QComboBox, QSpinBox, QCheckBox, QTabWidget)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from .base_module import TeacherModule
from .udp_discovery import DeviceInfo
import logging

class DeviceStats:
    """设备统计信息"""
    
    def __init__(self):
        self.total_devices = 0
        self.online_devices = 0
        self.offline_devices = 0
        self.teacher_devices = 0
        self.group_devices = 0
        self.student_devices = 0
        self.last_update = datetime.now()
    
    def update_from_devices(self, devices: Dict[str, DeviceInfo]):
        """从设备列表更新统计"""
        self.total_devices = len(devices)
        self.online_devices = sum(1 for d in devices.values() if d.status == "online")
        self.offline_devices = self.total_devices - self.online_devices
        self.teacher_devices = sum(1 for d in devices.values() if d.device_type == "teacher")
        self.group_devices = sum(1 for d in devices.values() if d.device_type == "group")
        self.student_devices = sum(1 for d in devices.values() if d.device_type == "student")
        self.last_update = datetime.now()

class DeviceAlert:
    """设备告警信息"""
    
    def __init__(self, device_id: str, alert_type: str, message: str, severity: str = "info"):
        self.device_id = device_id
        self.alert_type = alert_type  # offline, online, error, warning
        self.message = message
        self.severity = severity  # info, warning, error, critical
        self.timestamp = datetime.now()
        self.acknowledged = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "device_id": self.device_id,
            "alert_type": self.alert_type,
            "message": self.message,
            "severity": self.severity,
            "timestamp": self.timestamp.isoformat(),
            "acknowledged": self.acknowledged
        }

class DeviceDashboardModule(TeacherModule):
    """设备看板模块"""
    
    # 模块信号
    stats_updated = pyqtSignal(object)  # DeviceStats
    
    def __init__(self):
        super().__init__(
            module_name="device_dashboard",
            display_name="设备看板",
            version="1.0.0"
        )
        
        # 设备相关
        self.udp_discovery = None
        self.devices: Dict[str, DeviceInfo] = {}
        self.device_stats = DeviceStats()
        self.alerts: List[DeviceAlert] = []
        
        # 界面
        self.dashboard_window = None
        
        # 定时器
        self.update_timer = None
        self.alert_timer = None
        
        # 配置
        self.config.update({
            "update_interval": 5,  # 秒
            "device_timeout": 30,  # 秒
        })
    
    def get_widget(self):
        return self.dashboard_window

    def _initialize_module(self) -> bool:
        """初始化设备看板模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("设备看板模块需要教师认证")
                return False
            
            # 获取UDP发现模块实例
            self.udp_discovery = self._get_udp_discovery_module()
            if not self.udp_discovery:
                self.logger.error("无法获取UDP设备发现模块")
                return False
            
            # 连接UDP发现模块信号
            self.udp_discovery.device_discovered.connect(self.on_device_discovered)
            self.udp_discovery.device_updated.connect(self.on_device_updated)
            self.udp_discovery.device_offline.connect(self.on_device_offline)
            
            # 创建界面
            self.dashboard_window = DeviceDashboardWindow(self)
            
            # 设置定时器
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_device_stats)
            self.update_timer.start(self.get_config("update_interval", 5) * 1000)
            
            # 初始化设备列表
            self.refresh_devices()
            
            self.logger.info("设备看板模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设备看板模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理设备看板模块资源"""
        try:
            # 停止定时器
            if self.update_timer and self.update_timer.isActive():
                self.update_timer.stop()
            if self.alert_timer and self.alert_timer.isActive():
                self.alert_timer.stop()
            
            # 断开信号连接
            if self.udp_discovery:
                try:
                    self.udp_discovery.device_discovered.disconnect(self.on_device_discovered)
                    self.udp_discovery.device_updated.disconnect(self.on_device_updated)
                    self.udp_discovery.device_offline.disconnect(self.on_device_offline)
                except:
                    pass
            
            # 关闭界面
            if self.dashboard_window:
                self.dashboard_window.close()
                self.dashboard_window = None
            
            # 清理数据
            self.devices.clear()
            
            self.logger.info("设备看板模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理设备看板模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "get_device_stats":
            self.send_device_stats(sender)
        elif message_type == "get_device_list":
            self.send_device_list(sender)
        elif message_type == "refresh_devices":
            self.refresh_devices()
        elif message_type == "show_dashboard":
            self.show()
    
    def show(self):
        """显示设备看板"""
        if self.dashboard_window:
            self.dashboard_window.show()
            self.dashboard_window.raise_()
            self.dashboard_window.activateWindow()
    
    def hide(self):
        """隐藏设备看板"""
        if self.dashboard_window:
            self.dashboard_window.hide()
    
    def close(self):
        """关闭设备看板"""
        self.cleanup()
    
    def refresh_devices(self):
        """刷新设备列表"""
        if self.udp_discovery:
            self.devices = self.udp_discovery.get_discovered_devices()
            self.update_device_stats()
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            # 通过模块管理器获取UDP发现模块
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")

            # 如果没有模块管理器，尝试直接导入和创建
            from .udp_discovery import UDPDiscoveryModule
            udp_module = UDPDiscoveryModule()
            if udp_module.initialize():
                return udp_module

        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")

        return None
    
    def update_device_stats(self):
        """更新设备统计"""
        self.device_stats.update_from_devices(self.devices)
        self.stats_updated.emit(self.device_stats)
        
        # 通知其他模块统计信息已更新
        self.broadcast_message("device_stats_updated", {
            "stats": {
                "total": self.device_stats.total_devices,
                "online": self.device_stats.online_devices,
                "offline": self.device_stats.offline_devices,
                "teacher": self.device_stats.teacher_devices,
                "group": self.device_stats.group_devices,
                "student": self.device_stats.student_devices
            },
            "timestamp": self.device_stats.last_update.isoformat()
        })
    
    
    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.devices[device_info.device_id] = device_info
        self.update_device_stats()
    
    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.devices[device_info.device_id] = device_info
        self.update_device_stats()
    
    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.devices:
            device = self.devices[device_id]
            device.status = "offline"
        
        self.update_device_stats()
    
    def send_device_stats(self, requester: str):
        """发送设备统计信息"""
        stats_data = {
            "total": self.device_stats.total_devices,
            "online": self.device_stats.online_devices,
            "offline": self.device_stats.offline_devices,
            "teacher": self.device_stats.teacher_devices,
            "group": self.device_stats.group_devices,
            "student": self.device_stats.student_devices,
            "last_update": self.device_stats.last_update.isoformat()
        }
        self.send_message(requester, "device_stats_response", stats_data)
    
    def send_device_list(self, requester: str):
        """发送设备列表"""
        devices_data = [device.to_dict() for device in self.devices.values()]
        self.send_message(requester, "device_list_response", {"devices": devices_data})
    

class DeviceDashboardWindow(QWidget):
    """设备看板窗口"""

    def __init__(self, module: DeviceDashboardModule):
        super().__init__()
        self.module = module
        self.init_ui()

        # 连接模块信号
        self.module.stats_updated.connect(self.on_stats_updated)

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设备看板")
        self.setGeometry(100, 100, 1000, 800)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("设备看板")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_layout.addWidget(title_label)
        title_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_data)
        title_layout.addWidget(self.refresh_btn)

        main_layout.addLayout(title_layout)

        # 创建选项卡
        self.tab_widget = QTabWidget()

        # 概览选项卡
        self.overview_tab = self.create_overview_tab()
        self.tab_widget.addTab(self.overview_tab, "概览")

        # 设备列表选项卡
        self.devices_tab = self.create_devices_tab()
        self.tab_widget.addTab(self.devices_tab, "设备列表")

        main_layout.addWidget(self.tab_widget)

        self.setLayout(main_layout)

        # 设置样式
        self.setStyleSheet("""
            QWidget {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 2px solid #007acc;
            }
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #c0c0c0;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

    def create_overview_tab(self) -> QWidget:
        """创建概览选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()

        # 统计卡片区域
        stats_layout = QGridLayout()

        # 总设备数卡片
        self.total_card = self.create_stat_card("总设备数", "0", "#007acc")
        stats_layout.addWidget(self.total_card, 0, 0)

        # 在线设备卡片
        self.online_card = self.create_stat_card("在线设备", "0", "#28a745")
        stats_layout.addWidget(self.online_card, 0, 1)

        # 离线设备卡片
        self.offline_card = self.create_stat_card("离线设备", "0", "#dc3545")
        stats_layout.addWidget(self.offline_card, 0, 2)

        # 教师设备卡片
        self.teacher_card = self.create_stat_card("教师设备", "0", "#6f42c1")
        stats_layout.addWidget(self.teacher_card, 1, 0)

        # 小组设备卡片
        self.group_card = self.create_stat_card("小组设备", "0", "#fd7e14")
        stats_layout.addWidget(self.group_card, 1, 1)

        # 学生设备卡片
        self.student_card = self.create_stat_card("学生设备", "0", "#20c997")
        stats_layout.addWidget(self.student_card, 1, 2)

        layout.addLayout(stats_layout)

        # 设备状态图表区域（简化版本）
        chart_group = QGroupBox("设备状态分布")
        chart_layout = QVBoxLayout()

        # 在线率进度条
        online_layout = QHBoxLayout()
        online_layout.addWidget(QLabel("在线率:"))
        self.online_progress = QProgressBar()
        self.online_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }
        """)
        online_layout.addWidget(self.online_progress)
        self.online_percent_label = QLabel("0%")
        online_layout.addWidget(self.online_percent_label)
        chart_layout.addLayout(online_layout)

        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)

        # 最近活动区域
        activity_group = QGroupBox("最近活动")
        activity_layout = QVBoxLayout()

        self.activity_text = QTextEdit()
        self.activity_text.setMaximumHeight(200)
        self.activity_text.setReadOnly(True)
        activity_layout.addWidget(self.activity_text)

        activity_group.setLayout(activity_layout)
        layout.addWidget(activity_group)

        tab.setLayout(layout)
        return tab

    def create_stat_card(self, title: str, value: str, color: str) -> QFrame:
        """创建统计卡片"""
        card = QFrame()
        card.setFrameStyle(QFrame.Box)
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 16px;
            }}
            QLabel {{
                background-color: transparent;
            }}
        """)

        layout = QVBoxLayout()

        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10))
        title_label.setStyleSheet("color: #666;")
        layout.addWidget(title_label)

        # 数值
        value_label = QLabel(value)
        value_label.setFont(QFont("Arial", 24, QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)

        card.setLayout(layout)

        # 保存引用以便更新
        card.value_label = value_label

        return card

    def create_devices_tab(self) -> QWidget:
        """创建设备列表选项卡"""
        tab = QWidget()
        layout = QVBoxLayout()

        # 过滤器区域
        filter_layout = QHBoxLayout()
        filter_layout.addWidget(QLabel("设备类型:"))

        self.device_type_filter = QComboBox()
        self.device_type_filter.addItems(["全部", "教师设备", "小组设备", "学生设备"])
        self.device_type_filter.currentTextChanged.connect(self.filter_devices)
        filter_layout.addWidget(self.device_type_filter)

        filter_layout.addWidget(QLabel("状态:"))
        self.status_filter = QComboBox()
        self.status_filter.addItems(["全部", "在线", "离线"])
        self.status_filter.currentTextChanged.connect(self.filter_devices)
        filter_layout.addWidget(self.status_filter)

        filter_layout.addStretch()
        layout.addLayout(filter_layout)

        # 设备表格
        self.devices_table = QTableWidget()
        self.devices_table.setColumnCount(7)
        self.devices_table.setHorizontalHeaderLabels([
            "设备名称", "设备类型", "IP地址", "端口", "状态", "最后见到", "功能"
        ])

        # 设置表格属性
        header = self.devices_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(QHeaderView.ResizeToContents)

        self.devices_table.setAlternatingRowColors(True)
        self.devices_table.setSelectionBehavior(QTableWidget.SelectRows)

        layout.addWidget(self.devices_table)

        tab.setLayout(layout)
        return tab


    def refresh_data(self):
        """刷新数据"""
        self.module.refresh_devices()
        self.update_devices_table()
        self.add_activity("手动刷新数据")

    def filter_devices(self):
        """过滤设备列表"""
        self.update_devices_table()

    def on_stats_updated(self, stats: DeviceStats):
        """处理统计更新信号"""
        # 更新统计卡片
        self.total_card.value_label.setText(str(stats.total_devices))
        self.online_card.value_label.setText(str(stats.online_devices))
        self.offline_card.value_label.setText(str(stats.offline_devices))
        self.teacher_card.value_label.setText(str(stats.teacher_devices))
        self.group_card.value_label.setText(str(stats.group_devices))
        self.student_card.value_label.setText(str(stats.student_devices))

        # 更新在线率
        if stats.total_devices > 0:
            online_rate = int((stats.online_devices / stats.total_devices) * 100)
            self.online_progress.setValue(online_rate)
            self.online_percent_label.setText(f"{online_rate}%")
        else:
            self.online_progress.setValue(0)
            self.online_percent_label.setText("0%")

        # 更新设备表格
        self.update_devices_table()

    def update_devices_table(self):
        """更新设备表格"""
        # 获取过滤条件
        type_filter = self.device_type_filter.currentText()
        status_filter = self.status_filter.currentText()

        # 过滤设备
        filtered_devices = []
        for device in self.module.devices.values():
            # 类型过滤
            if type_filter != "全部":
                if type_filter == "教师设备" and device.device_type != "teacher":
                    continue
                elif type_filter == "小组设备" and device.device_type != "group":
                    continue
                elif type_filter == "学生设备" and device.device_type != "student":
                    continue

            # 状态过滤
            if status_filter != "全部":
                if status_filter == "在线" and device.status != "online":
                    continue
                elif status_filter == "离线" and device.status != "offline":
                    continue

            filtered_devices.append(device)

        # 更新表格
        self.devices_table.setRowCount(len(filtered_devices))

        for row, device in enumerate(filtered_devices):
            self.devices_table.setItem(row, 0, QTableWidgetItem(device.device_name))
            self.devices_table.setItem(row, 1, QTableWidgetItem(device.device_type))
            self.devices_table.setItem(row, 2, QTableWidgetItem(device.ip_address))
            self.devices_table.setItem(row, 3, QTableWidgetItem(str(device.port)))

            # 状态列带颜色
            status_item = QTableWidgetItem(device.status)
            if device.status == "online":
                status_item.setBackground(QColor("#d4edda"))
            else:
                status_item.setBackground(QColor("#f8d7da"))
            self.devices_table.setItem(row, 4, status_item)

            # 最后见到时间
            last_seen = device.last_seen.strftime("%H:%M:%S")
            self.devices_table.setItem(row, 5, QTableWidgetItem(last_seen))

            # 功能列表
            capabilities = ", ".join(device.capabilities)
            self.devices_table.setItem(row, 6, QTableWidgetItem(capabilities))


    def add_activity(self, message: str):
        """添加活动记录"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.activity_text.append(f"[{timestamp}] {message}")

        # 限制活动记录数量
        document = self.activity_text.document()
        if document.blockCount() > 100:
            cursor = self.activity_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
            cursor.deleteChar()  # 删除换行符
