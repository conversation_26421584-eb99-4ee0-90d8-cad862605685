"""
基础模块类 - 所有功能模块的基类
"""
from abc import ABC, abstractmethod
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, Any, Optional
import logging

class ModuleInterface(ABC):
    """模块接口定义"""
    
    @abstractmethod
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        pass
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化模块"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理模块资源"""
        pass
    
    @abstractmethod
    def is_ready(self) -> bool:
        """检查模块是否就绪"""
        pass

class BaseModule(QObject):
    """基础模块类"""
    
    # 模块信号
    module_ready = pyqtSignal()
    module_error = pyqtSignal(str)
    module_message = pyqtSignal(str, dict)  # 模块间通信信号
    
    def __init__(self, module_name: str, display_name: str, version: str = "1.0.0"):
        super().__init__()
        self.module_name = module_name
        self.display_name = display_name
        self.version = version
        self.is_initialized = False
        self.is_common = True  # 默认为通用模块
        self.logger = logging.getLogger(f"module.{module_name}")
        self.communication_manager = None

        # 模块配置
        self.config = {}
        
        # 模块状态
        self.status = "not_initialized"  # not_initialized, initializing, ready, error
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            "name": self.module_name,
            "display_name": self.display_name,
            "version": self.version,
            "status": self.status,
            "is_initialized": self.is_initialized
        }
    
    def set_communication_manager(self, comm_manager):
        """设置通信管理器"""
        self.communication_manager = comm_manager
        if comm_manager:
            # 注册模块到通信管理器
            comm_manager.register_module(self)

    def set_module_manager(self, module_manager):
        """设置模块管理器"""
        self.module_manager = module_manager
    
    def initialize(self) -> bool:
        """初始化模块"""
        try:
            self.status = "initializing"
            self.logger.info(f"正在初始化模块: {self.display_name}")
            
            # 调用子类的初始化方法
            if self._initialize_module():
                self.is_initialized = True
                self.status = "ready"
                self.module_ready.emit()
                self.logger.info(f"模块初始化成功: {self.display_name}")
                return True
            else:
                self.status = "error"
                self.logger.error(f"模块初始化失败: {self.display_name}")
                return False
                
        except Exception as e:
            self.status = "error"
            error_msg = f"模块初始化异常: {str(e)}"
            self.logger.error(error_msg)
            self.module_error.emit(error_msg)
            return False
    
    def cleanup(self):
        """清理模块资源"""
        try:
            self.logger.info(f"正在清理模块: {self.display_name}")
            self._cleanup_module()
            self.is_initialized = False
            self.status = "not_initialized"
            
            # 从通信管理器注销
            if self.communication_manager:
                self.communication_manager.unregister_module(self.module_name)
                
        except Exception as e:
            self.logger.error(f"清理模块时出错: {str(e)}")
    
    def is_ready(self) -> bool:
        """检查模块是否就绪"""
        return self.is_initialized and self.status == "ready"
    
    def send_message(self, target_module: str, message_type: str, data: Dict[str, Any]):
        """发送消息到其他模块"""
        if self.communication_manager:
            self.communication_manager.send_message(
                self.module_name, target_module, message_type, data
            )
    
    def broadcast_message(self, message_type: str, data: Dict[str, Any]):
        """广播消息到所有模块"""
        if self.communication_manager:
            self.communication_manager.broadcast_message(
                self.module_name, message_type, data
            )
    
    def handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        try:
            self._handle_message(sender, message_type, data)
        except Exception as e:
            self.logger.error(f"处理消息时出错: {str(e)}")
    
    def set_config(self, config: Dict[str, Any]):
        """设置模块配置"""
        self.config.update(config)
        self._on_config_changed()
    
    def get_config(self, key: str, default=None):
        """获取配置值"""
        return self.config.get(key, default)
    
    # 子类需要实现的方法
    @abstractmethod
    def _initialize_module(self) -> bool:
        """子类实现的初始化方法"""
        pass
    
    def _cleanup_module(self):
        """子类实现的清理方法"""
        pass
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """子类实现的消息处理方法"""
        pass
    
    def _on_config_changed(self):
        """配置变更时的回调"""
        pass

class CommonModule(BaseModule):
    """通用功能模块基类"""

    def __init__(self, module_name: str, display_name: str, version: str = "1.0.0"):
        super().__init__(module_name, display_name, version)
        self.module_type = "common"
        self.is_common = True
    
    def get_module_info(self) -> Dict[str, Any]:
        info = super().get_module_info()
        info["type"] = self.module_type
        info["requires_auth"] = False
        return info

class TeacherModule(BaseModule):
    """教师专用功能模块基类"""

    def __init__(self, module_name: str, display_name: str, version: str = "1.0.0"):
        super().__init__(module_name, display_name, version)
        self.module_type = "teacher"
        self.is_common = False
        self.auth_manager = None
    
    def get_module_info(self) -> Dict[str, Any]:
        info = super().get_module_info()
        info["type"] = self.module_type
        info["requires_auth"] = True
        return info
    
    def set_auth_manager(self, auth_manager):
        """设置认证管理器"""
        self.auth_manager = auth_manager
    
    def is_authenticated(self) -> bool:
        """检查是否已认证"""
        return self.auth_manager and self.auth_manager.is_authenticated()
    
    def get_current_user(self) -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        if self.auth_manager:
            return self.auth_manager.get_current_user()
        return None
    
    def initialize(self) -> bool:
        """重写初始化方法，添加认证检查"""
        if not self.is_authenticated():
            self.logger.warning(f"教师模块 {self.display_name} 需要认证")
            self.status = "error"
            self.module_error.emit("需要教师认证")
            return False
        
        return super().initialize()