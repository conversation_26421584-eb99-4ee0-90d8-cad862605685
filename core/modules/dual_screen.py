"""
主副屏模式功能模块 - 教师专用功能
教师端设备主副屏模式功能，实现主副屏不同内容显示，实现一屏课件一屏白板模式
"""
import sys
import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,
                            QDialog, QDialogButtonBox, QInputDialog, QDesktopWidget,
                            QApplication, QMainWindow, QSplitter)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject, QRect
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter, QScreen,QPen
from .base_module import TeacherModule
import logging

class ScreenInfo:
    """屏幕信息类"""
    
    def __init__(self, screen_id: int, geometry: QRect, is_primary: bool = False):
        self.screen_id = screen_id
        self.geometry = geometry
        self.is_primary = is_primary
        self.width = geometry.width()
        self.height = geometry.height()
        self.x = geometry.x()
        self.y = geometry.y()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "screen_id": self.screen_id,
            "width": self.width,
            "height": self.height,
            "x": self.x,
            "y": self.y,
            "is_primary": self.is_primary
        }

class DualScreenConfig:
    """双屏配置类"""
    
    def __init__(self):
        self.primary_screen_id = 0
        self.secondary_screen_id = 1
        self.primary_content = "courseware"  # courseware, whiteboard
        self.secondary_content = "whiteboard"  # courseware, whiteboard
        self.sync_navigation = True  # 是否同步课件翻页
        self.auto_switch = False  # 是否自动切换内容
        self.layout_mode = "extended"  # extended, mirrored, single
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "primary_screen_id": self.primary_screen_id,
            "secondary_screen_id": self.secondary_screen_id,
            "primary_content": self.primary_content,
            "secondary_content": self.secondary_content,
            "sync_navigation": self.sync_navigation,
            "auto_switch": self.auto_switch,
            "layout_mode": self.layout_mode
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DualScreenConfig':
        """从字典创建配置"""
        config = cls()
        config.primary_screen_id = data.get("primary_screen_id", 0)
        config.secondary_screen_id = data.get("secondary_screen_id", 1)
        config.primary_content = data.get("primary_content", "courseware")
        config.secondary_content = data.get("secondary_content", "whiteboard")
        config.sync_navigation = data.get("sync_navigation", True)
        config.auto_switch = data.get("auto_switch", False)
        config.layout_mode = data.get("layout_mode", "extended")
        return config

class CoursewareWindow(QMainWindow):
    """课件显示窗口 - 等待老师主动打开课件"""

    courseware_opened = pyqtSignal(str)  # 课件打开信号

    def __init__(self, screen_geometry: QRect):
        super().__init__()
        self.screen_geometry = screen_geometry
        self.courseware_widget = None  # 存储实际的课件显示组件
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("课件显示区域")
        self.setGeometry(self.screen_geometry)
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout()
        layout.setContentsMargins(0, 0, 0, 0)  # 移除边距，为课件提供最大显示空间

        central_widget.setLayout(layout)

        # 设置窗口完全透明，不遮挡老师操作
        self.setStyleSheet("""
            QMainWindow {
                background-color: transparent;
            }
            QWidget {
                background-color: transparent;
            }
        """)

        # 设置窗口属性为透明
        self.setAttribute(Qt.WA_TranslucentBackground, True)

    def load_courseware(self, courseware_path: str):
        """加载课件文件"""
        try:
            # 这里应该根据文件类型加载相应的课件显示组件
            # 例如：PPT查看器、PDF查看器等
            # 目前先创建一个课件显示组件

            if not self.courseware_widget:
                self.courseware_widget = QLabel()
                self.courseware_widget.setAlignment(Qt.AlignCenter)
                self.courseware_widget.setStyleSheet("""
                    QLabel {
                        background-color: white;
                        font-size: 16px;
                        color: #007acc;
                        border: 1px solid #007acc;
                        border-radius: 4px;
                        padding: 10px;
                    }
                """)
                self.centralWidget().layout().addWidget(self.courseware_widget)

            # 显示课件加载状态（实际应用中这里会显示真实的课件内容）
            self.courseware_widget.setText(f"课件已加载：{courseware_path}")
            self.courseware_widget.show()

            self.courseware_opened.emit(courseware_path)

        except Exception as e:
            # 如果有课件组件，显示错误信息
            if hasattr(self, 'courseware_widget') and self.courseware_widget:
                self.courseware_widget.setText(f"课件加载失败：{str(e)}")
                self.courseware_widget.show()

    def close_courseware(self):
        """关闭当前课件"""
        if self.courseware_widget:
            self.courseware_widget.hide()

    def is_courseware_loaded(self) -> bool:
        """检查是否已加载课件"""
        return self.courseware_widget is not None and self.courseware_widget.isVisible()

class SimpleWhiteboardWidget(QWidget):
    """简单的白板绘制组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.drawing = False
        self.brush_size = 3
        self.brush_color = QColor(0, 0, 0)  # 默认黑色
        self.last_point = None

        # 创建画布
        self.image = QPixmap(800, 600)
        self.image.fill(Qt.white)

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 存储绘制路径
        self.paths = []
        self.current_path = []

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        # 重新创建适应新尺寸的画布
        new_image = QPixmap(event.size())
        new_image.fill(Qt.white)

        if not self.image.isNull():
            painter = QPainter(new_image)
            painter.drawPixmap(0, 0, self.image)
            painter.end()

        self.image = new_image
        super().resizeEvent(event)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drawing = True
            self.last_point = event.pos()
            self.current_path = [event.pos()]

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if event.buttons() & Qt.LeftButton and self.drawing:
            painter = QPainter(self.image)
            pen = QPen(self.brush_color, self.brush_size, Qt.SolidLine, Qt.RoundCap, Qt.RoundJoin)
            painter.setPen(pen)
            painter.setRenderHint(QPainter.Antialiasing)

            if self.last_point:
                painter.drawLine(self.last_point, event.pos())

            painter.end()

            self.last_point = event.pos()
            self.current_path.append(event.pos())
            self.update()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            if self.current_path:
                self.paths.append({
                    'path': self.current_path.copy(),
                    'color': QColor(self.brush_color),
                    'size': self.brush_size
                })
            self.current_path = []
            self.last_point = None

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.drawPixmap(0, 0, self.image)

    def clear_board(self):
        """清空画板"""
        self.image.fill(Qt.white)
        self.paths.clear()
        self.current_path.clear()
        self.update()

    def set_brush_color(self, color):
        """设置画笔颜色"""
        self.brush_color = color

    def set_brush_size(self, size):
        """设置画笔大小"""
        self.brush_size = size

class WhiteboardWindow(QMainWindow):
    """简单白板窗口 - 包含白色背景和基础绘图工具"""

    def __init__(self, screen_geometry: QRect):
        super().__init__()
        self.screen_geometry = screen_geometry
        self.whiteboard_widget = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("简单白板")
        self.setGeometry(self.screen_geometry)
        self.setWindowFlags(Qt.FramelessWindowHint)

        # 主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建白板绘制区域
        self.whiteboard_widget = SimpleWhiteboardWidget()
        self.whiteboard_widget.setMinimumSize(self.screen_geometry.width(), self.screen_geometry.height() - 80)
        main_layout.addWidget(self.whiteboard_widget)

        # 创建工具栏
        self.create_toolbar()
        main_layout.addWidget(self.toolbar)

        central_widget.setLayout(main_layout)

        # 设置整体样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
            }
        """)

    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar = QWidget()
        self.toolbar.setFixedHeight(80)
        self.toolbar.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-top: 2px solid #dee2e6;
            }
        """)

        layout = QHBoxLayout()
        layout.setContentsMargins(20, 10, 20, 10)

        # 画笔工具
        pen_label = QLabel("画笔:")
        pen_label.setStyleSheet("font-weight: bold; color: #333;")
        layout.addWidget(pen_label)

        # 颜色选择按钮
        self.color_buttons = []
        colors = [
            ("#000000", "黑色"),
            ("#FF0000", "红色"),
            ("#00FF00", "绿色"),
            ("#0000FF", "蓝色"),
            ("#FFFF00", "黄色"),
            ("#FF00FF", "紫色"),
            ("#00FFFF", "青色"),
            ("#FFA500", "橙色")
        ]

        for color_code, color_name in colors:
            btn = QPushButton()
            btn.setFixedSize(30, 30)
            btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_code};
                    border: 2px solid #333;
                    border-radius: 15px;
                }}
                QPushButton:hover {{
                    border: 3px solid #007acc;
                }}
                QPushButton:pressed {{
                    border: 3px solid #005a9e;
                }}
            """)
            btn.clicked.connect(lambda checked, c=color_code: self.set_color(c))
            btn.setToolTip(color_name)
            layout.addWidget(btn)
            self.color_buttons.append(btn)

        layout.addSpacing(20)

        # 画笔大小
        size_label = QLabel("大小:")
        size_label.setStyleSheet("font-weight: bold; color: #333;")
        layout.addWidget(size_label)

        # 画笔大小按钮
        sizes = [1, 3, 5, 8, 12]
        for size in sizes:
            btn = QPushButton(str(size))
            btn.setFixedSize(35, 35)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: white;
                    border: 2px solid #333;
                    border-radius: 17px;
                    font-weight: bold;
                    color: #333;
                }
                QPushButton:hover {
                    background-color: #007acc;
                    color: white;
                }
                QPushButton:pressed {
                    background-color: #005a9e;
                }
            """)
            btn.clicked.connect(lambda checked, s=size: self.set_brush_size(s))
            btn.setToolTip(f"画笔大小: {size}")
            layout.addWidget(btn)

        layout.addSpacing(20)

        # 功能按钮
        clear_btn = QPushButton("清空")
        clear_btn.setFixedSize(60, 40)
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        clear_btn.clicked.connect(self.clear_whiteboard)
        layout.addWidget(clear_btn)

        layout.addStretch()

        # 状态显示
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 12px;
                padding: 5px 10px;
                background-color: white;
                border: 1px solid #dee2e6;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.status_label)

        self.toolbar.setLayout(layout)

    def set_color(self, color_code):
        """设置画笔颜色"""
        if self.whiteboard_widget:
            color = QColor(color_code)
            self.whiteboard_widget.set_brush_color(color)
            self.status_label.setText(f"颜色: {color_code}")

    def set_brush_size(self, size):
        """设置画笔大小"""
        if self.whiteboard_widget:
            self.whiteboard_widget.set_brush_size(size)
            self.status_label.setText(f"画笔大小: {size}")

    def clear_whiteboard(self):
        """清空白板"""
        if self.whiteboard_widget:
            self.whiteboard_widget.clear_board()
            self.status_label.setText("白板已清空")

    def show(self):
        """显示白板"""
        super().show()

    def hide(self):
        """隐藏白板"""
        super().hide()

    def close(self):
        """关闭白板"""
        super().close()

    def clear_whiteboard(self):
        """清空白板"""
        if self.whiteboard_widget:
            self.whiteboard_widget.clear_board()

    def get_whiteboard_module(self):
        """获取白板模块实例（简单白板返回自身）"""
        return self

class DualScreenModule(TeacherModule):
    """主副屏模式功能模块"""
    
    # 模块信号
    screen_config_changed = pyqtSignal(object)  # DualScreenConfig
    page_navigation = pyqtSignal(int)           # 页面导航
    content_switched = pyqtSignal(str, str)     # 内容切换 (screen, content)
    
    def __init__(self):
        super().__init__(
            module_name="dual_screen",
            display_name="主副屏模式",
            version="1.0.0"
        )
        
        # 屏幕相关
        self.available_screens: List[ScreenInfo] = []
        self.dual_screen_config = DualScreenConfig()
        self.is_dual_screen_mode = False
        
        # 窗口
        self.control_window = None
        self.courseware_windows: Dict[int, CoursewareWindow] = {}
        self.whiteboard_windows: Dict[int, WhiteboardWindow] = {}
        
        # 配置
        self.config.update({
            "auto_detect_screens": True,        # 自动检测屏幕
            "remember_config": True,            # 记住配置
            "sync_navigation": True,            # 同步导航
            "enable_hotkeys": True,             # 启用热键
            "auto_fullscreen": True             # 自动全屏
        })
    
    def get_widget(self):
        return self.control_window

    def _initialize_module(self) -> bool:
        """初始化主副屏模式模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("主副屏模式模块需要教师认证")
                return False
            
            # 检测可用屏幕
            self.detect_screens()
            
            # 创建控制界面
            self.control_window = DualScreenControlWindow(self)
            
            # 加载配置
            self.load_screen_config()
            
            self.logger.info("主副屏模式模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"主副屏模式模块初始化失败: {str(e)}")
            return False

    def _cleanup_module(self):
        """清理主副屏模式模块资源"""
        try:
            # 保存配置
            if self.get_config("remember_config", True):
                self.save_screen_config()

            # 关闭所有窗口
            self.close_all_windows()

            # 关闭控制界面
            if self.control_window:
                self.control_window.close()
                self.control_window = None

            # 清理数据
            self.available_screens.clear()
            self.courseware_windows.clear()
            self.whiteboard_windows.clear()

            self.logger.info("主副屏模式模块资源清理完成")

        except Exception as e:
            self.logger.error(f"清理主副屏模式模块资源时出错: {str(e)}")

    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "enable_dual_screen":
            self.enable_dual_screen_mode()

        elif message_type == "disable_dual_screen":
            self.disable_dual_screen_mode()

        elif message_type == "switch_content":
            screen_id = data.get("screen_id")
            content_type = data.get("content_type")
            if screen_id is not None and content_type:
                self.switch_screen_content(screen_id, content_type)

        elif message_type == "navigate_page":
            page = data.get("page")
            if page:
                self.navigate_to_page(page)

        elif message_type == "get_screen_status":
            self.send_screen_status(sender)

        elif message_type == "show_dual_screen":
            self.show()

    def show(self):
        """显示主副屏控制窗口"""
        if self.control_window:
            self.control_window.show()
            self.control_window.raise_()
            self.control_window.activateWindow()

    def hide(self):
        """隐藏主副屏控制窗口"""
        if self.control_window:
            self.control_window.hide()

    def close(self):
        """关闭主副屏模式模块"""
        self.cleanup()

    def detect_screens(self):
        """检测可用屏幕"""
        try:
            self.available_screens.clear()

            app = QApplication.instance()
            if not app:
                self.logger.error("无法获取QApplication实例")
                return

            desktop = app.desktop()
            screen_count = desktop.screenCount()

            for i in range(screen_count):
                geometry = desktop.screenGeometry(i)
                is_primary = (i == desktop.primaryScreen())

                screen_info = ScreenInfo(i, geometry, is_primary)
                self.available_screens.append(screen_info)

                self.logger.info(f"检测到屏幕 {i}: {geometry.width()}x{geometry.height()} "
                               f"位置({geometry.x()}, {geometry.y()}) "
                               f"{'主屏' if is_primary else '副屏'}")

            self.logger.info(f"共检测到 {len(self.available_screens)} 个屏幕")

        except Exception as e:
            self.logger.error(f"检测屏幕失败: {str(e)}")

    def enable_dual_screen_mode(self) -> bool:
        """启用双屏模式"""
        try:
            if len(self.available_screens) < 2:
                self.logger.error("需要至少2个屏幕才能启用双屏模式")
                return False

            if self.is_dual_screen_mode:
                self.logger.info("双屏模式已经启用")
                return True

            # 创建主屏内容窗口
            primary_screen = self.available_screens[self.dual_screen_config.primary_screen_id]
            if self.dual_screen_config.primary_content == "courseware":
                self.create_courseware_window(primary_screen.screen_id, primary_screen.geometry)
            else:
                self.create_whiteboard_window(primary_screen.screen_id, primary_screen.geometry)

            # 创建副屏内容窗口
            secondary_screen = self.available_screens[self.dual_screen_config.secondary_screen_id]
            if self.dual_screen_config.secondary_content == "courseware":
                self.create_courseware_window(secondary_screen.screen_id, secondary_screen.geometry)
            else:
                self.create_whiteboard_window(secondary_screen.screen_id, secondary_screen.geometry)

            self.is_dual_screen_mode = True

            # 发送信号
            self.screen_config_changed.emit(self.dual_screen_config)

            # 通知其他模块
            self.broadcast_message("dual_screen_enabled", {
                "config": self.dual_screen_config.to_dict(),
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info("双屏模式已启用")
            return True

        except Exception as e:
            self.logger.error(f"启用双屏模式失败: {str(e)}")
            return False

    def disable_dual_screen_mode(self):
        """禁用双屏模式"""
        try:
            if not self.is_dual_screen_mode:
                self.logger.info("双屏模式未启用")
                return

            # 关闭所有内容窗口
            self.close_all_windows()

            self.is_dual_screen_mode = False

            # 通知其他模块
            self.broadcast_message("dual_screen_disabled", {
                "timestamp": datetime.now().isoformat()
            })

            self.logger.info("双屏模式已禁用")

        except Exception as e:
            self.logger.error(f"禁用双屏模式失败: {str(e)}")

    def create_courseware_window(self, screen_id: int, geometry: QRect):
        """创建课件窗口"""
        try:
            if screen_id in self.courseware_windows:
                self.courseware_windows[screen_id].close()

            courseware_window = CoursewareWindow(geometry)
            # 连接课件打开信号
            courseware_window.courseware_opened.connect(self.on_courseware_opened)
            courseware_window.show()

            self.courseware_windows[screen_id] = courseware_window

            self.logger.info(f"在屏幕 {screen_id} 创建课件窗口")

        except Exception as e:
            self.logger.error(f"创建课件窗口失败: {str(e)}")

    def create_whiteboard_window(self, screen_id: int, geometry: QRect):
        """创建白板窗口"""
        try:
            if screen_id in self.whiteboard_windows:
                self.whiteboard_windows[screen_id].close()

            whiteboard_window = WhiteboardWindow(geometry)
            whiteboard_window.show()

            self.whiteboard_windows[screen_id] = whiteboard_window

            self.logger.info(f"在屏幕 {screen_id} 创建白板窗口")

        except Exception as e:
            self.logger.error(f"创建白板窗口失败: {str(e)}")

    def close_all_windows(self):
        """关闭所有窗口"""
        try:
            # 关闭课件窗口
            for window in self.courseware_windows.values():
                window.close()
            self.courseware_windows.clear()

            # 关闭白板窗口
            for window in self.whiteboard_windows.values():
                window.close()
            self.whiteboard_windows.clear()

        except Exception as e:
            self.logger.error(f"关闭窗口失败: {str(e)}")

    def switch_screen_content(self, screen_id: int, content_type: str):
        """切换屏幕内容"""
        try:
            if screen_id >= len(self.available_screens):
                self.logger.error(f"屏幕ID {screen_id} 无效")
                return

            screen_geometry = self.available_screens[screen_id].geometry

            # 关闭当前屏幕的窗口
            if screen_id in self.courseware_windows:
                self.courseware_windows[screen_id].close()
                del self.courseware_windows[screen_id]

            if screen_id in self.whiteboard_windows:
                self.whiteboard_windows[screen_id].close()
                del self.whiteboard_windows[screen_id]

            # 创建新的内容窗口
            if content_type == "courseware":
                self.create_courseware_window(screen_id, screen_geometry)
            elif content_type == "whiteboard":
                self.create_whiteboard_window(screen_id, screen_geometry)

            # 更新配置
            if screen_id == self.dual_screen_config.primary_screen_id:
                self.dual_screen_config.primary_content = content_type
            elif screen_id == self.dual_screen_config.secondary_screen_id:
                self.dual_screen_config.secondary_content = content_type

            # 发送信号
            self.content_switched.emit(str(screen_id), content_type)

            self.logger.info(f"屏幕 {screen_id} 内容已切换为 {content_type}")

        except Exception as e:
            self.logger.error(f"切换屏幕内容失败: {str(e)}")

    def navigate_to_page(self, page: int):
        """导航到指定页面"""
        try:
            # 更新所有课件窗口的页面
            for window in self.courseware_windows.values():
                window.goto_page(page)

            # 发送信号
            self.page_navigation.emit(page)

            self.logger.info(f"导航到第 {page} 页")

        except Exception as e:
            self.logger.error(f"页面导航失败: {str(e)}")

    def on_courseware_opened(self, courseware_path: str):
        """课件打开事件处理"""
        try:
            self.logger.info(f"课件已打开: {courseware_path}")

            # 通知其他模块课件已打开
            self.broadcast_message("courseware_opened", {
                "path": courseware_path,
                "timestamp": datetime.now().isoformat()
            })

        except Exception as e:
            self.logger.error(f"处理课件打开事件失败: {str(e)}")

    def load_courseware_to_all_windows(self, courseware_path: str):
        """在所有课件窗口中加载课件"""
        try:
            for window in self.courseware_windows.values():
                window.load_courseware(courseware_path)

            self.logger.info(f"已在所有课件窗口中加载: {courseware_path}")

        except Exception as e:
            self.logger.error(f"加载课件到所有窗口失败: {str(e)}")

    def close_courseware_in_all_windows(self):
        """关闭所有课件窗口中的课件"""
        try:
            for window in self.courseware_windows.values():
                window.close_courseware()

            self.logger.info("已关闭所有课件窗口中的课件")

        except Exception as e:
            self.logger.error(f"关闭所有课件失败: {str(e)}")

    def get_screen_status(self) -> Dict[str, Any]:
        """获取屏幕状态"""
        return {
            "available_screens": len(self.available_screens),
            "is_dual_screen_mode": self.is_dual_screen_mode,
            "config": self.dual_screen_config.to_dict(),
            "active_windows": {
                "courseware": len(self.courseware_windows),
                "whiteboard": len(self.whiteboard_windows)
            }
        }

    # 公共接口方法
    def open_courseware(self, courseware_path: str):
        """打开课件（公共接口）"""
        if not self.is_dual_screen_mode:
            self.logger.warning("双屏模式未启用，无法打开课件")
            return False

        try:
            self.load_courseware_to_all_windows(courseware_path)
            return True
        except Exception as e:
            self.logger.error(f"打开课件失败: {str(e)}")
            return False

    def close_courseware(self):
        """关闭课件（公共接口）"""
        if not self.is_dual_screen_mode:
            self.logger.warning("双屏模式未启用")
            return False

        try:
            self.close_courseware_in_all_windows()
            return True
        except Exception as e:
            self.logger.error(f"关闭课件失败: {str(e)}")
            return False

    def clear_all_whiteboards(self):
        """清空所有白板（公共接口）"""
        if not self.is_dual_screen_mode:
            self.logger.warning("双屏模式未启用")
            return False

        try:
            for window in self.whiteboard_windows.values():
                window.clear_whiteboard()

            self.logger.info("已清空所有白板")
            return True
        except Exception as e:
            self.logger.error(f"清空白板失败: {str(e)}")
            return False

    def get_whiteboard_modules(self) -> List:
        """获取所有白板模块实例"""
        modules = []
        for window in self.whiteboard_windows.values():
            module = window.get_whiteboard_module()
            if module:
                modules.append(module)
        return modules

    def send_screen_status(self, requester: str):
        """发送屏幕状态"""
        status = self.get_screen_status()
        self.send_message(requester, "screen_status", status)

    def load_screen_config(self):
        """加载屏幕配置"""
        try:
            import os
            from resource_manager import path_manager
            config_file = path_manager.get_database_path("dual_screen_config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                self.dual_screen_config = DualScreenConfig.from_dict(config_data)
                self.logger.info("屏幕配置加载成功")

        except Exception as e:
            self.logger.error(f"加载屏幕配置失败: {str(e)}")

    def save_screen_config(self):
        """保存屏幕配置"""
        try:
            import os
            config_file = os.path.join(os.path.dirname(__file__), "..", "database", "dual_screen_config.json")
            os.makedirs(os.path.dirname(config_file), exist_ok=True)

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self.dual_screen_config.to_dict(), f, ensure_ascii=False, indent=2)

            self.logger.info("屏幕配置保存成功")

        except Exception as e:
            self.logger.error(f"保存屏幕配置失败: {str(e)}")

class DualScreenControlWindow(QWidget):
    """主副屏控制窗口"""

    def __init__(self, module: 'DualScreenModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("主副屏模式控制")
        self.setGeometry(200, 200, 800, 500)
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'dual_screen.png')))

        self._init_ui()
        self._connect_signals()
        self.update_ui_from_config()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(12)

        # 屏幕检测信息
        detection_group = QGroupBox("屏幕检测")
        detection_layout = QHBoxLayout(detection_group)
        self.screen_info_label = QLabel(f"检测到 {len(self.module.available_screens)} 个屏幕")
        self.refresh_screens_button = QPushButton("重新检测")
        detection_layout.addWidget(self.screen_info_label)
        detection_layout.addStretch()
        detection_layout.addWidget(self.refresh_screens_button)
        main_layout.addWidget(detection_group)

        # 主配置区
        config_group = QGroupBox("双屏配置")
        config_layout = QGridLayout(config_group)

        config_layout.addWidget(QLabel("主屏幕:"), 0, 0)
        self.primary_screen_combo = QComboBox()
        config_layout.addWidget(self.primary_screen_combo, 0, 1)

        config_layout.addWidget(QLabel("主屏内容:"), 0, 2)
        self.primary_content_combo = QComboBox()
        self.primary_content_combo.addItems(["课件", "白板"])
        config_layout.addWidget(self.primary_content_combo, 0, 3)

        config_layout.addWidget(QLabel("副屏幕:"), 1, 0)
        self.secondary_screen_combo = QComboBox()
        config_layout.addWidget(self.secondary_screen_combo, 1, 1)

        config_layout.addWidget(QLabel("副屏内容:"), 1, 2)
        self.secondary_content_combo = QComboBox()
        self.secondary_content_combo.addItems(["白板", "课件"])
        config_layout.addWidget(self.secondary_content_combo, 1, 3)
        
        self.swap_button = QPushButton("交换内容")
        config_layout.addWidget(self.swap_button, 0, 4, 2, 1, Qt.AlignCenter)

        main_layout.addWidget(config_group)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.toggle_mode_button = QPushButton("启用双屏模式")
        self.toggle_mode_button.setCheckable(True)
        control_layout.addStretch()
        control_layout.addWidget(self.toggle_mode_button)
        control_layout.addStretch()
        main_layout.addLayout(control_layout)

        self._populate_screen_combos()
        self._apply_styles()

    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }
            QPushButton {
                padding: 8px 15px;
                border-radius: 4px;
                font-size: 14px;
            }
            #toggle_mode_button[checked="true"] {
                background-color: #dc3545;
                border: 1px solid #dc3545;
                color: white;
            }
            #toggle_mode_button[checked="false"] {
                background-color: #28a745;
                border: 1px solid #28a745;
                color: white;
            }
        """)

    def _connect_signals(self):
        """连接信号和槽"""
        self.refresh_screens_button.clicked.connect(self.refresh_screens)
        self.toggle_mode_button.clicked.connect(self.toggle_dual_screen_mode)
        self.swap_button.clicked.connect(self.swap_content)
        self.module.screen_config_changed.connect(self.update_ui_from_config)

    def _populate_screen_combos(self):
        """填充屏幕选择下拉框"""
        self.primary_screen_combo.clear()
        self.secondary_screen_combo.clear()
        for screen in self.module.available_screens:
            screen_text = f"屏幕 {screen.screen_id} ({screen.width}x{screen.height})"
            if screen.is_primary:
                screen_text += " [主]"
            self.primary_screen_combo.addItem(screen_text, screen.screen_id)
            self.secondary_screen_combo.addItem(screen_text, screen.screen_id)

    @pyqtSlot()
    def update_ui_from_config(self):
        """根据模块配置更新UI"""
        config = self.module.dual_screen_config
        self.primary_screen_combo.setCurrentIndex(self.primary_screen_combo.findData(config.primary_screen_id))
        self.secondary_screen_combo.setCurrentIndex(self.secondary_screen_combo.findData(config.secondary_screen_id))
        self.primary_content_combo.setCurrentText("课件" if config.primary_content == "courseware" else "白板")
        self.secondary_content_combo.setCurrentText("白板" if config.secondary_content == "whiteboard" else "课件")

        self.toggle_mode_button.setChecked(self.module.is_dual_screen_mode)
        self.toggle_mode_button.setText("禁用双屏模式" if self.module.is_dual_screen_mode else "启用双屏模式")

    @pyqtSlot()
    def refresh_screens(self):
        """刷新屏幕列表"""
        self.module.detect_screens()
        self.screen_info_label.setText(f"检测到 {len(self.module.available_screens)} 个屏幕")
        self._populate_screen_combos()
        self.update_ui_from_config()
        QMessageBox.information(self, "刷新成功", f"已重新检测屏幕，共发现 {len(self.module.available_screens)} 个屏幕。")



    @pyqtSlot(bool)
    def toggle_dual_screen_mode(self, checked):
        """切换双屏模式的启用/禁用状态"""
        if checked:
            # 启用前先应用当前UI配置
            primary_id = self.primary_screen_combo.currentData()
            secondary_id = self.secondary_screen_combo.currentData()

            if primary_id == secondary_id:
                QMessageBox.warning(self, "配置错误", "主屏幕和副屏幕不能是同一个显示器。")
                self.toggle_mode_button.setChecked(False)
                return

            # 更新配置
            config = self.module.dual_screen_config
            config.primary_screen_id = primary_id
            config.secondary_screen_id = secondary_id
            config.primary_content = "courseware" if self.primary_content_combo.currentText() == "课件" else "whiteboard"
            config.secondary_content = "whiteboard" if self.secondary_content_combo.currentText() == "白板" else "courseware"

            # 启用双屏模式
            if not self.module.enable_dual_screen_mode():
                QMessageBox.critical(self, "启动失败", "无法启用双屏模式，请检查屏幕数量和配置。")
                self.toggle_mode_button.setChecked(False)
        else:
            self.module.disable_dual_screen_mode()

        self.update_ui_from_config()

    @pyqtSlot()
    def swap_content(self):
        """交换主副屏内容"""
        primary_idx = self.primary_content_combo.currentIndex()
        secondary_idx = self.secondary_content_combo.currentIndex()
        self.primary_content_combo.setCurrentIndex(secondary_idx)
        self.secondary_content_combo.setCurrentIndex(primary_idx)

        # 直接更新配置
        config = self.module.dual_screen_config
        config.primary_content = "courseware" if self.primary_content_combo.currentText() == "课件" else "whiteboard"
        config.secondary_content = "whiteboard" if self.secondary_content_combo.currentText() == "白板" else "courseware"

        self.module.screen_config_changed.emit(config)

        # 如果双屏模式已启用，重新应用配置
        if self.module.is_dual_screen_mode:
            self.module.disable_dual_screen_mode()
            self.module.enable_dual_screen_mode()

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
