"""
广播播放器模块 - 用于小组接收教师屏幕广播
专门用于学生端接收教师通过屏幕广播工具播放的视频流
基于python-vlc实现，提供更好的流媒体播放性能
"""
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                             QLabel, QFrame, QSizePolicy, QMessageBox)
from PyQt5.QtCore import QTimer, pyqtSignal, pyqtSlot, Qt
from PyQt5.QtGui import QFont
import logging
from typing import Dict, Any, Optional
import time
import platform
import sys
import os
import socket
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from urllib.parse import urlparse

from modules.base_module import CommonModule
from network_config import network_config
from vlc_loader import get_vlc, test_vlc

class VLCPlayer(QWidget):
    """基于VLC的播放器组件"""
    
    # 信号定义
    player_status_changed = pyqtSignal(bool, str)  # 播放状态变化
    stream_info_updated = pyqtSignal(dict)  # 流信息更新
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger("vlc_player")

        # VLC模块和实例
        self.vlc = None
        self.vlc_instance = None
        self.vlc_player = None
        self.media = None

        # 播放状态
        self.is_playing = False
        self.current_url = ""

        # 初始化VLC
        self._init_vlc()

        # 设置界面
        self._setup_ui()

        # 状态监控定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._check_player_status)
        self.status_timer.start(1000)  # 每秒检查一次
    
    def _init_vlc(self):
        """初始化VLC"""
        try:
            # 使用VLC加载器动态加载VLC
            self.vlc = get_vlc()
            self.logger.info("VLC模块加载成功")

            # 检查VLC是否可用
            if not test_vlc():
                raise Exception("VLC功能测试失败")

            vlc_options = [
                '--intf=dummy',
                '--no-video-title-show',
                '--network-caching=150',
                '--live-caching=150',
                '--quiet',
                '--no-stats',
                '--no-osd',
                '--no-interact'
            ]

            # 根据操作系统和环境添加特定选项
            if platform.system() == "Windows":
                vlc_options.extend([
                    '--no-crashdump',
                    '--no-plugins-cache'
                ])
            elif platform.system() == "Linux":
                vlc_options.extend([
                    '--no-xlib',
                    '--aout=dummy',  # 禁用音频输出，避免ALSA错误
                    '--no-plugins-cache'
                ])

            # 在打包环境中添加额外选项和插件路径
            if hasattr(sys, '_MEIPASS'):
                vlc_plugin_path = os.path.join(sys._MEIPASS, 'vlc', 'plugins')
                if os.path.exists(vlc_plugin_path):
                    vlc_options.extend([
                        f'--plugin-path={vlc_plugin_path}',
                        '--no-plugins-cache',
                        '--reset-config',
                        '--reset-plugins-cache'
                    ])
                    self.logger.info(f"使用打包的VLC插件路径: {vlc_plugin_path}")
                else:
                    self.logger.warning("打包的VLC插件路径不存在，使用系统默认")
                    vlc_options.extend([
                        '--no-plugins-cache',
                        '--reset-config'
                    ])

            # 创建VLC实例
            self.vlc_instance = self.vlc.Instance(vlc_options)

            if not self.vlc_instance:
                raise Exception("无法创建VLC实例")

            # 创建媒体播放器
            self.vlc_player = self.vlc_instance.media_player_new()

            if not self.vlc_player:
                raise Exception("无法创建VLC播放器")

            # 设置事件管理器
            self.event_manager = self.vlc_player.event_manager()
            self.event_manager.event_attach(self.vlc.EventType.MediaPlayerPlaying, self._on_playing)
            self.event_manager.event_attach(self.vlc.EventType.MediaPlayerStopped, self._on_stopped)
            self.event_manager.event_attach(self.vlc.EventType.MediaPlayerEncounteredError, self._on_error)
            self.event_manager.event_attach(self.vlc.EventType.MediaPlayerEndReached, self._on_end_reached)

            self.logger.info("VLC初始化成功")

        except Exception as e:
            self.logger.error(f"VLC初始化失败: {str(e)}")
            raise

    def _setup_vlc_environment(self):
        """设置VLC环境变量"""
        if hasattr(sys, '_MEIPASS'):
            # 在打包环境中设置VLC相关环境变量
            vlc_plugin_path = os.path.join(sys._MEIPASS, 'vlc', 'plugins')
            if os.path.exists(vlc_plugin_path):
                os.environ['VLC_PLUGIN_PATH'] = vlc_plugin_path
                self.logger.info(f"设置VLC_PLUGIN_PATH: {vlc_plugin_path}")

            # 设置库文件路径
            lib_path = sys._MEIPASS
            if 'LD_LIBRARY_PATH' in os.environ:
                os.environ['LD_LIBRARY_PATH'] = f"{lib_path}:{os.environ['LD_LIBRARY_PATH']}"
            else:
                os.environ['LD_LIBRARY_PATH'] = lib_path

    def _check_vlc_availability(self):
        """检查VLC是否可用"""
        try:
            # 使用VLC加载器测试
            return test_vlc()
        except Exception as e:
            self.logger.warning(f"VLC可用性检查失败: {e}")
            return False
    
    def _setup_ui(self):
        """设置用户界面"""
        self.setMinimumSize(640, 480)
        self.setStyleSheet("background-color: black;")

        self.video_frame = QFrame()
        self.video_frame.setStyleSheet("background-color: black; border: 1px solid #333;")

        # 设置布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.video_frame)
        self.setLayout(layout)

        # 将VLC的输出绑定到video_frame上
        if platform.system() == "Windows":
            self.vlc_player.set_hwnd(int(self.video_frame.winId()))
        elif platform.system() == "Linux":
            self.vlc_player.set_xwindow(int(self.video_frame.winId()))
        elif platform.system() == "Darwin":  # macOS
            self.vlc_player.set_nsobject(int(self.video_frame.winId()))
    
    def play_stream(self, stream_url: str) -> bool:
        """播放流"""
        try:
            if self.is_playing:
                self.stop_stream()
            
            self.current_url = stream_url
            
            # 创建媒体对象
            self.media = self.vlc_instance.media_new(stream_url)
            
            if not self.media:
                self.logger.error("无法创建媒体对象")
                return False
            
            self.media.add_option(":network-caching=150")
            self.media.add_option(":live-caching=150")
            
            # 设置媒体到播放器
            self.vlc_player.set_media(self.media)
            
            # 开始播放
            result = self.vlc_player.play()
            
            if result == 0:  # 0表示成功
                self.is_playing = True
                self.logger.info(f"开始播放流: {stream_url}")
                return True
            else:
                self.logger.error(f"播放失败，错误代码: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"播放流失败: {str(e)}")
            return False
    
    def stop_stream(self):
        """停止播放"""
        try:
            if self.vlc_player and self.is_playing:
                self.vlc_player.stop()
                self.is_playing = False
                self.current_url = ""
                self.logger.info("已停止播放")
                
        except Exception as e:
            self.logger.error(f"停止播放失败: {str(e)}")
    
    def pause_stream(self):
        """暂停播放"""
        try:
            if self.vlc_player and self.is_playing:
                self.vlc_player.pause()
                
        except Exception as e:
            self.logger.error(f"暂停播放失败: {str(e)}")
    
    def resume_stream(self):
        """恢复播放"""
        try:
            if self.vlc_player:
                self.vlc_player.play()
                
        except Exception as e:
            self.logger.error(f"恢复播放失败: {str(e)}")
    
    def get_stream_info(self) -> Dict[str, Any]:
        """获取流信息"""
        info = {
            "url": self.current_url,
            "is_playing": self.is_playing,
            "state": "unknown",
            "position": 0,
            "length": 0
        }
        
        try:
            if self.vlc_player:
                state = self.vlc_player.get_state()
                info["state"] = str(state)
                info["position"] = self.vlc_player.get_position()
                info["length"] = self.vlc_player.get_length()
                
        except Exception as e:
            self.logger.error(f"获取流信息失败: {str(e)}")
        
        return info
    
    def _check_player_status(self):
        """检查播放器状态"""
        if not self.vlc_player:
            return
        
        try:
            state = self.vlc_player.get_state()
            
            # 检查是否有错误
            if state == self.vlc.State.Error:
                self.player_status_changed.emit(False, "播放错误")
                self.is_playing = False
            elif state == self.vlc.State.Ended:
                self.player_status_changed.emit(False, "播放结束")
                self.is_playing = False
            elif state == self.vlc.State.Playing:
                if not self.is_playing:
                    self.is_playing = True
                    self.player_status_changed.emit(True, "正在播放")
                    
                    # 获取流信息
                    info = self.get_stream_info()
                    self.stream_info_updated.emit(info)
            
        except Exception as e:
            self.logger.error(f"检查播放器状态失败: {str(e)}")
    
    def _on_playing(self, event):
        """播放开始事件"""
        self.logger.info("VLC播放开始")
        self.is_playing = True
        self.player_status_changed.emit(True, "正在播放")
    
    def _on_stopped(self, event):
        """播放停止事件"""
        self.logger.info("VLC播放停止")
        self.is_playing = False
        self.player_status_changed.emit(False, "已停止")
    
    def _on_error(self, event):
        """播放错误事件"""
        self.logger.error("VLC播放错误")
        self.is_playing = False
        self.player_status_changed.emit(False, "播放错误")
    
    def _on_end_reached(self, event):
        """播放结束事件"""
        self.logger.info("VLC播放结束")
        self.is_playing = False
        self.player_status_changed.emit(False, "播放结束")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.status_timer:
                self.status_timer.stop()
            
            if self.vlc_player:
                self.vlc_player.stop()
                self.vlc_player.release()
                self.vlc_player = None
            
            if self.vlc_instance:
                self.vlc_instance.release()
                self.vlc_instance = None
            
            self.logger.info("VLC播放器资源已清理")
            
        except Exception as e:
            self.logger.error(f"清理VLC播放器资源失败: {str(e)}")



class BroadcastPlayerModule(CommonModule):
    """广播播放器模块 - 用于小组接收教师屏幕广播"""
    
    # 信号定义
    broadcast_connected = pyqtSignal()  # 广播连接成功
    broadcast_disconnected = pyqtSignal()  # 广播断开连接
    
    def __init__(self):
        super().__init__(
            module_name="broadcast_player",
            display_name="接收教师广播"
        )
        
        # 播放器状态
        self.is_receiving = False
        self.teacher_stream_url = ""
        
        # VLC播放器组件
        self.vlc_player = None
        
        # 自动重连设置
        self.auto_reconnect = True
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self._try_reconnect)
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        
        self.logger = logging.getLogger("broadcast_player")
        
        # UDP发现模块引用
        self.udp_discovery_module = None
        
        # 教师设备缓存
        self._teacher_device = None
        self._last_discovery_time = 0
        
        # 加载配置
        self.config = self._load_config()
        self._discovery_interval = self.config.get("discovery_interval", 60)  # 发现间隔
        
        # 网络质量监控
        self._network_quality = "未知"
        self._last_ping_time = 0
        self._ping_interval = 10  # 10秒ping一次
    
    def _initialize_module(self) -> bool:
        """初始化模块"""
        try:
            self._create_ui()
            self._setup_connections()
            
            # 获取UDP发现模块引用
            self._get_udp_discovery_module()
            
            # 初始化时尝试发现教师设备
            self._discover_teacher_device()
            
            self.logger.info("广播播放器模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"广播播放器模块初始化失败: {str(e)}")
            return False
    
    def _create_ui(self):
        """创建用户界面"""
        self.widget = QWidget()
        self.widget.setWindowTitle("接收教师广播")
        self.widget.resize(800, 600)
        
        layout = QVBoxLayout(self.widget)
        
        # 简单的控制面板
        control_panel = self._create_control_panel()
        layout.addWidget(control_panel)
        
        # VLC播放器区域
        try:
            self.vlc_player = VLCPlayer()
            layout.addWidget(self.vlc_player, 1)

            # 连接VLC播放器信号
            self.vlc_player.player_status_changed.connect(self._on_vlc_status_changed)
            self.vlc_player.stream_info_updated.connect(self._on_stream_info_updated)

        except Exception as e:
            self.logger.error(f"创建VLC播放器失败: {str(e)}")
            self.vlc_player = None

            # 创建错误显示和解决方案
            error_widget = self._create_vlc_error_widget(str(e))
            layout.addWidget(error_widget, 1)
        
        # 状态显示
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("padding: 5px; background-color: #f0f0f0;")
        layout.addWidget(self.status_label)
        
        # 教师地址显示
        self.teacher_address_label = QLabel("教师地址: 未获取")
        self.teacher_address_label.setStyleSheet("padding: 3px; font-size: 12px; color: #666;")
        layout.addWidget(self.teacher_address_label)

    def _create_vlc_error_widget(self, error_message: str) -> QWidget:
        """创建VLC错误提示界面"""
        error_widget = QWidget()
        error_layout = QVBoxLayout(error_widget)
        error_layout.setContentsMargins(20, 20, 20, 20)

        # 错误标题
        title_label = QLabel("VLC播放器初始化失败")
        title_label.setStyleSheet("color: #d32f2f; font-size: 18px; font-weight: bold; margin-bottom: 10px;")
        title_label.setAlignment(Qt.AlignCenter)
        error_layout.addWidget(title_label)

        # 错误详情
        error_label = QLabel(f"错误详情: {error_message}")
        error_label.setStyleSheet("color: #666; font-size: 12px; margin-bottom: 15px;")
        error_label.setAlignment(Qt.AlignCenter)
        error_label.setWordWrap(True)
        error_layout.addWidget(error_label)

        # 解决方案提示
        solution_label = QLabel("""
可能的解决方案：
1. 确保系统已安装VLC媒体播放器
2. 在Linux系统上，尝试安装: sudo apt-get install vlc
3. 在Windows系统上，从官网下载安装VLC
4. 重启应用程序后再试

注意：广播播放功能需要VLC支持，其他功能不受影响。
        """)
        solution_label.setStyleSheet("""
            color: #424242;
            font-size: 11px;
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #ff9800;
        """)
        solution_label.setWordWrap(True)
        error_layout.addWidget(solution_label)

        # 重试按钮
        retry_btn = QPushButton("重试初始化VLC")
        retry_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        retry_btn.clicked.connect(self._retry_vlc_init)
        error_layout.addWidget(retry_btn, 0, Qt.AlignCenter)

        error_layout.addStretch()
        return error_widget

    def _retry_vlc_init(self):
        """重试VLC初始化"""
        try:
            self.logger.info("重试VLC初始化...")

            # 尝试重新创建VLC播放器
            self.vlc_player = VLCPlayer()

            # 连接信号
            self.vlc_player.player_status_changed.connect(self._on_vlc_status_changed)
            self.vlc_player.stream_info_updated.connect(self._on_stream_info_updated)

            # 重新创建界面
            self._recreate_ui_with_vlc()

            self.logger.info("VLC重新初始化成功")
            QMessageBox.information(self.widget, "成功", "VLC播放器初始化成功！")

        except Exception as e:
            self.logger.error(f"VLC重新初始化失败: {str(e)}")
            QMessageBox.critical(self.widget, "失败", f"VLC重新初始化失败: {str(e)}")

    def _recreate_ui_with_vlc(self):
        """重新创建包含VLC的界面"""
        # 清除当前布局
        layout = self.widget.layout()
        if layout:
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()

        # 重新创建界面
        self._create_ui()
    
    def _create_control_panel(self) -> QFrame:
        """创建简单的控制面板"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.StyledPanel)
        layout = QHBoxLayout(frame)
        
        # 连接按钮
        self.connect_btn = QPushButton("开始接收广播")
        self.connect_btn.clicked.connect(self.start_receiving)
        self.connect_btn.setStyleSheet("QPushButton { padding: 8px; font-size: 14px; }")
        layout.addWidget(self.connect_btn)
        
        # 断开按钮
        self.disconnect_btn = QPushButton("停止接收")
        self.disconnect_btn.clicked.connect(self.stop_receiving)
        self.disconnect_btn.setEnabled(False)
        self.disconnect_btn.setStyleSheet("QPushButton { padding: 8px; font-size: 14px; }")
        layout.addWidget(self.disconnect_btn)
        
        layout.addStretch()
        
        # 刷新教师地址按钮
        self.refresh_btn = QPushButton("刷新教师地址")
        self.refresh_btn.clicked.connect(self.refresh_teacher_address_manually)
        self.refresh_btn.setStyleSheet("QPushButton { padding: 8px; }")
        layout.addWidget(self.refresh_btn)
        
        # 自动重连选项
        self.auto_reconnect_btn = QPushButton("自动重连: 开")
        self.auto_reconnect_btn.clicked.connect(self._toggle_auto_reconnect)
        self.auto_reconnect_btn.setStyleSheet("QPushButton { padding: 8px; }")
        layout.addWidget(self.auto_reconnect_btn)
        
        return frame
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块引用"""
        try:
            if self.communication_manager:
                # 通过通信管理器获取UDP发现模块
                modules = getattr(self.communication_manager, 'modules', {})
                self.udp_discovery_module = modules.get('udp_discovery')
                
                if self.udp_discovery_module:
                    self.logger.info("成功获取UDP发现模块引用")
                else:
                    self.logger.warning("未找到UDP发现模块")
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {str(e)}")
    
    def _discover_teacher_device(self) -> Optional[str]:
        """通过UDP发现模块查找教师设备"""
        try:
            current_time = time.time()
            
            # 检查是否需要重新发现
            if (self._teacher_device and 
                current_time - self._last_discovery_time < self._discovery_interval):
                return self._teacher_device.ip_address
            
            # 使用UDP发现模块查找教师设备
            if self.udp_discovery_module:
                teacher_devices = self.udp_discovery_module.get_devices_by_type("teacher")
                
                if teacher_devices:
                    # 选择第一个在线的教师设备
                    for device in teacher_devices:
                        if device.status == "online":
                            self._teacher_device = device
                            self._last_discovery_time = current_time
                            self.logger.info(f"通过UDP发现模块找到教师设备: {device.ip_address}")
                            return device.ip_address
                
                self.logger.debug("UDP发现模块中未找到在线教师设备")
            else:
                self.logger.warning("UDP发现模块不可用，尝试快速网络检测")
                # 如果UDP发现模块不可用，进行快速网络检测
                return self._quick_network_scan()
                
        except Exception as e:
            self.logger.error(f"教师设备发现失败: {str(e)}")
        
        return None
    
    def _quick_network_scan(self) -> Optional[str]:
        """快速网络扫描，仅检查常见教师IP"""
        try:
            import socket
            from concurrent.futures import ThreadPoolExecutor, as_completed
            
            # 获取本地网络段
            local_ip = network_config.get_local_ip()
            if not local_ip or local_ip == "127.0.0.1":
                return None
            
            # 解析网络段 (假设是/24网络)
            ip_parts = local_ip.split('.')
            if len(ip_parts) != 4:
                return None
            
            network_base = '.'.join(ip_parts[:3])
            
            # 获取本机IP，避免扫描自己
            try:
                all_my_ips = {local_ip, "127.0.0.1"}
                all_my_ips.add(socket.gethostbyname(socket.gethostname()))
            except:
                all_my_ips = {local_ip, "127.0.0.1"}

            def check_teacher_service(ip):
                """快速检查指定IP是否有教师服务"""
                if ip in all_my_ips:
                    return None
                try:
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(0.5)  # 500ms超时，更快
                    result = sock.connect_ex((ip, 8554))  # RTSP端口
                    sock.close()
                    
                    if result == 0:
                        return ip
                except:
                    pass
                return None
            
            # 仅扫描常见的教师IP地址，大大减少扫描范围
            priority_ips = [1, 100, 101, 10, 2, 50, 200, 11, 12, 20, 30]
            
            self.logger.debug(f"快速扫描网络段 {network_base}.x，检查IP: {priority_ips}")
            
            # 并发扫描，但限制数量和时间
            found_teacher = None
            with ThreadPoolExecutor(max_workers=5) as executor:  # 减少并发数
                futures = []
                
                # 仅扫描优先IP
                for i in priority_ips:
                    ip = f"{network_base}.{i}"
                    if ip in all_my_ips:
                        continue
                    futures.append(executor.submit(check_teacher_service, ip))
                
                # 快速获取结果，3秒超时
                for future in as_completed(futures, timeout=3):
                    try:
                        result = future.result()
                        if result:
                            found_teacher = result
                            break
                    except:
                        continue
            
            if found_teacher:
                self.logger.info(f"快速扫描发现教师设备: {found_teacher}")
            else:
                self.logger.debug("快速扫描未发现教师设备")
            
            return found_teacher
            
        except Exception as e:
            self.logger.error(f"快速网络扫描失败: {str(e)}")
            return None
    

    
    def _check_network_quality(self, target_ip: str) -> str:
        """检查到教师设备的网络质量"""
        try:
            import subprocess
            import platform
            
            # 根据操作系统选择ping命令
            if platform.system().lower() == "windows":
                cmd = ["ping", "-n", "3", target_ip]
            else:
                cmd = ["ping", "-c", "3", target_ip]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                output = result.stdout.lower()
                
                # 解析延迟信息
                if "time=" in output or "时间=" in output:
                    # 提取平均延迟
                    lines = output.split('\n')
                    for line in lines:
                        if 'avg' in line or '平均' in line:
                            # 尝试提取延迟数值
                            import re
                            delay_match = re.search(r'(\d+\.?\d*)\s*ms', line)
                            if delay_match:
                                delay = float(delay_match.group(1))
                                if delay < 50:
                                    return "优秀"
                                elif delay < 100:
                                    return "良好"
                                elif delay < 200:
                                    return "一般"
                                else:
                                    return "较差"
                    
                    return "良好"  # 如果能ping通但无法解析延迟
                else:
                    return "较差"
            else:
                return "无法连接"
                
        except Exception as e:
            self.logger.debug(f"网络质量检测失败: {str(e)}")
            return "检测失败"
    
    def _update_network_quality(self):
        """更新网络质量显示"""
        try:
            current_time = time.time()
            if current_time - self._last_ping_time < self._ping_interval:
                return
            
            if self.teacher_stream_url:
                parsed = urlparse(self.teacher_stream_url)
                if parsed.hostname and parsed.hostname not in ['localhost', '127.0.0.1']:
                    self._network_quality = self._check_network_quality(parsed.hostname)
                    self._last_ping_time = current_time
                    
                    # 更新显示
                    if hasattr(self, 'teacher_address_label'):
                        display_text = f"教师地址: {parsed.hostname}:{parsed.port} (网络: {self._network_quality})"
                        self.teacher_address_label.setText(display_text)
                        
        except Exception as e:
            self.logger.debug(f"更新网络质量失败: {str(e)}")
    

    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            import yaml
            from pathlib import Path
            from resource_manager import path_manager

            config_path = Path(path_manager.get_config_path("broadcast_player_config.yaml"))
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f) or {}
            else:
                # 返回默认配置
                return {
                    "discovery_interval": 60,  # 发现间隔（秒）
                    "default_teacher_ip": "************",  # 默认教师IP
                    "rtsp_port": 8554,  # RTSP端口
                    "stream_path": "desktop"  # 流路径
                }
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {str(e)}")
            return {
                "discovery_interval": 60,
                "default_teacher_ip": "************",
                "rtsp_port": 8554,
                "stream_path": "desktop"
            }

    def _get_teacher_stream_url(self):
        """获取教师广播流地址"""
        try:
            self.logger.debug("获取教师广播流地址...")
            
            # 首先尝试UDP发现
            teacher_ip = self._discover_teacher_device()
            
            if teacher_ip:
                self.teacher_stream_url = f"rtsp://{teacher_ip}:8554/desktop"
                self.logger.info(f"发现教师设备: {teacher_ip}")
                self._update_address_label()
                return
            
            # 如果UDP发现失败，使用配置中的默认地址
            default_ip = self.config.get("default_teacher_ip", "************")
            rtsp_port = self.config.get("rtsp_port", 8554)
            stream_path = self.config.get("stream_path", "desktop")
            self.teacher_stream_url = f"rtsp://{default_ip}:{rtsp_port}/{stream_path}"
            self.logger.warning(f"未发现教师设备，使用默认地址: {default_ip}")
            if hasattr(self, 'teacher_address_label'):
                self.teacher_address_label.setText(f"教师地址: 使用默认地址 ({default_ip})")

        except Exception as e:
            self.logger.error(f"获取教师广播地址失败: {str(e)}")
            default_ip = self.config.get("default_teacher_ip", "************")
            rtsp_port = self.config.get("rtsp_port", 8554)
            stream_path = self.config.get("stream_path", "desktop")
            self.teacher_stream_url = f"rtsp://{default_ip}:{rtsp_port}/{stream_path}"
            if hasattr(self, 'teacher_address_label'):
                self.teacher_address_label.setText("教师地址: 获取失败，使用默认地址")

    def _update_address_label(self):
        """更新教师地址的UI标签"""
        if not hasattr(self, 'teacher_address_label'):
            return
        
        try:
            self.logger.info(f"最终教师广播地址: {self.teacher_stream_url}")
            parsed = urlparse(self.teacher_stream_url)
            display_address = f"{parsed.hostname}:{parsed.port}"
            self.teacher_address_label.setText(f"教师地址: {display_address}")
        except Exception as e:
            self.logger.error(f"更新地址标签失败: {e}")
            self.teacher_address_label.setText("教师地址: 无效的URL")
    
    def _setup_connections(self):
        """设置信号连接"""
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(2000)  # 每2秒更新一次
        
        # 教师地址刷新定时器 - 仅在未连接时启用，减少资源浪费
        self.address_refresh_timer = QTimer()
        self.address_refresh_timer.timeout.connect(self._periodic_address_refresh)
        # 不自动启动，仅在需要时启动
    
    def start_receiving(self):
        """开始接收教师广播"""
        if self.is_receiving:
            return
        
        # 在开始接收前，尝试刷新教师地址
        if not self.teacher_stream_url:
            self._get_teacher_stream_url()
        
        if not self.teacher_stream_url:
            QMessageBox.warning(self.widget, "警告", "无法获取教师广播地址")
            return
        
        if not self.vlc_player:
            QMessageBox.critical(self.widget, "错误",
                               "VLC播放器未初始化。请确保已安装VLC媒体播放器，然后点击'重试初始化VLC'按钮。")
            return
        
        try:
            # 停止地址刷新定时器，节省资源
            if hasattr(self, 'address_refresh_timer'):
                self.address_refresh_timer.stop()
            
            # 使用VLC播放器播放流
            success = self.vlc_player.play_stream(self.teacher_stream_url)
            
            if success:
                self.is_receiving = True
                self.reconnect_attempts = 0
                
                # 更新UI状态
                self.connect_btn.setEnabled(False)
                self.disconnect_btn.setEnabled(True)
                
                self.status_label.setText("状态: 正在连接...")
                self.logger.info(f"开始接收教师广播: {self.teacher_stream_url}")
            else:
                # 如果播放失败，重新启动地址刷新定时器
                if hasattr(self, 'address_refresh_timer'):
                    self.address_refresh_timer.start(60000)  # 1分钟间隔
                QMessageBox.critical(self.widget, "错误", "无法开始播放教师广播")
            
        except Exception as e:
            self.logger.error(f"开始接收广播失败: {str(e)}")
            # 如果出错，重新启动地址刷新定时器
            if hasattr(self, 'address_refresh_timer'):
                self.address_refresh_timer.start(60000)  # 1分钟间隔
            QMessageBox.critical(self.widget, "错误", f"开始接收广播失败: {str(e)}")
    
    def stop_receiving(self):
        """停止接收教师广播"""
        if not self.is_receiving:
            return
        
        try:
            # 停止重连定时器
            self.reconnect_timer.stop()
            
            if self.vlc_player:
                self.vlc_player.stop_stream()
            
            self.is_receiving = False
            
            # 更新UI状态
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            
            # 更新状态显示
            self.status_label.setText("状态: 已停止")
            
            # 重新启动地址刷新定时器，以便发现新的教师设备
            if hasattr(self, 'address_refresh_timer'):
                self.address_refresh_timer.start(60000)  # 1分钟间隔，比之前的30秒更节省资源
            
            self.logger.info("已停止接收教师广播")
            self.broadcast_disconnected.emit()
            
        except Exception as e:
            self.logger.error(f"停止接收广播失败: {str(e)}")
    
    def _toggle_auto_reconnect(self):
        """切换自动重连状态"""
        self.auto_reconnect = not self.auto_reconnect
        status_text = "开" if self.auto_reconnect else "关"
        self.auto_reconnect_btn.setText(f"自动重连: {status_text}")
        
        if not self.auto_reconnect:
            self.reconnect_timer.stop()
        
        self.logger.info(f"自动重连已{'开启' if self.auto_reconnect else '关闭'}")
    
    def _try_reconnect(self):
        """尝试重连"""
        if not self.auto_reconnect or self.is_receiving:
            self.reconnect_timer.stop()
            return
        
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.reconnect_timer.stop()
            self.status_label.setText("状态: 重连失败，已停止尝试")
            self.logger.warning("达到最大重连次数，停止重连")
            
            # 重新启动地址刷新定时器，以便发现新的教师设备
            if hasattr(self, 'address_refresh_timer'):
                self.address_refresh_timer.start(60000)  # 1分钟间隔
            return
        
        self.reconnect_attempts += 1
        self.status_label.setText(f"状态: 重连中... ({self.reconnect_attempts}/{self.max_reconnect_attempts})")
        self.logger.info(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})")
        
        # 重连时重新获取教师地址
        self._refresh_teacher_address()
        self.start_receiving()
    
    def _refresh_teacher_address(self):
        """刷新教师设备地址"""
        try:
            # 清除设备缓存，强制重新发现
            self._teacher_device = None
            self._last_discovery_time = 0
            
            # 重新获取教师流地址
            self._get_teacher_stream_url()
            
            self.logger.info(f"刷新后的教师广播地址: {self.teacher_stream_url}")
            
        except Exception as e:
            self.logger.error(f"刷新教师地址失败: {str(e)}")
    
    def refresh_teacher_address_manually(self):
        """手动刷新教师地址（供外部调用）"""
        self._refresh_teacher_address()
        
        # 如果正在播放，重新连接
        if self.is_receiving:
            self.stop_receiving()
            QTimer.singleShot(1000, self.start_receiving)  # 1秒后重新连接
    
    def _periodic_address_refresh(self):
        """定期刷新教师地址（仅在未连接时）"""
        try:
            # 只在未接收广播时刷新地址
            if not self.is_receiving:
                current_url = self.teacher_stream_url
                
                # 清除设备缓存，强制重新发现
                self._teacher_device = None
                self._last_discovery_time = 0
                
                # 重新获取教师地址
                self._get_teacher_stream_url()
                
                # 如果地址发生变化，记录日志
                if current_url != self.teacher_stream_url:
                    self.logger.info(f"教师地址已更新: {current_url} -> {self.teacher_stream_url}")
            else:
                # 如果正在接收，停止定时刷新以节省资源
                self.address_refresh_timer.stop()
                self.logger.debug("正在接收广播，停止地址刷新定时器")
                    
        except Exception as e:
            self.logger.error(f"定期地址刷新失败: {str(e)}")
    
    @pyqtSlot(bool, str)
    def _on_vlc_status_changed(self, is_playing: bool, message: str):
        """VLC播放器状态变化处理"""
        if is_playing:
            self.status_label.setText("状态: 已连接教师广播")
            self.status_label.setStyleSheet("color: green; padding: 5px; background-color: #f0f0f0;")
            self.reconnect_attempts = 0
            self.reconnect_timer.stop()
            self.broadcast_connected.emit()
            self.logger.info("成功连接教师广播")
        else:
            self.status_label.setText(f"状态: {message}")
            self.status_label.setStyleSheet("color: red; padding: 5px; background-color: #f0f0f0;")
            
            # 如果启用自动重连且不是手动停止
            if self.auto_reconnect and self.is_receiving and message in ["播放错误", "播放结束"]:
                self.is_receiving = False  # 临时设置为False以允许重连
                self.reconnect_timer.start(3000)  # 3秒后重连
    
    @pyqtSlot(dict)
    def _on_stream_info_updated(self, info: Dict[str, Any]):
        """流信息更新处理"""
        state = info.get('state', 'Unknown')
        url = info.get('url', '')
        if state == "State.Playing":
            self.status_label.setText("状态: 正在播放教师广播")
        else:
            self.status_label.setText(f"状态: {state}")
    
    def _update_status(self):
        """更新状态显示"""
        # 更新网络质量 (暂时禁用，此功能会阻塞UI线程导致视频画面冻结)
        # self._update_network_quality()
        
        if self.vlc_player and self.is_receiving:
            info = self.vlc_player.get_stream_info()
            
            if info.get("is_playing", False):
                state = info.get("state", "unknown")
                if "Playing" in state:
                    self.status_label.setText("状态: 正在播放教师广播")
                    self.status_label.setStyleSheet("color: green; padding: 5px; background-color: #f0f0f0;")
                elif "Error" in state:
                    self.status_label.setText("状态: 播放错误")
                    self.status_label.setStyleSheet("color: red; padding: 5px; background-color: #f0f0f0;")
            else:
                # 检查是否需要重连
                if self.auto_reconnect and self.is_receiving:
                    current_time = time.time()
                    if not hasattr(self, '_last_check_time'):
                        self._last_check_time = current_time
                    elif current_time - self._last_check_time > 10:  # 10秒无播放状态
                        self.status_label.setText("状态: 连接可能中断")
                        self.status_label.setStyleSheet("color: orange; padding: 5px; background-color: #f0f0f0;")
                        self._last_check_time = current_time
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理模块间消息"""
        try:
            if message_type == "start_broadcast":
                # 开始接收广播
                self.start_receiving()
            
            elif message_type == "stop_broadcast":
                # 停止接收广播
                self.stop_receiving()
            
            elif message_type == "get_broadcast_status":
                # 返回广播接收状态
                status = {
                    "is_receiving": self.is_receiving,
                    "stream_url": self.teacher_stream_url,
                    "auto_reconnect": self.auto_reconnect
                }
                
                if self.communication_manager:
                    self.communication_manager.send_message(
                        self.module_name, sender, "broadcast_status_response", status
                    )
            
            elif message_type == "set_auto_reconnect":
                # 设置自动重连
                auto_reconnect = data.get("enabled", True)
                if auto_reconnect != self.auto_reconnect:
                    self._toggle_auto_reconnect()
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {str(e)}")
    
    def get_widget(self) -> QWidget:
        """获取主界面组件"""
        return self.widget
    
    def _cleanup_module(self):
        """清理资源"""
        try:
            if self.is_receiving:
                self.stop_receiving()
            
            if hasattr(self, 'status_timer') and self.status_timer:
                self.status_timer.stop()
            
            if hasattr(self, 'reconnect_timer') and self.reconnect_timer:
                self.reconnect_timer.stop()
            
            if hasattr(self, 'address_refresh_timer') and self.address_refresh_timer:
                self.address_refresh_timer.stop()
            
            if self.vlc_player:
                self.vlc_player.cleanup()
                self.vlc_player = None
            
            self.logger.info("广播播放器模块已清理")
            
        except Exception as e:
            self.logger.error(f"清理广播播放器模块失败: {str(e)}")
    
    def get_module_info(self) -> Dict[str, Any]:
        """获取模块信息"""
        return {
            "name": self.module_name,
            "display_name": self.display_name,
            "version": "1.0.0",
            "description": "用于小组接收教师屏幕广播的播放器",
            "is_receiving": self.is_receiving,
            "stream_url": self.teacher_stream_url,
            "auto_reconnect": self.auto_reconnect
        }