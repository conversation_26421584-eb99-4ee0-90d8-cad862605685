"""
设备控制模块 - 教师专用功能
"""
import sys
import os
import json
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QGroupBox, QTextEdit, QCheckBox, QSpinBox,
                            QMessageBox, QProgressBar, QFrame, QGridLayout,
                            QTableWidget, QTableWidgetItem, QHeaderView,QAbstractItemView,
                            QDialog, QDialogButtonBox, QInputDialog, QProgressDialog)
from PyQt5.QtCore import QTimer, pyqtSignal, Qt, QThread, pyqtSlot, QObject
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter
from .base_module import TeacherModule
from .udp_discovery import DeviceInfo
import logging

# 尝试导入wakeonlan，如果不存在则使用替代方案
try:
    from wakeonlan import send_magic_packet
    WAKEONLAN_AVAILABLE = True
except ImportError:
    WAKEONLAN_AVAILABLE = False
    def send_magic_packet(mac_address):
        """wakeonlan模块不可用时的替代实现"""
        import socket
        import struct

        # 创建魔术包
        mac_bytes = bytes.fromhex(mac_address.replace(':', '').replace('-', ''))
        magic_packet = b'\xff' * 6 + mac_bytes * 16

        # 发送广播包
        sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
        sock.sendto(magic_packet, ('***************', 9))
        sock.close()

class DeviceControlRecord:
    """设备控制记录类"""
    
    def __init__(self, device_id: str, device_name: str, action: str, 
                 result: str, timestamp: datetime = None):
        self.device_id = device_id
        self.device_name = device_name
        self.action = action  # 'wake', 'shutdown', 'restart'
        self.result = result  # 'success', 'failed', 'timeout'
        self.timestamp = timestamp or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "device_id": self.device_id,
            "device_name": self.device_name,
            "action": self.action,
            "result": self.result,
            "timestamp": self.timestamp.isoformat()
        }

class DeviceControlWorker(QObject):
    """设备控制工作线程"""
    
    operation_completed = pyqtSignal(str, str, str)  # device_id, action, result
    progress_updated = pyqtSignal(int)  # progress percentage
    
    def __init__(self):
        super().__init__()
        self.is_running = False
    
    def wake_device(self, device_id: str, mac_address: str, ip_address: str):
        """唤醒设备"""
        try:
            # 发送魔术包
            send_magic_packet(mac_address)
            
            # 等待设备响应
            for i in range(30):  # 等待30秒
                self.progress_updated.emit(int((i + 1) / 30 * 100))
                time.sleep(1)
                
                # 检查设备是否已唤醒
                if self._ping_device(ip_address):
                    self.operation_completed.emit(device_id, "wake", "success")
                    return
            
            # 超时
            self.operation_completed.emit(device_id, "wake", "timeout")
            
        except Exception as e:
            self.operation_completed.emit(device_id, "wake", "failed")
    
    def shutdown_device(self, device_id: str, ip_address: str, username: str = None, password: str = None):
        """关闭设备"""
        try:
            if sys.platform.startswith('win'):
                # Windows系统
                cmd = ['shutdown', '/s', '/m', f'\\\\{ip_address}', '/t', '0']
                if username and password:
                    cmd.extend(['/u', username, '/p', password])
            else:
                # Linux系统
                if username:
                    cmd = ['ssh', f'{username}@{ip_address}', 'sudo shutdown -h now']
                else:
                    cmd = ['ssh', f'user@{ip_address}', 'sudo shutdown -h now']
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # 等待设备关闭
                for i in range(30):
                    self.progress_updated.emit(int((i + 1) / 30 * 100))
                    time.sleep(1)
                    
                    # 检查设备是否已关闭
                    if not self._ping_device(ip_address):
                        self.operation_completed.emit(device_id, "shutdown", "success")
                        return
                
                # 超时
                self.operation_completed.emit(device_id, "shutdown", "timeout")
            else:
                self.operation_completed.emit(device_id, "shutdown", "failed")
                
        except Exception as e:
            self.operation_completed.emit(device_id, "shutdown", "failed")
    
    def restart_device(self, device_id: str, ip_address: str, username: str = None, password: str = None):
        """重启设备"""
        try:
            if sys.platform.startswith('win'):
                # Windows系统
                cmd = ['shutdown', '/r', '/m', f'\\\\{ip_address}', '/t', '0']
                if username and password:
                    cmd.extend(['/u', username, '/p', password])
            else:
                # Linux系统
                if username:
                    cmd = ['ssh', f'{username}@{ip_address}', 'sudo reboot']
                else:
                    cmd = ['ssh', f'user@{ip_address}', 'sudo reboot']
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                self.operation_completed.emit(device_id, "restart", "success")
            else:
                self.operation_completed.emit(device_id, "restart", "failed")
                
        except Exception as e:
            self.operation_completed.emit(device_id, "restart", "failed")
    
    def _ping_device(self, ip_address: str) -> bool:
        """检查设备是否在线"""
        try:
            if sys.platform.startswith('win'):
                cmd = ['ping', '-n', '1', '-w', '1000', ip_address]
            else:
                cmd = ['ping', '-c', '1', '-W', '1', ip_address]
            
            result = subprocess.run(cmd, capture_output=True, timeout=5)
            return result.returncode == 0
        except:
            return False

class DeviceControlModule(TeacherModule):
    """设备控制模块"""
    
    # 模块信号
    operation_completed = pyqtSignal(object)  # DeviceControlRecord
    device_status_changed = pyqtSignal(str, str)  # device_id, status
    
    def __init__(self):
        super().__init__(
            module_name="device_control",
            display_name="设备控制",
            version="2.0.0"
        )
        
        # 设备相关
        self.available_devices: Dict[str, DeviceInfo] = {}
        self.device_credentials: Dict[str, Dict[str, str]] = {}  # 设备认证信息
        self.control_records: List[DeviceControlRecord] = []
        
        # 界面
        self.control_window = None
        
        # 工作线程
        self.control_worker = None
        
        # 配置
        self.config.update({
            "require_confirmation": True,      # 需要操作确认
            "enable_batch_operations": True,   # 启用批量操作
            "operation_timeout": 30,           # 操作超时时间（秒）
            "max_records": 500,               # 最大记录数
            "auto_save_records": True,        # 自动保存记录
            "enable_security_check": True     # 启用安全检查
        })
    
    def get_widget(self):
        return self.control_window

    def _initialize_module(self) -> bool:
        """初始化设备控制模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("设备控制模块需要教师认证")
                return False
            
            # 创建界面
            self.control_window = DeviceControlWindow(self)
            
            # 创建工作线程
            self.control_worker = DeviceControlWorker()
            self.control_worker.operation_completed.connect(self.on_operation_completed)
            self.control_worker.progress_updated.connect(self.on_progress_updated)
            
            # 获取设备发现模块，监听设备变化
            self._setup_device_discovery()
            
            # 加载设备认证信息
            self.load_device_credentials()
            
            # 加载控制记录
            self.load_control_records()
            
            self.logger.info("设备控制模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"设备控制模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理设备控制模块资源"""
        try:
            # 保存控制记录
            if self.get_config("auto_save_records", True):
                self.save_control_records()
            
            # 关闭界面
            if self.control_window:
                self.control_window.close()
                self.control_window = None
            
            # 清理数据
            self.available_devices.clear()
            self.device_credentials.clear()
            self.control_records.clear()
            
            self.logger.info("设备控制模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理设备控制模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "wake_device":
            device_id = data.get("device_id")
            if device_id:
                self.wake_device(device_id)
        
        elif message_type == "shutdown_device":
            device_id = data.get("device_id")
            if device_id:
                self.shutdown_device(device_id)
        
        elif message_type == "restart_device":
            device_id = data.get("device_id")
            if device_id:
                self.restart_device(device_id)
        
        elif message_type == "get_control_status":
            self.send_control_status(sender)
        
        elif message_type == "show_control":
            self.show()
    
    def _setup_device_discovery(self):
        """设置设备发现"""
        try:
            # 获取UDP发现模块
            udp_discovery = self._get_udp_discovery_module()
            if udp_discovery:
                # 连接设备发现信号
                udp_discovery.device_discovered.connect(self.on_device_discovered)
                udp_discovery.device_updated.connect(self.on_device_updated)
                udp_discovery.device_offline.connect(self.on_device_offline)
                
                # 获取当前设备列表
                self.available_devices = udp_discovery.get_discovered_devices()
        except Exception as e:
            self.logger.error(f"设置设备发现失败: {e}")
    
    def _get_udp_discovery_module(self):
        """获取UDP发现模块实例"""
        try:
            if hasattr(self, 'module_manager') and self.module_manager:
                return self.module_manager.get_module_instance("udp_discovery")
            return None
        except Exception as e:
            self.logger.error(f"获取UDP发现模块失败: {e}")
            return None

    def show(self):
        """显示设备控制窗口"""
        if self.control_window:
            self.control_window.show()
            self.control_window.raise_()
            self.control_window.activateWindow()

    def hide(self):
        """隐藏设备控制窗口"""
        if self.control_window:
            self.control_window.hide()

    def close(self):
        """关闭设备控制模块"""
        self.cleanup()

    def wake_device(self, device_id: str) -> bool:
        """唤醒设备"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False

            device = self.available_devices[device_id]

            # 安全检查
            if self.get_config("enable_security_check", True):
                if not self._security_check("wake", device_id):
                    return False

            # 操作确认
            if self.get_config("require_confirmation", True):
                if not self._confirm_operation("唤醒", device.device_name):
                    return False

            # 获取MAC地址
            mac_address = self._get_device_mac(device_id)
            if not mac_address:
                self.logger.error(f"无法获取设备 {device_id} 的MAC地址")
                return False

            # 执行唤醒操作
            self.control_worker.wake_device(device_id, mac_address, device.ip_address)

            self.logger.info(f"开始唤醒设备: {device.device_name}")
            return True

        except Exception as e:
            self.logger.error(f"唤醒设备失败: {str(e)}")
            return False

    def shutdown_device(self, device_id: str) -> bool:
        """关闭设备"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False

            device = self.available_devices[device_id]

            # 安全检查
            if self.get_config("enable_security_check", True):
                if not self._security_check("shutdown", device_id):
                    return False

            # 操作确认
            if self.get_config("require_confirmation", True):
                if not self._confirm_operation("关闭", device.device_name):
                    return False

            # 获取认证信息
            credentials = self.device_credentials.get(device_id, {})
            username = credentials.get("username")
            password = credentials.get("password")

            # 执行关机操作
            self.control_worker.shutdown_device(device_id, device.ip_address, username, password)

            self.logger.info(f"开始关闭设备: {device.device_name}")
            return True

        except Exception as e:
            self.logger.error(f"关闭设备失败: {str(e)}")
            return False

    def restart_device(self, device_id: str) -> bool:
        """重启设备"""
        try:
            if device_id not in self.available_devices:
                self.logger.error(f"设备 {device_id} 不可用")
                return False

            device = self.available_devices[device_id]

            # 安全检查
            if self.get_config("enable_security_check", True):
                if not self._security_check("restart", device_id):
                    return False

            # 操作确认
            if self.get_config("require_confirmation", True):
                if not self._confirm_operation("重启", device.device_name):
                    return False

            # 获取认证信息
            credentials = self.device_credentials.get(device_id, {})
            username = credentials.get("username")
            password = credentials.get("password")

            # 执行重启操作
            self.control_worker.restart_device(device_id, device.ip_address, username, password)

            self.logger.info(f"开始重启设备: {device.device_name}")
            return True

        except Exception as e:
            self.logger.error(f"重启设备失败: {str(e)}")
            return False

    def batch_operation(self, device_ids: List[str], action: str) -> bool:
        """批量操作"""
        try:
            if not self.get_config("enable_batch_operations", True):
                self.logger.error("批量操作已禁用")
                return False

            if not device_ids:
                return False

            # 批量操作确认
            device_names = [self.available_devices[did].device_name
                          for did in device_ids if did in self.available_devices]

            if self.get_config("require_confirmation", True):
                message = f"确定要对以下设备执行{action}操作吗？\n\n" + "\n".join(device_names)
                if not self._confirm_operation_with_message(message):
                    return False

            # 执行批量操作
            success_count = 0
            for device_id in device_ids:
                if action == "wake":
                    if self.wake_device(device_id):
                        success_count += 1
                elif action == "shutdown":
                    if self.shutdown_device(device_id):
                        success_count += 1
                elif action == "restart":
                    if self.restart_device(device_id):
                        success_count += 1

            self.logger.info(f"批量{action}操作完成: {success_count}/{len(device_ids)} 成功")
            return success_count > 0

        except Exception as e:
            self.logger.error(f"批量操作失败: {str(e)}")
            return False

    def _security_check(self, action: str, device_id: str) -> bool:
        """安全检查"""
        try:
            # 检查操作权限
            user_info = self.get_current_user()
            if not user_info or user_info.get("role") != "teacher":
                self.logger.error("只有教师可以执行设备控制操作")
                return False

            # 检查设备类型（只能控制小组设备）
            device = self.available_devices.get(device_id)
            if not device or device.device_type != "group":
                self.logger.error(f"只能控制小组设备，设备 {device_id} 类型: {device.device_type if device else '未知'}")
                return False

            # 检查操作频率限制
            recent_records = [r for r in self.control_records
                            if r.device_id == device_id and
                            (datetime.now() - r.timestamp).total_seconds() < 60]

            if len(recent_records) >= 3:
                self.logger.error(f"设备 {device_id} 操作过于频繁，请稍后再试")
                return False

            return True

        except Exception as e:
            self.logger.error(f"安全检查失败: {str(e)}")
            return False

    def _confirm_operation(self, action: str, device_name: str) -> bool:
        """操作确认"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                None, "确认操作",
                f"确定要{action}设备 {device_name} 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception:
            # 如果无法显示对话框，默认拒绝
            return False

    def _confirm_operation_with_message(self, message: str) -> bool:
        """带自定义消息的操作确认"""
        try:
            from PyQt5.QtWidgets import QMessageBox

            reply = QMessageBox.question(
                None, "确认批量操作", message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            return reply == QMessageBox.Yes

        except Exception:
            return False

    def _get_device_mac(self, device_id: str) -> Optional[str]:
        """获取设备MAC地址"""
        try:
            # 从设备信息中获取MAC地址
            device = self.available_devices.get(device_id)
            if device and hasattr(device, 'mac_address'):
                return device.mac_address

            # 从配置文件中获取
            credentials = self.device_credentials.get(device_id, {})
            return credentials.get("mac_address")

        except Exception as e:
            self.logger.error(f"获取设备MAC地址失败: {str(e)}")
            return None

    def on_operation_completed(self, device_id: str, action: str, result: str):
        """操作完成事件处理"""
        try:
            device_name = "未知设备"
            if device_id in self.available_devices:
                device_name = self.available_devices[device_id].device_name

            # 创建控制记录
            record = DeviceControlRecord(device_id, device_name, action, result)
            self.control_records.append(record)

            # 限制记录数量
            max_records = self.get_config("max_records", 500)
            if len(self.control_records) > max_records:
                self.control_records = self.control_records[-max_records:]

            # 发送信号
            self.operation_completed.emit(record)

            # 更新设备状态
            if result == "success":
                if action == "wake":
                    self.device_status_changed.emit(device_id, "online")
                elif action == "shutdown":
                    self.device_status_changed.emit(device_id, "offline")

            # 通知其他模块
            self.broadcast_message("device_control_completed", {
                "device_id": device_id,
                "action": action,
                "result": result,
                "timestamp": record.timestamp.isoformat()
            })

            self.logger.info(f"设备控制操作完成: {device_name} {action} {result}")

        except Exception as e:
            self.logger.error(f"处理操作完成事件失败: {str(e)}")

    def on_progress_updated(self, progress: int):
        """进度更新事件处理"""
        # 可以在这里更新界面进度条
        pass

    def on_device_discovered(self, device_info: DeviceInfo):
        """设备发现事件处理"""
        self.available_devices[device_info.device_id] = device_info
        self.logger.info(f"发现新设备: {device_info.device_name}")

    def on_device_updated(self, device_info: DeviceInfo):
        """设备更新事件处理"""
        self.available_devices[device_info.device_id] = device_info

    def on_device_offline(self, device_id: str):
        """设备离线事件处理"""
        if device_id in self.available_devices:
            del self.available_devices[device_id]

    def get_available_groups(self) -> List[DeviceInfo]:
        """获取可用的小组设备列表"""
        return [device for device in self.available_devices.values()
                if device.device_type == "group"]

    def get_control_status(self) -> Dict[str, Any]:
        """获取控制状态"""
        return {
            "available_devices": len(self.available_devices),
            "group_devices": len(self.get_available_groups()),
            "total_records": len(self.control_records),
            "recent_operations": len([r for r in self.control_records
                                    if (datetime.now() - r.timestamp).total_seconds() < 3600])
        }

    def send_control_status(self, requester: str):
        """发送控制状态"""
        status = self.get_control_status()
        self.send_message(requester, "control_status", status)

    def load_device_credentials(self):
        """加载设备认证信息"""
        try:
            import os
            credentials_file = os.path.join(os.path.dirname(__file__), "..", "database", "device_credentials.json")

            if os.path.exists(credentials_file):
                with open(credentials_file, 'r', encoding='utf-8') as f:
                    self.device_credentials = json.load(f)

                self.logger.info(f"加载了 {len(self.device_credentials)} 个设备的认证信息")

        except Exception as e:
            self.logger.error(f"加载设备认证信息失败: {str(e)}")

    def save_device_credentials(self):
        """保存设备认证信息"""
        try:
            import os
            credentials_file = os.path.join(os.path.dirname(__file__), "..", "database", "device_credentials.json")
            os.makedirs(os.path.dirname(credentials_file), exist_ok=True)

            with open(credentials_file, 'w', encoding='utf-8') as f:
                json.dump(self.device_credentials, f, ensure_ascii=False, indent=2)

            self.logger.info(f"保存了 {len(self.device_credentials)} 个设备的认证信息")

        except Exception as e:
            self.logger.error(f"保存设备认证信息失败: {str(e)}")

    def load_control_records(self):
        """加载控制记录"""
        try:
            import os
            records_file = os.path.join(os.path.dirname(__file__), "..", "database", "control_records.json")

            if os.path.exists(records_file):
                with open(records_file, 'r', encoding='utf-8') as f:
                    records_data = json.load(f)

                self.control_records.clear()
                for record_data in records_data:
                    record = DeviceControlRecord(
                        record_data["device_id"],
                        record_data["device_name"],
                        record_data["action"],
                        record_data["result"],
                        datetime.fromisoformat(record_data["timestamp"])
                    )
                    self.control_records.append(record)

                self.logger.info(f"加载了 {len(self.control_records)} 条控制记录")

        except Exception as e:
            self.logger.error(f"加载控制记录失败: {str(e)}")

    def save_control_records(self):
        """保存控制记录"""
        try:
            import os
            records_file = os.path.join(os.path.dirname(__file__), "..", "database", "control_records.json")
            os.makedirs(os.path.dirname(records_file), exist_ok=True)

            records_data = [record.to_dict() for record in self.control_records]

            with open(records_file, 'w', encoding='utf-8') as f:
                json.dump(records_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"保存了 {len(self.control_records)} 条控制记录")

        except Exception as e:
            self.logger.error(f"保存控制记录失败: {str(e)}")

class DeviceControlWindow(QWidget):
    """设备控制窗口"""

    def __init__(self, module: 'DeviceControlModule'):
        super().__init__()
        self.module = module
        self.setWindowTitle("设备控制中心")
        self.setGeometry(150, 150, 1200, 800)
        self.setWindowIcon(QIcon(os.path.join(os.path.dirname(__file__), '..', 'assets', 'icons', 'device_control.png')))

        self._init_ui()
        self._connect_signals()
        self.update_device_list()
        self.update_control_records()

    def _init_ui(self):
        """初始化UI界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        self.refresh_button = QPushButton("刷新列表")
        self.select_all_button = QPushButton("全选")
        self.deselect_all_button = QPushButton("取消全选")
        self.wake_all_button = QPushButton("全部唤醒")
        self.shutdown_all_button = QPushButton("全部关闭")
        self.restart_all_button = QPushButton("全部重启")
        
        toolbar_layout.addWidget(self.refresh_button)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.select_all_button)
        toolbar_layout.addWidget(self.deselect_all_button)
        toolbar_layout.addSpacing(20)
        toolbar_layout.addWidget(self.wake_all_button)
        toolbar_layout.addWidget(self.shutdown_all_button)
        toolbar_layout.addWidget(self.restart_all_button)
        main_layout.addLayout(toolbar_layout)

        # 主内容区
        content_layout = QHBoxLayout()
        
        # 左侧：设备列表和控制
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        
        # 设备列表
        device_group = QGroupBox("小组设备列表")
        device_layout = QVBoxLayout(device_group)
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(5)
        self.device_table.setHorizontalHeaderLabels(["", "设备名称", "状态", "IP地址", "MAC地址"])
        self.device_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.device_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.device_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        device_layout.addWidget(self.device_table)
        left_layout.addWidget(device_group)

        # 单设备控制
        control_group = QGroupBox("单设备控制")
        control_layout = QGridLayout(control_group)
        self.wake_button = QPushButton("唤醒选中设备")
        self.shutdown_button = QPushButton("关闭选中设备")
        self.restart_button = QPushButton("重启选中设备")
        self.manage_credentials_button = QPushButton("管理认证信息")
        control_layout.addWidget(self.wake_button, 0, 0)
        control_layout.addWidget(self.shutdown_button, 0, 1)
        control_layout.addWidget(self.restart_button, 1, 0)
        control_layout.addWidget(self.manage_credentials_button, 1, 1)
        left_layout.addWidget(control_group)

        # 右侧：操作日志和状态
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 操作日志
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        self.log_table = QTableWidget()
        self.log_table.setColumnCount(4)
        self.log_table.setHorizontalHeaderLabels(["时间", "设备", "操作", "结果"])
        self.log_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.log_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        log_layout.addWidget(self.log_table)
        right_layout.addWidget(log_group)

        # 状态摘要
        status_group = QGroupBox("状态摘要")
        status_layout = QGridLayout(status_group)
        self.online_label = QLabel("在线设备: 0")
        self.offline_label = QLabel("离线设备: 0")
        self.total_label = QLabel("总设备数: 0")
        status_layout.addWidget(self.online_label, 0, 0)
        status_layout.addWidget(self.offline_label, 0, 1)
        status_layout.addWidget(self.total_label, 1, 0)
        right_layout.addWidget(status_group)

        content_layout.addWidget(left_panel, 2)
        content_layout.addWidget(right_panel, 1)
        main_layout.addLayout(content_layout)

        # 底部状态栏
        self.status_bar = QLabel("准备就绪")
        main_layout.addWidget(self.status_bar)

        self._apply_styles()

    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #ccc;
                border-radius: 5px;
                margin-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 0 5px;
            }
            QTableWidget {
                border: 1px solid #ddd;
                gridline-color: #ddd;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 4px;
                border: 1px solid #ddd;
            }
        """)

    def _connect_signals(self):
        """连接信号和槽"""
        # 模块信号
        self.module.operation_completed.connect(self.on_operation_completed)
        self.module.device_status_changed.connect(self.on_device_status_changed)
        
        # 按钮点击
        self.refresh_button.clicked.connect(self.update_device_list)
        self.select_all_button.clicked.connect(self.select_all_devices)
        self.deselect_all_button.clicked.connect(self.deselect_all_devices)
        
        self.wake_button.clicked.connect(lambda: self.perform_selected_action("wake"))
        self.shutdown_button.clicked.connect(lambda: self.perform_selected_action("shutdown"))
        self.restart_button.clicked.connect(lambda: self.perform_selected_action("restart"))

        self.wake_all_button.clicked.connect(lambda: self.perform_batch_action("wake"))
        self.shutdown_all_button.clicked.connect(lambda: self.perform_batch_action("shutdown"))
        self.restart_all_button.clicked.connect(lambda: self.perform_batch_action("restart"))

        self.device_table.itemSelectionChanged.connect(self.update_control_buttons)

    @pyqtSlot()
    def update_device_list(self):
        """更新设备列表"""
        self.device_table.setRowCount(0)
        devices = self.module.get_available_groups()
        
        online_count = 0
        for device in devices:
            row_position = self.device_table.rowCount()
            self.device_table.insertRow(row_position)

            # 复选框
            chk_box_item = QTableWidgetItem()
            chk_box_item.setFlags(Qt.ItemIsUserCheckable | Qt.ItemIsEnabled)
            chk_box_item.setCheckState(Qt.Unchecked)
            chk_box_item.setData(Qt.UserRole, device.device_id)
            self.device_table.setItem(row_position, 0, chk_box_item)

            # 设备信息
            self.device_table.setItem(row_position, 1, QTableWidgetItem(device.device_name))
            
            status_item = QTableWidgetItem(device.status)
            if device.status == "online":
                status_item.setForeground(QColor("green"))
                online_count += 1
            else:
                status_item.setForeground(QColor("red"))
            self.device_table.setItem(row_position, 2, status_item)
            
            self.device_table.setItem(row_position, 3, QTableWidgetItem(device.ip_address))
            mac = self.module._get_device_mac(device.device_id) or "未知"
            self.device_table.setItem(row_position, 4, QTableWidgetItem(mac))

        self.update_status_summary(online_count, len(devices))
        self.update_control_buttons()
        self.status_bar.setText(f"设备列表已更新，共发现 {len(devices)} 个小组设备。")

    @pyqtSlot()
    def update_control_records(self):
        """更新操作日志"""
        self.log_table.setRowCount(0)
        records = sorted(self.module.control_records, key=lambda r: r.timestamp, reverse=True)
        
        for record in records:
            row_position = self.log_table.rowCount()
            self.log_table.insertRow(row_position)
            
            self.log_table.setItem(row_position, 0, QTableWidgetItem(record.timestamp.strftime("%Y-%m-%d %H:%M:%S")))
            self.log_table.setItem(row_position, 1, QTableWidgetItem(record.device_name))
            self.log_table.setItem(row_position, 2, QTableWidgetItem(record.action))
            
            result_item = QTableWidgetItem(record.result)
            if record.result == "success":
                result_item.setForeground(QColor("green"))
            elif record.result == "failed":
                result_item.setForeground(QColor("red"))
            else:
                result_item.setForeground(QColor("orange"))
            self.log_table.setItem(row_position, 3, result_item)

    @pyqtSlot(object)
    def on_operation_completed(self, record: DeviceControlRecord):
        """操作完成槽函数"""
        self.update_control_records()
        self.status_bar.setText(f"操作完成: {record.device_name} {record.action} -> {record.result}")

    @pyqtSlot(str, str)
    def on_device_status_changed(self, device_id: str, status: str):
        """设备状态变更槽函数"""
        for row in range(self.device_table.rowCount()):
            item = self.device_table.item(row, 0)
            if item and item.data(Qt.UserRole) == device_id:
                status_item = self.device_table.item(row, 2)
                status_item.setText(status)
                if status == "online":
                    status_item.setForeground(QColor("green"))
                else:
                    status_item.setForeground(QColor("red"))
                break
        self.update_device_list() # 重新计算状态

    def get_selected_device_ids(self) -> List[str]:
        """获取选中的设备ID列表"""
        selected_ids = []
        for row in range(self.device_table.rowCount()):
            item = self.device_table.item(row, 0)
            if item and item.checkState() == Qt.Checked:
                selected_ids.append(item.data(Qt.UserRole))
        return selected_ids

    def perform_selected_action(self, action: str):
        """对选中的设备执行操作"""
        device_ids = self.get_selected_device_ids()
        if not device_ids:
            QMessageBox.warning(self, "无选择", "请至少选择一个设备。")
            return

        for device_id in device_ids:
            if action == "wake":
                self.module.wake_device(device_id)
            elif action == "shutdown":
                self.module.shutdown_device(device_id)
            elif action == "restart":
                self.module.restart_device(device_id)
        
        self.status_bar.setText(f"已发送 {action} 指令给 {len(device_ids)} 个设备。")

    def perform_batch_action(self, action: str):
        """执行批量操作"""
        device_ids = [self.device_table.item(row, 0).data(Qt.UserRole) 
                      for row in range(self.device_table.rowCount())]
        if not device_ids:
            QMessageBox.warning(self, "无设备", "设备列表中没有可用设备。")
            return
        
        self.module.batch_operation(device_ids, action)
        self.status_bar.setText(f"已发送批量 {action} 指令。")

    def select_all_devices(self, state=Qt.Checked):
        """全选或取消全选"""
        for row in range(self.device_table.rowCount()):
            item = self.device_table.item(row, 0)
            if item:
                item.setCheckState(state)

    def deselect_all_devices(self):
        self.select_all_devices(Qt.Unchecked)

    def update_control_buttons(self):
        """根据选择更新按钮状态"""
        selected_ids = self.get_selected_device_ids()
        has_selection = bool(selected_ids)
        
        self.wake_button.setEnabled(has_selection)
        self.shutdown_button.setEnabled(has_selection)
        self.restart_button.setEnabled(has_selection)

    def update_status_summary(self, online: int, total: int):
        """更新状态摘要"""
        self.online_label.setText(f"在线设备: {online}")
        self.offline_label.setText(f"离线设备: {total - online}")
        self.total_label.setText(f"总设备数: {total}")

    def closeEvent(self, event):
        """关闭窗口事件"""
        self.hide()
        event.ignore()
