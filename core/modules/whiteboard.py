"""
重构后的白板模块 - 基于新的模块化架构
优化性能和用户体验，支持内容保存和加载
"""
import time
import subprocess
import os
import sys
import platform
import json
from datetime import datetime
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *

from modules.base_module import CommonModule
from typing import Dict, Any, List, Optional
try:
    import qtawesome as fa
except ImportError:
    fa = None

# 延迟导入PPT控制模块，避免循环导入
def get_ppt_control():
    try:
        from modules.ppt_control import PptControl
        return PptControl()
    except ImportError as e:
        print(f"Warning: PPT control module not available: {e}")
        return None

class WhiteboardModule(CommonModule):
    """重构后的白板模块"""

    def __init__(self):
        super().__init__(
            module_name="whiteboard",
            display_name="电子白板",
            version="2.0.0"
        )

        # 白板相关属性
        self.whiteboard_window = None
        self.shapes = []  # 存储所有绘制的形状
        self.undo_stack = []  # 撤销栈
        self.redo_stack = []  # 重做栈
        self.drawing = False
        self.pen_color = QColor(255, 0, 0)
        self.current_shape = "freehand"
        self.fine = 5
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.last_point = QPoint()
        self.isMenuExpanded = True
        self.eraser_points = []
        self.eraser_size = 20
        self.spotlight_radius = 100
        self.magnifier_radius = 100

        # 性能优化相关
        self.drawing_cache = QPixmap()  # 绘图缓存
        self.cache_dirty = True  # 缓存是否需要更新
        self.batch_update_timer = QTimer()  # 批量更新定时器
        self.batch_update_timer.setSingleShot(True)
        self.batch_update_timer.timeout.connect(self._update_cache)

        # 录制相关属性
        self.is_recording = False
        self.recording_process = None
        self.output_file = None
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_timer)
        self.start_time = 0

        # PPT控制
        self.ppt = None

        # 文件保存相关
        from resource_manager import get_save_directory
        self.save_directory = get_save_directory("whiteboard_saves")
        self.auto_save_enabled = False
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save)
        self.auto_save_interval = 30000  # 30秒自动保存
        self.show_save_notifications = False  # 禁用自动保存弹窗

        # 协作相关
        self.collaboration_enabled = False
        self.collaboration_target = None

        # 图标颜色配置
        self.icon_color_mode = "light"  # "auto", "light", "dark"
        self.custom_icon_color = "#FFFFFF"
    
    def get_widget(self):
        return self.whiteboard_window

    def _initialize_module(self) -> bool:
        """初始化白板模块"""
        try:
            # 创建保存目录
            from resource_manager import ensure_directory
            if not ensure_directory(self.save_directory):
                self.logger.warning(f"无法创建保存目录: {self.save_directory}")

            # 创建录制目录
            record_dir = os.path.join(os.path.expanduser("~"), "Desktop")
            try:
                if not os.path.exists(record_dir):
                    os.makedirs(record_dir)
            except Exception as e:
                self.logger.warning(f"无法创建录制目录: {e}")

            # 初始化PPT控制（延迟加载，允许失败）
            try:
                self.ppt = get_ppt_control()
            except Exception as e:
                self.logger.warning(f"PPT控制模块初始化失败: {e}")
                self.ppt = None

            # 创建白板窗口
            try:
                self.whiteboard_window = WhiteboardWindow(self)
            except Exception as e:
                self.logger.error(f"创建白板窗口失败: {e}")
                return False

            # 设置模块配置
            self.config.update({
                "default_pen_color": "#FF0000",
                "default_pen_size": 5,
                "enable_recording": True,
                "auto_save": False,
                "auto_save_interval": 30000,
                "show_save_notifications": False,
                "max_undo_steps": 50,
                "enable_performance_mode": True,
                "enable_collaboration": False
            })

            # 应用配置
            self.show_save_notifications = self.config.get("show_save_notifications", False)

            # 启动自动保存（可选）
            try:
                if self.config.get("auto_save", False):  # 默认关闭自动保存
                    self.auto_save_timer.start(self.config.get("auto_save_interval", 30000))
            except Exception as e:
                self.logger.warning(f"自动保存启动失败: {e}")

            self.logger.info("白板模块初始化成功")
            return True

        except Exception as e:
            import traceback
            self.logger.error(f"白板模块初始化失败: {str(e)}")
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return False
    
    def _cleanup_module(self):
        """清理白板模块资源"""
        try:
            # 停止录制
            if self.is_recording:
                self.stop_recording()

            # 停止自动保存
            if self.auto_save_timer.isActive():
                self.auto_save_timer.stop()

            # 停止批量更新定时器
            if self.batch_update_timer.isActive():
                self.batch_update_timer.stop()

            # 关闭白板窗口
            if self.whiteboard_window:
                self.whiteboard_window.close()
                self.whiteboard_window = None

            # 停止定时器
            if self.timer.isActive():
                self.timer.stop()

            self.logger.info("白板模块资源清理完成")

        except Exception as e:
            self.logger.error(f"清理白板模块资源时出错: {str(e)}")

    def _auto_save(self):
        """自动保存白板内容"""
        if self.whiteboard_window and self.shapes:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.save_directory, f"auto_save_{timestamp}.json")
            self.save_board(filename, show_notification=self.show_save_notifications)
            self.logger.info(f"自动保存完成: {filename}")

    def _update_cache(self):
        """更新绘图缓存"""
        if self.cache_dirty and self.whiteboard_window:
            # 重新绘制缓存
            self.cache_dirty = False
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "ppt_control":
            self.handle_ppt_control(data)
        elif message_type == "drawing_sync":
            self.handle_drawing_sync(data)
        elif message_type == "clear_board":
            self.clear_board()
        elif message_type == "save_board":
            self.save_board(data.get("filename"))
        elif message_type == "load_board":
            self.load_board(data.get("filename"))
    
    def show(self):
        """显示白板"""
        if self.whiteboard_window:
            # 确保窗口设置为全屏
            self.whiteboard_window.setGeometry(self.whiteboard_window.screen_rect)
            self.whiteboard_window.showFullScreen()
            self.whiteboard_window.raise_()
            self.whiteboard_window.activateWindow()

            # 通知其他模块白板已显示
            self.broadcast_message("whiteboard_shown", {
                "timestamp": datetime.now().isoformat()
            })
    
    def hide(self):
        """隐藏白板"""
        if self.whiteboard_window:
            self.whiteboard_window.hide()
            
            # 通知其他模块白板已隐藏
            self.broadcast_message("whiteboard_hidden", {
                "timestamp": datetime.now().isoformat()
            })
    
    def close(self):
        """关闭白板"""
        self.cleanup()
    
    def handle_ppt_control(self, data: Dict[str, Any]):
        """处理PPT控制消息"""
        action = data.get("action")
        if action == "next_slide" and self.ppt:
            self.ppt.next_slide()
        elif action == "prev_slide" and self.ppt:
            self.ppt.prev_slide()
        elif action == "start_slideshow" and self.ppt:
            self.ppt.start_slideshow()
        elif action == "end_slideshow" and self.ppt:
            self.ppt.end_slideshow()
    
    def handle_drawing_sync(self, data: Dict[str, Any]):
        """处理绘图同步消息"""
        if self.whiteboard_window:
            self.whiteboard_window.sync_drawing(data)
    
    def clear_board(self):
        """清空白板"""
        if self.whiteboard_window:
            self.whiteboard_window.clear_all()
            
            # 通知其他模块白板已清空
            self.broadcast_message("board_cleared", {
                "timestamp": datetime.now().isoformat()
            })
    
    def save_board(self, filename: str = None, show_notification: bool = True):
        """保存白板内容"""
        if self.whiteboard_window:
            saved_file = self.whiteboard_window.save_content(filename, show_notification)
            if saved_file:
                # 通知其他模块白板已保存
                self.broadcast_message("board_saved", {
                    "filename": saved_file,
                    "timestamp": datetime.now().isoformat()
                })
                return saved_file
        return None
    
    def load_board(self, filename: str):
        """加载白板内容"""
        if self.whiteboard_window and filename:
            if self.whiteboard_window.load_content(filename):
                # 通知其他模块白板已加载
                self.broadcast_message("board_loaded", {
                    "filename": filename,
                    "timestamp": datetime.now().isoformat()
                })
                return True
        return False
    
    def start_recording(self):
        """开始录制"""
        if not self.is_recording:
            try:
                output_dir = os.path.join(os.path.expanduser("~"), "Desktop")
                if not os.path.exists(output_dir):
                    os.makedirs(output_dir)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                self.output_file = os.path.join(output_dir, f"whiteboard_{timestamp}.flv")

                # 根据操作系统选择 FFmpeg 参数
                if platform.system() == "Windows":
                    command = [
                        'ffmpeg',
                        '-f', 'gdigrab',
                        '-framerate', '30',
                        '-i', 'desktop',
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        self.output_file
                    ]
                    kwargs = {"creationflags": subprocess.CREATE_NO_WINDOW}
                else:  # Linux
                    command = [
                        'ffmpeg',
                        '-f', 'x11grab',
                        '-framerate', '30',
                        '-i', ':0.0',
                        '-c:v', 'libx264',
                        '-preset', 'fast',
                        self.output_file
                    ]
                    kwargs = {}

                self.recording_process = subprocess.Popen(
                    command,
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    **kwargs
                )

                self.is_recording = True
                self.start_time = time.time()
                self.timer.start(1000)

                # 通知其他模块开始录制
                self.broadcast_message("recording_started", {
                    "output_file": self.output_file,
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"开始录制: {self.output_file}")

            except Exception as e:
                self.logger.error(f"启动录制失败: {str(e)}")

    def stop_recording(self):
        """停止录制"""
        if self.is_recording:
            try:
                if self.recording_process:
                    self.recording_process.terminate()
                    self.recording_process.wait()
                    self.recording_process = None

                self.is_recording = False
                self.timer.stop()

                # 通知其他模块停止录制
                self.broadcast_message("recording_stopped", {
                    "output_file": self.output_file,
                    "timestamp": datetime.now().isoformat()
                })

                self.logger.info(f"录制已停止: {self.output_file}")

            except Exception as e:
                self.logger.error(f"停止录制失败: {str(e)}")

    def update_timer(self):
        """更新录制计时器"""
        if self.is_recording and self.whiteboard_window:
            elapsed_time = int(time.time() - self.start_time)
            minutes = elapsed_time // 60
            seconds = elapsed_time % 60
            time_str = f"{minutes:02}:{seconds:02}"
            self.whiteboard_window.update_recording_time(time_str)

    def set_save_notifications(self, enabled: bool):
        """设置是否显示保存通知"""
        self.show_save_notifications = enabled
        self.config["show_save_notifications"] = enabled

    def set_icon_color_mode(self, mode: str, custom_color: str = "#FFFFFF"):
        """设置图标颜色模式

        Args:
            mode: "auto", "light", "dark", "custom"
            custom_color: 自定义颜色（当mode为"custom"时使用）
        """
        self.icon_color_mode = mode
        self.custom_icon_color = custom_color

        # 更新白板窗口的图标颜色
        if self.whiteboard_window:
            self.whiteboard_window.update_icon_colors()

class WhiteboardWindow(QMainWindow):
    """白板窗口类"""

    def __init__(self, module: WhiteboardModule):
        super().__init__()
        self.module = module

        # 获取屏幕尺寸（高DPI适配）
        screen = QApplication.primaryScreen()
        self.screen_rect = screen.availableGeometry()
        self.screen_width = self.screen_rect.width()
        self.screen_height = self.screen_rect.height()

        # 初始化绘图相关属性
        self.pixmap = QPixmap(self.screen_width, self.screen_height)
        self.pixmap.fill(Qt.transparent)

        # 绘图状态
        self.drawing = False
        self.last_point = QPoint()
        self.current_points = []  # 当前绘制的点

        # 设置窗口属性
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 设置窗口几何为全屏
        self.setGeometry(self.screen_rect)

        # 创建UI
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主绘图层
        self.widget_main = QWidget(self)
        self.widget_main.setGeometry(0, 0, self.screen_width, self.screen_height)
        self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")

        # 底部工具菜单
        self.draw_menu = QWidget(self)
        self.draw_menu.setGeometry(
            (self.screen_width - 800) // 2, self.screen_height - 140, 800, 200)
        self.horizontal_layout = QHBoxLayout(self.draw_menu)

        # 创建工具按钮
        self.create_tool_buttons()

        # PPT控制菜单
        self.ppt_menu = QWidget(self)
        self.ppt_menu.setGeometry(
            (self.screen_width - 250), self.screen_height - 140, 250, 200)
        self.ppt_layout = QHBoxLayout(self.ppt_menu)

        # 创建PPT控制按钮
        self.create_ppt_buttons()

        # 应用qtawesome图标
        self.apply_qtawesome_icons()

        # 菜单状态
        self.isMenuExpanded = True
    
    def create_tool_buttons(self):
        """创建工具按钮"""
        # 菜单伸缩按钮
        self.menu_pushButton = QPushButton("", self.draw_menu)
        self.menu_pushButton.setFixedSize(50, 50)
        self.menu_pushButton.clicked.connect(self.toggle_menu)
        self.horizontal_layout.addWidget(self.menu_pushButton)

        # 画笔按钮
        self.plotting_pushButton = QPushButton("", self.draw_menu)
        self.plotting_pushButton.setFixedSize(50, 50)
        self.plotting_pushButton.clicked.connect(self.show_pen_menu)
        self.horizontal_layout.addWidget(self.plotting_pushButton)

        # 画笔菜单
        self.pen_menu = QMenu(self.draw_menu)

        # 添加颜色选择，如果有qtawesome则使用图标
        if fa:
            color_action = self.pen_menu.addAction("选择颜色")
            icon_color = self.get_adaptive_icon_color()
            color_action.setIcon(fa.icon('fa5s.palette', color=icon_color))
            color_action.triggered.connect(self.open_color_dialog)
        else:
            self.pen_menu.addAction("选择颜色", self.open_color_dialog)

        self.width_menu = QMenu("选择笔粗细", self.pen_menu)

        # 添加笔宽选项，如果有qtawesome则使用图标
        if fa:
            icon_color = self.get_adaptive_icon_color()
            for i in range(1, 11):
                width_action = self.width_menu.addAction(f"笔宽 {i}")
                # 根据笔宽大小选择不同的图标
                if i <= 3:
                    width_action.setIcon(fa.icon('fa5s.circle', color=icon_color, scale_factor=0.3 + i*0.1))
                elif i <= 6:
                    width_action.setIcon(fa.icon('fa5s.circle', color=icon_color, scale_factor=0.6 + i*0.05))
                else:
                    width_action.setIcon(fa.icon('fa5s.circle', color=icon_color, scale_factor=0.9))
                width_action.triggered.connect(lambda checked, w=i: self.set_pen_width(w))
        else:
            for i in range(1, 11):
                self.width_menu.addAction(f"笔宽 {i}", lambda w=i: self.set_pen_width(w))

        self.pen_menu.addMenu(self.width_menu)

        self.shape_menu = QMenu("形状选择", self.pen_menu)

        # 添加形状选项，如果有qtawesome则使用图标
        if fa:
            icon_color = self.get_adaptive_icon_color()

            freehand_action = self.shape_menu.addAction("手绘")
            freehand_action.setIcon(fa.icon('fa5s.pencil-alt', color=icon_color))
            freehand_action.triggered.connect(lambda: self.set_shape("freehand"))

            line_action = self.shape_menu.addAction("直线")
            line_action.setIcon(fa.icon('fa5s.minus', color=icon_color))
            line_action.triggered.connect(lambda: self.set_shape("line"))

            rect_action = self.shape_menu.addAction("矩形")
            rect_action.setIcon(fa.icon('fa5s.square', color=icon_color))
            rect_action.triggered.connect(lambda: self.set_shape("rectangle"))

            circle_action = self.shape_menu.addAction("圆形")
            circle_action.setIcon(fa.icon('fa5s.circle', color=icon_color))
            circle_action.triggered.connect(lambda: self.set_shape("circle"))

            triangle_action = self.shape_menu.addAction("三角形")
            triangle_action.setIcon(fa.icon('fa5s.caret-up', color=icon_color))
            triangle_action.triggered.connect(lambda: self.set_shape("triangle"))
        else:
            self.shape_menu.addAction("手绘", lambda: self.set_shape("freehand"))
            self.shape_menu.addAction("直线", lambda: self.set_shape("line"))
            self.shape_menu.addAction("矩形", lambda: self.set_shape("rectangle"))
            self.shape_menu.addAction("圆形", lambda: self.set_shape("circle"))
            self.shape_menu.addAction("三角形", lambda: self.set_shape("triangle"))

        self.pen_menu.addMenu(self.shape_menu)

        # 聚光灯按钮
        self.spotlight_pushButton = QPushButton("", self.draw_menu)
        self.spotlight_pushButton.setFixedSize(50, 50)
        self.spotlight_pushButton.clicked.connect(lambda: self.set_shape("spotlight"))
        self.horizontal_layout.addWidget(self.spotlight_pushButton)

        # 放大镜按钮
        self.magnify_pushButton = QPushButton("", self.draw_menu)
        self.magnify_pushButton.setFixedSize(50, 50)
        self.magnify_pushButton.clicked.connect(lambda: self.set_shape("magnifier"))
        self.horizontal_layout.addWidget(self.magnify_pushButton)

        # 擦除按钮
        self.eraser_pushButton = QPushButton("", self.draw_menu)
        self.eraser_pushButton.setFixedSize(50, 50)
        self.eraser_pushButton.clicked.connect(lambda: self.set_shape("eraser"))
        self.horizontal_layout.addWidget(self.eraser_pushButton)

        # 撤销按钮
        self.revoke_pushButton = QPushButton("", self.draw_menu)
        self.revoke_pushButton.setFixedSize(50, 50)
        self.revoke_pushButton.clicked.connect(self.undo_last_drawing)
        self.horizontal_layout.addWidget(self.revoke_pushButton)

        # 清屏按钮
        self.clear_pushButton = QPushButton("", self.draw_menu)
        self.clear_pushButton.setFixedSize(50, 50)
        self.clear_pushButton.clicked.connect(self.clear_all)
        self.horizontal_layout.addWidget(self.clear_pushButton)

        # 截屏按钮
        self.capture_screen_pushButton = QPushButton("", self.draw_menu)
        self.capture_screen_pushButton.setFixedSize(50, 50)
        self.capture_screen_pushButton.clicked.connect(self.capture_screen)
        self.horizontal_layout.addWidget(self.capture_screen_pushButton)

        # 录屏按钮
        self.record_screen_pushButton = QPushButton("", self.draw_menu)
        self.record_screen_pushButton.setFixedSize(50, 50)
        self.record_screen_pushButton.clicked.connect(self.toggle_recording)
        self.horizontal_layout.addWidget(self.record_screen_pushButton)

        # 保存按钮（隐藏）
        self.save_pushButton = QPushButton("💾", self.draw_menu)
        self.save_pushButton.setFixedSize(50, 50)
        self.save_pushButton.clicked.connect(self.save_content)
        self.save_pushButton.setVisible(False)  # 隐藏按钮
        self.horizontal_layout.addWidget(self.save_pushButton)

        # 加载按钮（隐藏）
        self.load_pushButton = QPushButton("📁", self.draw_menu)
        self.load_pushButton.setFixedSize(50, 50)
        self.load_pushButton.clicked.connect(self.load_content)
        self.load_pushButton.setVisible(False)  # 隐藏按钮
        self.horizontal_layout.addWidget(self.load_pushButton)

        # 设置按钮（隐藏）
        self.settings_pushButton = QPushButton("⚙️", self.draw_menu)
        self.settings_pushButton.setFixedSize(50, 50)
        self.settings_pushButton.clicked.connect(self.show_settings)
        self.settings_pushButton.setVisible(False)  # 隐藏按钮
        self.horizontal_layout.addWidget(self.settings_pushButton)

        # 关闭按钮
        self.close_pushButton = QPushButton("", self.draw_menu)
        self.close_pushButton.setFixedSize(50, 50)
        self.close_pushButton.clicked.connect(self.close)
        self.horizontal_layout.addWidget(self.close_pushButton)
    
    def create_ppt_buttons(self):
        """创建PPT控制按钮"""
        # 获取自适应图标颜色和样式
        if fa:
            icon_color = self.get_adaptive_icon_color()
            base_style = self.get_qtawesome_button_style()

        # PPT开始按钮
        self.ppt_pushButton_start = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_start.setFixedSize(50, 50)
        if fa:
            self.ppt_pushButton_start.setIcon(fa.icon('fa5s.play', color=icon_color))
            self.ppt_pushButton_start.setStyleSheet(base_style)
        self.ppt_pushButton_start.clicked.connect(self.start_ppt)
        self.ppt_layout.addWidget(self.ppt_pushButton_start)

        # PPT下一页按钮
        self.ppt_pushButton_next = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_next.setFixedSize(50, 50)
        if fa:
            self.ppt_pushButton_next.setIcon(fa.icon('fa5s.chevron-right', color=icon_color))
            self.ppt_pushButton_next.setStyleSheet(base_style)
        self.ppt_pushButton_next.clicked.connect(self.next_slide)
        self.ppt_layout.addWidget(self.ppt_pushButton_next)

        # PPT上一页按钮
        self.ppt_pushButton_prev = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_prev.setFixedSize(50, 50)
        if fa:
            self.ppt_pushButton_prev.setIcon(fa.icon('fa5s.chevron-left', color=icon_color))
            self.ppt_pushButton_prev.setStyleSheet(base_style)
        self.ppt_pushButton_prev.clicked.connect(self.prev_slide)
        self.ppt_layout.addWidget(self.ppt_pushButton_prev)

        # PPT停止按钮
        self.ppt_pushButton_stop = QPushButton("", self.ppt_menu)
        self.ppt_pushButton_stop.setFixedSize(50, 50)
        if fa:
            self.ppt_pushButton_stop.setIcon(fa.icon('fa5s.stop', color=icon_color))
            self.ppt_pushButton_stop.setStyleSheet(base_style)
        self.ppt_pushButton_stop.clicked.connect(self.stop_ppt)
        self.ppt_layout.addWidget(self.ppt_pushButton_stop)

    def toggle_menu(self):
        """切换菜单状态"""
        # 切换其他按钮的可见性，但保持某些按钮始终隐藏
        hidden_buttons = [self.save_pushButton, self.load_pushButton, self.settings_pushButton]

        for i in range(1, self.horizontal_layout.count()):
            widget = self.horizontal_layout.itemAt(i).widget()
            if widget in hidden_buttons:
                widget.setVisible(False)  # 始终隐藏这些按钮
            else:
                widget.setVisible(not widget.isVisible())

        if self.isMenuExpanded:
            # 收缩状态：关闭透明层和主图层
            self.widget_main.hide()
            self.setAttribute(Qt.WA_TransparentForMouseEvents, True)

            # 确保收缩按钮保持可点击
            self.menu_pushButton.setAttribute(Qt.WA_TransparentForMouseEvents, False)
            self.menu_pushButton.raise_()

            self.clear_all()
        else:
            # 展开状态：恢复透明层和主图层
            self.widget_main.show()
            self.widget_main.setStyleSheet("background-color: rgba(0, 0, 0, 2)")
            self.setAttribute(Qt.WA_TransparentForMouseEvents, False)

        # 保持窗口置顶
        self.setWindowFlags(self.windowFlags() | Qt.WindowStaysOnTopHint)
        self.show()
        self.isMenuExpanded = not self.isMenuExpanded

    def show_pen_menu(self):
        """显示画笔菜单"""
        pos = self.plotting_pushButton.mapToGlobal(
            self.plotting_pushButton.rect().topLeft())
        pos.setY(pos.y() - self.pen_menu.sizeHint().height())
        self.pen_menu.exec_(pos)

    def open_color_dialog(self):
        """打开颜色选择对话框"""
        color = QColorDialog.getColor(self.module.pen_color, self, "选择颜色")
        if color.isValid():
            self.module.pen_color = color

    def set_pen_width(self, width: int):
        """设置画笔粗细"""
        self.module.fine = width

    def set_shape(self, shape: str):
        """设置当前绘制的形状"""
        self.module.current_shape = shape

    def undo_last_drawing(self):
        """撤销最后一次绘制"""
        if self.module.shapes:
            # 将当前状态保存到重做栈
            self.module.redo_stack.append(self.module.shapes.pop())

            # 重新绘制
            self.pixmap.fill(Qt.transparent)
            painter = QPainter(self.pixmap)
            for shape in self.module.shapes:
                self.draw_shape(painter, shape)
            painter.end()
            self.update()

    def clear_all(self):
        """清空所有内容"""
        self.module.shapes.clear()
        self.module.undo_stack.clear()
        self.module.redo_stack.clear()
        self.pixmap.fill(Qt.transparent)
        self.update()

    def save_content(self, filename: str = None, show_notification: bool = True):
        """保存白板内容"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"whiteboard_{timestamp}"
            filename, _ = QFileDialog.getSaveFileName(
                self, "保存白板内容", default_name,
                "PNG Files (*.png);;JSON Files (*.json);;All Files (*)"
            )

        if filename:
            try:
                if filename.endswith('.json'):
                    # 保存为JSON格式（包含形状数据和图片）
                    # 同时保存图片文件
                    image_filename = filename.replace('.json', '.png')
                    self.pixmap.save(image_filename)

                    data = {
                        "shapes": self.module.shapes,
                        "image_file": image_filename,
                        "timestamp": datetime.now().isoformat(),
                        "version": "2.0.0"
                    }
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                else:
                    # 保存为图片格式
                    self.pixmap.save(filename)

                if show_notification:
                    QMessageBox.information(self, "保存成功", f"白板内容已保存到: {filename}")
                return filename
            except Exception as e:
                if show_notification:
                    QMessageBox.warning(self, "保存失败", f"无法保存白板内容: {str(e)}")
        return None

    def load_content(self, filename: str = None):
        """加载白板内容"""
        if not filename:
            filename, _ = QFileDialog.getOpenFileName(
                self, "加载白板内容", "",
                "JSON Files (*.json);;PNG Files (*.png);;All Files (*)"
            )

        if filename and os.path.exists(filename):
            try:
                if filename.endswith('.json'):
                    # 从JSON格式加载
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    self.module.shapes = data.get("shapes", [])
                    self.redraw_all_shapes()
                else:
                    # 从图片格式加载
                    self.pixmap.load(filename)

                self.update()
                QMessageBox.information(self, "加载成功", f"白板内容已从 {filename} 加载")
                return True
            except Exception as e:
                QMessageBox.warning(self, "加载失败", f"无法加载白板内容: {str(e)}")
        return False

    def redraw_all_shapes(self):
        """重新绘制所有形状"""
        self.pixmap.fill(Qt.transparent)
        painter = QPainter(self.pixmap)
        painter.setRenderHint(QPainter.Antialiasing)

        for shape in self.module.shapes:
            self.draw_shape(painter, shape)

        painter.end()

    def capture_screen(self):
        """截屏功能"""
        try:
            # 修改截屏保存路径为桌面
            desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
            screenshot_folder = os.path.join(desktop_path, "Screenshots")
            if not os.path.exists(screenshot_folder):
                os.makedirs(screenshot_folder)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(screenshot_folder, f"screenshot_{timestamp}.png")

            # 检查截屏工具并使用合适的方法
            if platform.system() == "Linux":
                # 检查是否安装了scrot
                try:
                    result = subprocess.run(["which", "scrot"], capture_output=True, text=True)
                    if result.returncode == 0:
                        # 使用scrot截屏
                        subprocess.run(["scrot", filename], check=True)
                    else:
                        # scrot未安装，尝试使用gnome-screenshot
                        try:
                            subprocess.run(["gnome-screenshot", "-f", filename], check=True)
                        except (subprocess.CalledProcessError, FileNotFoundError):
                            # 如果gnome-screenshot也不可用，使用Qt方法
                            self._qt_screenshot(filename)
                except (subprocess.CalledProcessError, FileNotFoundError):
                    # 如果所有Linux工具都失败，使用Qt方法
                    self._qt_screenshot(filename)
            else:
                # Windows或其他系统使用Qt方法
                self._qt_screenshot(filename)

            QMessageBox.information(self, "截屏成功", f"截图已保存为: {filename}")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"截屏失败: {str(e)}")

    def _qt_screenshot(self, filename: str):
        """使用Qt进行截屏的备用方法"""
        screen = QApplication.primaryScreen()
        screenshot = screen.grabWindow(0)
        screenshot.save(filename)

    def show_settings(self):
        """显示设置对话框"""
        dialog = QDialog(self)
        dialog.setWindowTitle("白板设置")
        dialog.setFixedSize(300, 200)

        layout = QVBoxLayout(dialog)

        # 自动保存弹窗设置
        notification_group = QGroupBox("通知设置")
        notification_layout = QVBoxLayout(notification_group)

        self.notification_checkbox = QCheckBox("显示自动保存通知")
        self.notification_checkbox.setChecked(self.module.show_save_notifications)
        notification_layout.addWidget(self.notification_checkbox)

        layout.addWidget(notification_group)

        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("确定")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(lambda: self.apply_settings(dialog))
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        dialog.exec_()

    def apply_settings(self, dialog):
        """应用设置"""
        self.module.set_save_notifications(self.notification_checkbox.isChecked())
        dialog.accept()
        QMessageBox.information(self, "设置", "设置已保存")

    def get_adaptive_icon_color(self):
        """根据背景色自适应选择图标颜色"""
        try:
            # 检查手动配置
            if hasattr(self.module, 'icon_color_mode'):
                if self.module.icon_color_mode == "light":
                    return "#FFFFFF"
                elif self.module.icon_color_mode == "dark":
                    return "#333333"
                elif self.module.icon_color_mode == "custom":
                    return getattr(self.module, 'custom_icon_color', "#FFFFFF")

            # 自动检测模式
            app = QApplication.instance()
            if app:
                palette = app.palette()

                # 方法1: 通过文本颜色推断背景（最可靠）
                text_color = palette.color(QPalette.WindowText)
                if text_color.isValid():
                    r, g, b = text_color.red(), text_color.green(), text_color.blue()
                    text_brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
                    # 如果文本颜色亮（接近白色），说明背景是深色
                    # 如果文本颜色暗（接近黑色），说明背景是浅色
                    if text_brightness > 0.7:  # 调整阈值，更倾向于白色图标
                        return "#FFFFFF"
                    elif text_brightness < 0.3:  # 明确的深色文本
                        return "#333333"
                    else:
                        # 中等亮度，默认使用白色（适合白板场景）
                        return "#FFFFFF"

                # 方法2: 检查系统窗口背景色
                window_color = palette.color(QPalette.Window)
                if window_color.isValid() and window_color.alpha() > 0:
                    r, g, b = window_color.red(), window_color.green(), window_color.blue()
                    brightness = (0.299 * r + 0.587 * g + 0.114 * b) / 255
                    return "#333333" if brightness > 0.6 else "#FFFFFF"

            # 默认情况：白板通常在深色环境下使用，返回白色图标
            return "#FFFFFF"

        except Exception as e:
            print(f"颜色检测出错: {e}")
            # 默认返回白色
            return "#FFFFFF"

    def get_qtawesome_button_style(self, color=None, hover_color="#FFA500", pressed_color="#06B2A8"):
        """获取qtawesome按钮的基础样式"""
        if color is None:
            color = self.get_adaptive_icon_color()

        return f"""
        QPushButton {{
            background-color: rgba(85,85,85,0);
            border-radius: 5px;
            border: 1px solid rgba(105,105,105,50);
            color: {color};
            font-size: 16px;
            padding: 5px;
        }}
        QPushButton:hover {{
            background-color: rgba(85,85,85,100);
            border: 1px solid rgba(105,105,105,100);
            color: {hover_color};
        }}
        QPushButton:pressed {{
            background-color: rgba(85,85,85,150);
            border: 1px solid rgba(105,105,105,150);
            color: {pressed_color};
        }}
        """

    def apply_qtawesome_icons(self):
        """应用qtawesome图标替换自定义图标"""
        if not fa:
            return  # qtawesome未安装，保持原有图标

        try:
            # 获取自适应图标颜色
            icon_color = self.get_adaptive_icon_color()

            # 基础按钮样式
            base_style = self.get_qtawesome_button_style()

            # 菜单伸缩按钮 - 使用菜单图标
            self.menu_pushButton.setIcon(fa.icon('fa5s.bars', color=icon_color))
            self.menu_pushButton.setStyleSheet(base_style)

            # 画笔按钮 - 使用画笔图标
            self.plotting_pushButton.setIcon(fa.icon('fa5s.paint-brush', color=icon_color))
            self.plotting_pushButton.setStyleSheet(base_style)

            # 聚光灯按钮 - 使用灯泡图标
            self.spotlight_pushButton.setIcon(fa.icon('fa5s.lightbulb', color=icon_color))
            self.spotlight_pushButton.setStyleSheet(base_style)

            # 放大镜按钮 - 使用搜索图标
            self.magnify_pushButton.setIcon(fa.icon('fa5s.search-plus', color=icon_color))
            self.magnify_pushButton.setStyleSheet(base_style)

            # 擦除按钮 - 使用橡皮擦图标
            self.eraser_pushButton.setIcon(fa.icon('fa5s.eraser', color=icon_color))
            self.eraser_pushButton.setStyleSheet(base_style)

            # 撤销按钮 - 使用撤销图标
            self.revoke_pushButton.setIcon(fa.icon('fa5s.undo', color=icon_color))
            self.revoke_pushButton.setStyleSheet(base_style)

            # 清屏按钮 - 使用垃圾桶图标
            self.clear_pushButton.setIcon(fa.icon('fa5s.trash', color=icon_color))
            self.clear_pushButton.setStyleSheet(base_style)

            # 截屏按钮 - 使用相机图标
            self.capture_screen_pushButton.setIcon(fa.icon('fa5s.camera', color=icon_color))
            self.capture_screen_pushButton.setStyleSheet(base_style)

            # 录屏按钮 - 使用录制图标
            self.record_screen_pushButton.setIcon(fa.icon('fa5s.video', color=icon_color))
            # 为录屏按钮设置特殊样式以适应时间显示
            record_style = self.get_qtawesome_button_style()
            record_style = record_style.replace('font-size: 16px;', 'font-size: 10px;')
            record_style = record_style.replace('padding: 5px;', 'padding: 2px;')
            self.record_screen_pushButton.setStyleSheet(record_style)

            # 保存按钮 - 使用保存图标（隐藏）
            self.save_pushButton.setIcon(fa.icon('fa5s.save', color=icon_color))
            self.save_pushButton.setText("")  # 清除emoji文本
            self.save_pushButton.setStyleSheet(base_style)
            self.save_pushButton.setVisible(False)  # 确保隐藏

            # 加载按钮 - 使用文件夹图标（隐藏）
            self.load_pushButton.setIcon(fa.icon('fa5s.folder-open', color=icon_color))
            self.load_pushButton.setText("")  # 清除emoji文本
            self.load_pushButton.setStyleSheet(base_style)
            self.load_pushButton.setVisible(False)  # 确保隐藏

            # 设置按钮 - 使用齿轮图标（隐藏）
            self.settings_pushButton.setIcon(fa.icon('fa5s.cog', color=icon_color))
            self.settings_pushButton.setText("")  # 清除emoji文本
            self.settings_pushButton.setStyleSheet(base_style)
            self.settings_pushButton.setVisible(False)  # 确保隐藏

            # 关闭按钮 - 使用关闭图标
            self.close_pushButton.setIcon(fa.icon('fa5s.times', color=icon_color))
            self.close_pushButton.setStyleSheet(base_style)

            # PPT控制按钮（检查是否存在）
            if hasattr(self, 'ppt_pushButton_start') and self.ppt_pushButton_start:
                # PPT开始按钮 - 使用播放图标
                self.ppt_pushButton_start.setIcon(fa.icon('fa5s.play', color=icon_color))
                self.ppt_pushButton_start.setStyleSheet(base_style)

            if hasattr(self, 'ppt_pushButton_next') and self.ppt_pushButton_next:
                # PPT下一页按钮 - 使用右箭头图标
                self.ppt_pushButton_next.setIcon(fa.icon('fa5s.chevron-right', color=icon_color))
                self.ppt_pushButton_next.setStyleSheet(base_style)

            if hasattr(self, 'ppt_pushButton_prev') and self.ppt_pushButton_prev:
                # PPT上一页按钮 - 使用左箭头图标
                self.ppt_pushButton_prev.setIcon(fa.icon('fa5s.chevron-left', color=icon_color))
                self.ppt_pushButton_prev.setStyleSheet(base_style)

            if hasattr(self, 'ppt_pushButton_stop') and self.ppt_pushButton_stop:
                # PPT停止按钮 - 使用停止图标
                self.ppt_pushButton_stop.setIcon(fa.icon('fa5s.stop', color=icon_color))
                self.ppt_pushButton_stop.setStyleSheet(base_style)

        except Exception as e:
            print(f"应用qtawesome图标时出错: {e}")
            # 如果出错，保持原有的自定义图标

    def update_icon_colors(self):
        """更新所有图标颜色以适应当前背景"""
        if not fa:
            return

        try:
            # 重新应用所有图标
            self.apply_qtawesome_icons()
        except Exception as e:
            print(f"更新图标颜色时出错: {e}")

    def toggle_recording(self):
        """切换录制状态"""
        if self.module.is_recording:
            self.module.stop_recording()
            if fa:
                # 获取自适应图标颜色
                icon_color = self.get_adaptive_icon_color()
                self.record_screen_pushButton.setIcon(fa.icon('fa5s.video', color=icon_color))
                # 恢复正常样式并清除时间文本
                record_style = self.get_qtawesome_button_style()
                record_style = record_style.replace('font-size: 16px;', 'font-size: 10px;')
                record_style = record_style.replace('padding: 5px;', 'padding: 2px;')
                self.record_screen_pushButton.setStyleSheet(record_style)
                self.record_screen_pushButton.setText("")  # 清除时间显示
        else:
            self.module.start_recording()
            if fa:
                self.record_screen_pushButton.setIcon(fa.icon('fa5s.stop-circle', color='red'))
                # 录制时的样式
                record_style = self.get_qtawesome_button_style(color='red')
                record_style = record_style.replace('font-size: 16px;', 'font-size: 10px;')
                record_style = record_style.replace('padding: 5px;', 'padding: 2px;')
                self.record_screen_pushButton.setStyleSheet(record_style)

    def update_recording_time(self, time_str: str):
        """更新录制时间显示"""
        if hasattr(self, 'record_screen_pushButton') and self.module.is_recording:
            # 只在录制时显示时间，并且使用较小的字体
            self.record_screen_pushButton.setText(time_str)

    # PPT控制方法
    def activate_ppt_window(self):
        """激活PPT窗口"""
        try:
            if platform.system() == "Linux":
                result = subprocess.run(
                    ["wmctrl", "-l"],
                    capture_output=True,
                    text=True
                )
                windows = result.stdout.splitlines()
                ppt_windows = [w for w in windows if "WPS Presentation" in w or "LibreOffice Impress" in w]

                if ppt_windows:
                    window_id = ppt_windows[0].split()[0]
                    subprocess.run(["wmctrl", "-i", "-a", window_id])
                    subprocess.run(["xdotool", "windowfocus", window_id])
                else:
                    QMessageBox.warning(self, "警告", "未检测到 PPT 窗口")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"激活窗口失败: {str(e)}")

    def start_ppt(self):
        """开始放映PPT"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "F5"])
        else:
            # Windows下的实现
            pass

    def prev_slide(self):
        """上一页"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Left"])

    def next_slide(self):
        """下一页"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Right"])

    def stop_ppt(self):
        """结束放映"""
        self.activate_ppt_window()
        if platform.system() == "Linux":
            subprocess.run(["xdotool", "key", "Escape"])

    def draw_shape(self, painter: QPainter, shape: Dict[str, Any]):
        """绘制单个形状"""
        if not isinstance(shape, dict):
            return

        pen = QPen(QColor(shape.get("color", "#FF0000")),
                  shape.get("fine", 5), Qt.SolidLine, Qt.RoundCap)
        painter.setPen(pen)

        shape_type = shape.get("shape", "freehand")

        if shape_type == "line":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            painter.drawLine(start, end)
        elif shape_type == "rectangle":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            rect = QRect(start, end)
            painter.drawRect(rect)
        elif shape_type == "triangle":
            points = shape.get("points", [])
            if len(points) >= 3:
                qpoints = [QPoint(p["x"], p["y"]) for p in points]
                polygon = QPolygon(qpoints)
                painter.drawPolygon(polygon)
        elif shape_type == "circle":
            start = QPoint(shape["start"]["x"], shape["start"]["y"])
            end = QPoint(shape["end"]["x"], shape["end"]["y"])
            radius = int((end - start).manhattanLength() / 2)
            center = start
            painter.drawEllipse(center, radius, radius)
        elif shape_type == "freehand":
            points = shape.get("points", [])
            if len(points) > 1:
                for i in range(1, len(points)):
                    p1 = QPoint(points[i-1]["x"], points[i-1]["y"])
                    p2 = QPoint(points[i]["x"], points[i]["y"])
                    painter.drawLine(p1, p2)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drawing = True
            self.module.start_point = event.pos()
            self.last_point = event.pos()

            if self.module.current_shape == "freehand":
                self.current_points = [{"x": event.pos().x(), "y": event.pos().y()}]
            elif self.module.current_shape == "eraser":
                self.module.eraser_points = [event.pos()]

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.drawing and event.buttons() & Qt.LeftButton:
            self.module.end_point = event.pos()

            if self.module.current_shape == "freehand":
                self.current_points.append({"x": event.pos().x(), "y": event.pos().y()})
            elif self.module.current_shape == "eraser":
                # 在pixmap上擦除
                painter = QPainter(self.pixmap)
                painter.setCompositionMode(QPainter.CompositionMode_Clear)
                painter.setPen(QPen(Qt.transparent, self.module.eraser_size, Qt.SolidLine, Qt.RoundCap))
                painter.drawLine(self.last_point, event.pos())
                painter.end()
                self.last_point = event.pos()
            elif self.module.current_shape == "spotlight":
                self.last_point = event.pos()
            elif self.module.current_shape == "magnifier":
                self.last_point = event.pos()

            self.update()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton and self.drawing:
            self.drawing = False
            shape = None

            if self.module.current_shape == "line":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "rectangle":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "triangle":
                mid_x = (self.module.start_point.x() + self.module.end_point.x()) / 2
                side_length = abs(self.module.end_point.x() - self.module.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    {"x": self.module.start_point.x(), "y": self.module.end_point.y()},
                    {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    {"x": int(mid_x), "y": self.module.end_point.y() - height}
                ]
                shape = {
                    "shape": self.module.current_shape,
                    "points": points,
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "circle":
                shape = {
                    "shape": self.module.current_shape,
                    "start": {"x": self.module.start_point.x(), "y": self.module.start_point.y()},
                    "end": {"x": self.module.end_point.x(), "y": self.module.end_point.y()},
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }
            elif self.module.current_shape == "freehand":
                shape = {
                    "shape": self.module.current_shape,
                    "points": self.current_points[:],
                    "color": self.module.pen_color.name(),
                    "fine": self.module.fine
                }

            if shape:
                self.module.shapes.append(shape)
                # 将形状绘制到pixmap中
                painter = QPainter(self.pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                self.draw_shape(painter, shape)
                painter.end()

                # 通知协作模块
                if self.module.collaboration_enabled:
                    self.module.broadcast_message("drawing_sync", {
                        "action": "add_shape",
                        "shape": shape,
                        "timestamp": datetime.now().isoformat()
                    })

            self.update()

    def paintEvent(self, event):
        """绘制事件"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(0, 0, self.pixmap)

        # 绘制当前实时预览形状
        if self.drawing:
            pen = QPen(self.module.pen_color, self.module.fine, Qt.SolidLine, Qt.RoundCap)
            painter.setPen(pen)

            if self.module.current_shape == "line":
                painter.drawLine(self.module.start_point, self.module.end_point)
            elif self.module.current_shape == "rectangle":
                rect = QRect(self.module.start_point, self.module.end_point)
                painter.drawRect(rect)
            elif self.module.current_shape == "triangle":
                mid_x = (self.module.start_point.x() + self.module.end_point.x()) / 2
                side_length = abs(self.module.end_point.x() - self.module.start_point.x())
                height = int((3 ** 0.5 / 2) * side_length)
                points = [
                    QPoint(self.module.start_point.x(), self.module.end_point.y()),
                    QPoint(self.module.end_point.x(), self.module.end_point.y()),
                    QPoint(int(mid_x), self.module.end_point.y() - height)
                ]
                painter.drawPolygon(QPolygon(points))
            elif self.module.current_shape == "circle":
                radius = int((self.module.end_point - self.module.start_point).manhattanLength() / 2)
                center = self.module.start_point
                painter.drawEllipse(center, radius, radius)
            elif self.module.current_shape == "freehand" and hasattr(self, 'current_points') and len(self.current_points) > 1:
                for i in range(1, len(self.current_points)):
                    p1 = QPoint(self.current_points[i-1]["x"], self.current_points[i-1]["y"])
                    p2 = QPoint(self.current_points[i]["x"], self.current_points[i]["y"])
                    painter.drawLine(p1, p2)

            # 聚光灯效果
            elif self.module.current_shape == "spotlight":
                gradient = QRadialGradient(self.last_point, self.module.spotlight_radius)
                gradient.setColorAt(0, QColor(255, 255, 255, 150))
                gradient.setColorAt(1, QColor(0, 0, 0, 150))
                painter.setBrush(QBrush(gradient))
                painter.setPen(Qt.NoPen)
                painter.drawEllipse(self.last_point, self.module.spotlight_radius, self.module.spotlight_radius)

            # 放大镜效果
            elif self.module.current_shape == "magnifier":
                magnified_rect = QRect(self.last_point.x() - self.module.magnifier_radius,
                                     self.last_point.y() - self.module.magnifier_radius,
                                     self.module.magnifier_radius * 2,
                                     self.module.magnifier_radius * 2)
                magnified_pixmap = self.pixmap.copy(magnified_rect).scaled(magnified_rect.size() * 2)
                painter.drawPixmap(magnified_rect.topLeft(), magnified_pixmap)
                painter.setPen(QPen(Qt.black, 2))
                painter.drawEllipse(self.last_point, self.module.magnifier_radius, self.module.magnifier_radius)

    def resizeEvent(self, event):
        """窗口大小调整事件"""
        new_pixmap = QPixmap(self.size())
        new_pixmap.fill(Qt.transparent)
        painter = QPainter(new_pixmap)
        painter.drawPixmap(0, 0, self.pixmap)
        painter.end()
        self.pixmap = new_pixmap

    def sync_drawing(self, data: Dict[str, Any]):
        """同步绘图数据"""
        action = data.get("action")
        if action == "add_shape":
            shape = data.get("shape")
            if shape:
                self.module.shapes.append(shape)
                painter = QPainter(self.pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                self.draw_shape(painter, shape)
                painter.end()
                self.update()
        elif action == "clear_all":
            self.clear_all()
        elif action == "undo":
            self.undo_last_drawing()

# 为了兼容性，保留原始类名
Whiteboard = WhiteboardModule