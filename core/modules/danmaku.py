"""
重构后的弹幕模块 - 基于新的模块化架构（教师专用）
"""
import sys
import socketio
import requests
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget, QPushButton, QTextEdit, QHBoxLayout
from PyQt5.QtCore import QTimer, Qt, pyqtSignal, QObject, QThread
from PyQt5.QtGui import QFont, QColor
from .base_module import TeacherModule
from typing import Dict, Any, List
import json
import logging

class DanmakuSignal(QObject):
    """弹幕信号类"""
    new_danmaku = pyqtSignal(str, dict)  # 新弹幕信号
    connection_status = pyqtSignal(bool)  # 连接状态信号

class DanmakuModule(TeacherModule):
    """重构后的弹幕模块"""
    
    def __init__(self):
        super().__init__(
            module_name="danmaku",
            display_name="弹幕系统",
            version="2.0.0"
        )
        
        # 弹幕相关属性
        self.danmaku_window = None
        self.sio = None
        self.danmaku_labels = []
        self.danmaku_speed = 5
        self.signals = DanmakuSignal()
        self.api_client = None
        self.connection_thread = None
        
        # 弹幕数据
        self.danmaku_history: List[Dict[str, Any]] = []
        self.active_danmaku: List[Dict[str, Any]] = []
        
        # 连接信号
        self.signals.new_danmaku.connect(self.create_danmaku)
        self.signals.connection_status.connect(self.on_connection_status_changed)
    
    def get_widget(self):
        return self.danmaku_window

    def _initialize_module(self) -> bool:
        """初始化弹幕模块"""
        try:
            # 检查认证状态
            if not self.is_authenticated():
                self.logger.error("弹幕模块需要教师认证")
                return False
            
            # 创建弹幕窗口
            self.danmaku_window = DanmakuWindow(self)
            
            # 初始化Socket.IO客户端
            self.init_socketio_client()
            
            # 设置模块配置
            self.config.update({
                "server_url": "https://localhost:5000",
                "auto_connect": True,
                "danmaku_speed": 5,
                "max_danmaku_count": 50,
                "enable_filter": True
            })
            
            # 启动定时器
            self.timer = QTimer()
            self.timer.timeout.connect(self.update_danmaku)
            self.timer.start(30)  # 30ms更新一次
            
            self.logger.info("弹幕模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"弹幕模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理弹幕模块资源"""
        try:
            # 停止定时器
            if hasattr(self, 'timer') and self.timer.isActive():
                self.timer.stop()
            
            # 断开Socket.IO连接
            if self.sio and self.sio.connected:
                self.sio.disconnect()
            
            # 关闭弹幕窗口
            if self.danmaku_window:
                self.danmaku_window.close()
                self.danmaku_window = None
            
            # 清理弹幕数据
            self.danmaku_labels.clear()
            self.danmaku_history.clear()
            self.active_danmaku.clear()
            
            self.logger.info("弹幕模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理弹幕模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "send_danmaku":
            self.send_danmaku(data.get("content", ""), data.get("user_info", {}))
        elif message_type == "clear_danmaku":
            self.clear_all_danmaku()
        elif message_type == "set_danmaku_speed":
            self.set_danmaku_speed(data.get("speed", 5))
        elif message_type == "toggle_danmaku":
            self.toggle_danmaku_display(data.get("enabled", True))
        elif message_type == "get_danmaku_stats":
            self.send_danmaku_stats(sender)
    
    def init_socketio_client(self):
        """初始化Socket.IO客户端"""
        try:
            # 创建一个自定义的requests session来忽略SSL验证
            session = requests.Session()
            session.verify = False
            
            self.sio = socketio.Client(http_session=session, logger=True, engineio_logger=True)
            
            @self.sio.event
            def connect():
                self.logger.info("Socket.IO连接成功")
                self.signals.connection_status.emit(True)
                
                # 加入教师房间
                user_info = self.get_current_user()
                if user_info:
                    user_id = user_info.get('id', user_info.get('username'))
                    self.sio.emit('join', {'room': f'teacher_{user_id}'})
                    self.logger.info(f"已加入教师房间: teacher_{user_id}")
                
                # 通知其他模块连接成功
                self.broadcast_message("danmaku_connected", {
                    "timestamp": self._get_timestamp()
                })
            
            @self.sio.event
            def disconnect():
                self.logger.info("Socket.IO连接断开")
                self.signals.connection_status.emit(False)
                
                # 通知其他模块连接断开
                self.broadcast_message("danmaku_disconnected", {
                    "timestamp": self._get_timestamp()
                })
            
            @self.sio.on('student_danmaku')
            def on_student_danmaku(data):
                """处理学生弹幕"""
                self.logger.debug(f"收到学生弹幕: {data}")
                content = data.get('content', '')
                user_info = data.get('user_info', {})
                
                # 过滤弹幕内容
                if self.filter_danmaku(content):
                    self.signals.new_danmaku.emit(content, user_info)
                    
                    # 记录弹幕历史
                    self.danmaku_history.append({
                        "content": content,
                        "user_info": user_info,
                        "timestamp": self._get_timestamp()
                    })
                    
                    # 限制历史记录数量
                    max_history = self.get_config("max_danmaku_count", 50)
                    if len(self.danmaku_history) > max_history:
                        self.danmaku_history = self.danmaku_history[-max_history:]
            
            @self.sio.on('student_interaction')
            def on_student_interaction(data):
                """处理学生互动"""
                self.logger.debug(f"收到学生互动: {data}")
                
                # 转发给其他模块
                self.broadcast_message("student_interaction", data)
            
            # 连接到服务器
            server_url = self.get_config("server_url", "https://localhost:5000")
            if self.get_config("auto_connect", True):
                self.connect_to_server(server_url)
                
        except Exception as e:
            self.logger.error(f"初始化Socket.IO客户端失败: {str(e)}")
    
    def connect_to_server(self, server_url: str):
        """连接到服务器"""
        try:
            if self.sio and not self.sio.connected:
                self.sio.connect(server_url)
        except Exception as e:
            self.logger.error(f"连接服务器失败: {str(e)}")
            self.signals.connection_status.emit(False)
    
    def disconnect_from_server(self):
        """断开服务器连接"""
        try:
            if self.sio and self.sio.connected:
                self.sio.disconnect()
        except Exception as e:
            self.logger.error(f"断开连接失败: {str(e)}")
    
    def show(self):
        """显示弹幕窗口"""
        if self.danmaku_window:
            self.danmaku_window.showFullScreen()
            
            # 通知其他模块弹幕窗口已显示
            self.broadcast_message("danmaku_shown", {
                "timestamp": self._get_timestamp()
            })
    
    def hide(self):
        """隐藏弹幕窗口"""
        if self.danmaku_window:
            self.danmaku_window.hide()
            
            # 通知其他模块弹幕窗口已隐藏
            self.broadcast_message("danmaku_hidden", {
                "timestamp": self._get_timestamp()
            })
    
    def close(self):
        """关闭弹幕模块"""
        self.cleanup()
    
    def create_danmaku(self, content: str, user_info: Dict[str, Any]):
        """创建弹幕"""
        if self.danmaku_window:
            self.danmaku_window.add_danmaku(content, user_info)
    
    def send_danmaku(self, content: str, user_info: Dict[str, Any]):
        """发送弹幕（教师发送）"""
        if self.sio and self.sio.connected:
            try:
                teacher_info = self.get_current_user()
                danmaku_data = {
                    "content": content,
                    "user_info": {
                        "name": teacher_info.get("name", "教师"),
                        "role": "teacher",
                        "id": teacher_info.get("id", "teacher")
                    },
                    "timestamp": self._get_timestamp()
                }
                
                self.sio.emit('teacher_danmaku', danmaku_data)
                
                # 在本地也显示
                self.signals.new_danmaku.emit(content, danmaku_data["user_info"])
                
                self.logger.info(f"教师弹幕已发送: {content}")
                
            except Exception as e:
                self.logger.error(f"发送弹幕失败: {str(e)}")
    
    def clear_all_danmaku(self):
        """清空所有弹幕"""
        if self.danmaku_window:
            self.danmaku_window.clear_all_danmaku()
        
        self.active_danmaku.clear()
        
        # 通知其他模块弹幕已清空
        self.broadcast_message("danmaku_cleared", {
            "timestamp": self._get_timestamp()
        })
    
    def set_danmaku_speed(self, speed: int):
        """设置弹幕速度"""
        self.danmaku_speed = max(1, min(10, speed))
        self.config["danmaku_speed"] = self.danmaku_speed
        
        # 通知其他模块速度已改变
        self.broadcast_message("danmaku_speed_changed", {
            "speed": self.danmaku_speed,
            "timestamp": self._get_timestamp()
        })
    
    def toggle_danmaku_display(self, enabled: bool):
        """切换弹幕显示"""
        if self.danmaku_window:
            self.danmaku_window.set_danmaku_enabled(enabled)
        
        # 通知其他模块显示状态已改变
        self.broadcast_message("danmaku_display_toggled", {
            "enabled": enabled,
            "timestamp": self._get_timestamp()
        })
    
    def filter_danmaku(self, content: str) -> bool:
        """过滤弹幕内容"""
        if not self.get_config("enable_filter", True):
            return True
        
        # 简单的内容过滤
        forbidden_words = ["垃圾", "废物", "傻"]  # 可以从配置文件加载
        for word in forbidden_words:
            if word in content:
                return False
        
        return True
    
    def send_danmaku_stats(self, requester: str):
        """发送弹幕统计信息"""
        stats = {
            "total_danmaku": len(self.danmaku_history),
            "active_danmaku": len(self.active_danmaku),
            "connection_status": self.sio.connected if self.sio else False,
            "danmaku_speed": self.danmaku_speed
        }
        
        self.send_message(requester, "danmaku_stats", stats)
    
    def update_danmaku(self):
        """更新弹幕位置"""
        if self.danmaku_window:
            self.danmaku_window.update_danmaku_positions()
    
    def on_connection_status_changed(self, connected: bool):
        """连接状态改变处理"""
        if self.danmaku_window:
            self.danmaku_window.update_connection_status(connected)
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

class DanmakuWindow(QMainWindow):
    """弹幕显示窗口"""
    
    def __init__(self, module: DanmakuModule):
        super().__init__()
        self.module = module
        self.danmaku_enabled = True
        
        # 设置窗口属性
        self.setWindowTitle("弹幕显示")
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Tool | Qt.WindowStaysOnTopHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 创建UI
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建控制面板
        self.control_panel = self.create_control_panel()
        layout.addWidget(self.control_panel)
        
        # 创建弹幕显示区域
        self.danmaku_area = QWidget()
        self.danmaku_area.setStyleSheet("background: transparent;")
        layout.addWidget(self.danmaku_area)
        
        # 设置布局
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
    
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        panel.setFixedHeight(80)
        panel.setStyleSheet("background: rgba(0, 0, 0, 100); border-radius: 5px;")
        
        layout = QHBoxLayout(panel)
        
        # 连接状态指示
        self.status_label = QLabel("未连接")
        self.status_label.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(self.status_label)
        
        # 弹幕输入框
        self.danmaku_input = QTextEdit()
        self.danmaku_input.setFixedHeight(40)
        self.danmaku_input.setPlaceholderText("输入弹幕内容...")
        layout.addWidget(self.danmaku_input)
        
        # 发送按钮
        send_btn = QPushButton("发送")
        send_btn.clicked.connect(self.send_danmaku)
        layout.addWidget(send_btn)
        
        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.clicked.connect(self.clear_all_danmaku)
        layout.addWidget(clear_btn)
        
        # 切换显示按钮
        self.toggle_btn = QPushButton("隐藏弹幕")
        self.toggle_btn.clicked.connect(self.toggle_danmaku)
        layout.addWidget(self.toggle_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)
        
        return panel
    
    def add_danmaku(self, content: str, user_info: Dict[str, Any]):
        """添加弹幕"""
        if not self.danmaku_enabled:
            return
        
        # 创建弹幕标签
        danmaku_label = QLabel(content)
        danmaku_label.setParent(self.danmaku_area)
        
        # 设置样式
        role = user_info.get("role", "student")
        if role == "teacher":
            danmaku_label.setStyleSheet("""
                color: gold;
                font-size: 16px;
                font-weight: bold;
                background: rgba(255, 215, 0, 50);
                padding: 5px;
                border-radius: 3px;
            """)
        else:
            danmaku_label.setStyleSheet("""
                color: white;
                font-size: 14px;
                background: rgba(0, 0, 0, 100);
                padding: 3px;
                border-radius: 3px;
            """)
        
        # 设置初始位置
        danmaku_label.move(self.width(), len(self.module.danmaku_labels) * 30)
        danmaku_label.show()
        
        # 添加到列表
        self.module.danmaku_labels.append(danmaku_label)
        self.module.active_danmaku.append({
            "label": danmaku_label,
            "content": content,
            "user_info": user_info,
            "x": self.width(),
            "y": len(self.module.danmaku_labels) * 30
        })
    
    def update_danmaku_positions(self):
        """更新弹幕位置"""
        to_remove = []
        
        for i, danmaku in enumerate(self.module.active_danmaku):
            label = danmaku["label"]
            danmaku["x"] -= self.module.danmaku_speed
            
            if danmaku["x"] + label.width() < 0:
                # 弹幕已移出屏幕
                to_remove.append(i)
                label.deleteLater()
            else:
                label.move(danmaku["x"], danmaku["y"])
        
        # 移除已完成的弹幕
        for i in reversed(to_remove):
            del self.module.active_danmaku[i]
            if i < len(self.module.danmaku_labels):
                del self.module.danmaku_labels[i]
    
    def send_danmaku(self):
        """发送弹幕"""
        content = self.danmaku_input.toPlainText().strip()
        if content:
            self.module.send_danmaku(content, {})
            self.danmaku_input.clear()
    
    def clear_all_danmaku(self):
        """清空所有弹幕"""
        for label in self.module.danmaku_labels:
            label.deleteLater()
        
        self.module.danmaku_labels.clear()
        self.module.active_danmaku.clear()
    
    def toggle_danmaku(self):
        """切换弹幕显示"""
        self.danmaku_enabled = not self.danmaku_enabled
        self.toggle_btn.setText("显示弹幕" if not self.danmaku_enabled else "隐藏弹幕")
        
        if not self.danmaku_enabled:
            self.clear_all_danmaku()
    
    def set_danmaku_enabled(self, enabled: bool):
        """设置弹幕启用状态"""
        self.danmaku_enabled = enabled
        self.toggle_btn.setText("显示弹幕" if not enabled else "隐藏弹幕")
    
    def update_connection_status(self, connected: bool):
        """更新连接状态"""
        if connected:
            self.status_label.setText("已连接")
            self.status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.status_label.setText("未连接")
            self.status_label.setStyleSheet("color: red; font-weight: bold;")

# 为了兼容性，保留原始类名
DanmakuTeacherApp = DanmakuModule