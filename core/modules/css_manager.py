"""
CSS管理器 - 处理打包后的CSS样式和图片路径问题
"""
import os
import sys
from resource_manager import get_image_path

class CSSManager:
    """CSS管理器，动态生成CSS样式"""
    
    @staticmethod
    def get_button_css(image_name_base, fallback_style=None):
        """获取按钮CSS样式，自动处理图片路径
        
        Args:
            image_name_base: 图片名称基础部分（不含状态后缀）
            fallback_style: 备用样式（当图片不存在时使用）
            
        Returns:
            str: CSS样式字符串
        """
        try:
            # 获取三种状态的图片路径
            normal_path = get_image_path(f"{image_name_base}0.png")
            hover_path = get_image_path(f"{image_name_base}1.png")
            pressed_path = get_image_path(f"{image_name_base}2.png")
            
            # 检查文件是否存在
            if all(os.path.exists(path) for path in [normal_path, hover_path, pressed_path]):
                # 转换为URL格式（Windows路径需要特殊处理）
                normal_url = normal_path.replace('\\', '/')
                hover_url = hover_path.replace('\\', '/')
                pressed_url = pressed_path.replace('\\', '/')
                
                css = f"""
                QPushButton{{
                    background-color: rgba(85,85,85,0);
                    background-image: url({normal_url});
                    background-position: center;
                    background-repeat: no-repeat;
                    border-radius: 0px;
                    border: 0px solid rgba(105,105,105,0);
                }}
                QPushButton:hover{{
                    background-image: url({hover_url});
                    border-radius: 0px;
                    border: 0px solid rgba(105,105,105,0);
                    background-position: center;
                    background-repeat: no-repeat;
                }}
                QPushButton:pressed{{
                    background-image: url({pressed_url});
                    border-radius: 0px;
                    border: 0px solid rgba(105,105,105,0);
                    background-position: center;
                    background-repeat: no-repeat;
                }}
                """
                return css.strip()
            else:
                # 使用备用样式
                if fallback_style:
                    return fallback_style
                else:
                    return CSSManager.get_default_button_style()
                    
        except Exception as e:
            print(f"Error generating CSS for {image_name_base}: {e}")
            return fallback_style or CSSManager.get_default_button_style()
    
    @staticmethod
    def get_default_button_style():
        """获取默认按钮样式（不依赖图片）"""
        return """
        QPushButton {
            background-color: rgba(85,85,85,180);
            border-radius: 5px;
            border: 1px solid rgba(105,105,105,150);
            font-weight: bold;
            font: 200 10pt "微软雅黑";
            color: rgba(255,255,255,150);
        }
        QPushButton:hover {
            background-color: rgba(85,85,85,200);
            border: 1px solid rgba(105,105,105,100);
            color: rgba(250,114,5,200);
        }
        QPushButton:pressed {
            background-color: rgba(85,85,85,220);
            border: 1px solid rgba(105,105,105,50);
            color: rgba(6,178,168,200);
        }
        """

class MyCss:
    """兼容原有的MyCss类，使用新的CSS管理器"""
    
    # 基础样式（不依赖图片）
    mainBgcolor = "background-color: rgb(9, 9, 9)"
    mainBgcolora = "background-color: rgba(9, 9, 9,180);border-radius:5px;border:1px solid rgba(150, 150, 150,40);"
    mainBackground = "background-color: rgba(0, 0, 0,2)"
    menuBackground = "background-color: rgba(0, 0, 0,50);border-radius:5px;border:1px solid rgba(150, 150, 150,40);"
    mainBgcolorc = "background-color: rgba(19, 19, 19,250)"
    
    # 按钮样式
    butBCss = """
    QPushButton {
        background-color: rgba(85,85,85,180);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,150);
        font-weight: bold;
        font: 200 10pt "微软雅黑";
        color: rgba(255,255,255,150);
    }
    QPushButton:hover {
        background-color: rgba(85,85,85,180);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,100);
        font-weight: bold;
        font: 75 10pt "微软雅黑";
        color: rgba(250,114,5,200);
    }
    QPushButton:pressed {
        background-color: rgba(85,85,85,180);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,50);
        font-weight: bold;
        font: 75 10pt "微软雅黑";
        color: rgba(6,178,168,200);
    }
    """
    
    butCss = """
    QPushButton {
        background-color: rgba(85,85,85,0);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,150);
        font: 75 10pt "微软雅黑";
        color: rgba(255,255,255,150);
    }
    QPushButton:hover {
        background-color: rgba(85,85,85,100);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,100);
        font: 75 10pt "微软雅黑";
        color: rgba(250,114,5,200);
    }
    QPushButton:pressed {
        background-color: rgba(85,85,85,100);
        border-radius: 5px;
        border: 1px solid rgba(105,105,105,50);
        font: 75 10pt "微软雅黑";
        color: rgba(6,178,168,200);
    }
    """
    
    # 动态生成的图片按钮样式
    @property
    def menuIconCss(self):
        return CSSManager.get_button_css("setGN", self.butBCss)
    
    @property
    def closeIconCss(self):
        return CSSManager.get_button_css("DClose", self.butBCss)
    
    @property
    def plottingIconCss(self):
        return CSSManager.get_button_css("plotting", self.butBCss)
    
    @property
    def revokeIconCss(self):
        return CSSManager.get_button_css("revoke", self.butBCss)
    
    @property
    def erasureIconCss(self):
        return CSSManager.get_button_css("erasure", self.butBCss)
    
    @property
    def ClearIconCss(self):
        return CSSManager.get_button_css("Clear", self.butBCss)
    
    @property
    def ScreenshotCss(self):
        return CSSManager.get_button_css("capture_screen", self.butBCss)
    
    @property
    def recordCss(self):
        return CSSManager.get_button_css("record", self.butBCss)
    
    @property
    def pptCss(self):
        return CSSManager.get_button_css("ppt", self.butBCss)
    
    @property
    def ppt_rightCss(self):
        return CSSManager.get_button_css("ppt_right", self.butBCss)
    
    @property
    def ppt_leftCss(self):
        return CSSManager.get_button_css("ppt_left", self.butBCss)
    
    @property
    def ppt_EndCss(self):
        return CSSManager.get_button_css("ppt_End", self.butBCss)
    
    @property
    def spotlightCss(self):
        return CSSManager.get_button_css("spotlight", self.butBCss)
    
    @property
    def MagnifyCss(self):
        return CSSManager.get_button_css("Magnify", self.butBCss)
    
    @property
    def chCss(self):
        return CSSManager.get_button_css("DClose", self.butBCss)

# 创建全局实例
MyCss = MyCss()