"""
虚拟键盘功能模块 - 教师专用功能
使用UDP广播发现和控制UOS/Deepin系统上的Onboard虚拟键盘。
"""
import json
import subprocess
import threading
import time
import socket
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum
from dataclasses import dataclass

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QTableWidget, QTableWidgetItem, 
                            QHeaderView, QAbstractItemView, QGroupBox)
from PyQt5.QtCore import pyqtSignal, Qt, pyqtSlot

from .base_module import BaseModule
from .udp_discovery import DeviceInfo
import logging

# 日志配置
log = logging.getLogger(__name__)

# --- 从 udp_keyboard_demo.py 引入的常量和类 ---

UDP_PORT = 9999
BROADCAST_PORT = 9998
MAGIC_HEADER = b'UKBD'
PROTOCOL_VERSION = 1

class MessageType(Enum):
    DISCOVER = "discover"
    DISCOVER_REPLY = "discover_reply"
    SHOW_KEYBOARD = "show_keyboard"
    HIDE_KEYBOARD = "hide_keyboard"
    STATUS_REQUEST = "status_request"
    STATUS_REPLY = "status_reply"
    HEARTBEAT = "heartbeat"

class KeyboardStatus(Enum):
    HIDDEN = "hidden"
    SHOWN = "shown"
    ERROR = "error"
    UNKNOWN = "unknown"

@dataclass
class KeyboardDeviceInfo:
    device_id: str
    hostname: str
    ip: str
    keyboard_status: KeyboardStatus = KeyboardStatus.UNKNOWN
    last_seen: float = 0.0
    
    def to_dict(self):
        return {
            'device_id': self.device_id,
            'hostname': self.hostname,
            'ip': self.ip,
            'keyboard_status': self.keyboard_status.value,
            'last_seen': self.last_seen
        }

class UOSVirtualKeyboard:
    """UOS系统本地虚拟键盘控制器"""

    def __init__(self):
        # 不再需要内部状态跟踪
        pass

    def is_uos_system(self) -> bool:
        """检测是否为UOS/Deepin系统"""
        try:
            with open('/etc/os-release', 'r') as f:
                content = f.read().lower()
                return 'uos' in content or 'deepin' in content
        except FileNotFoundError:
            return False

    def _get_current_visibility(self) -> bool:
        """通过DBus获取当前键盘的真实可见性"""
        if not self.is_uos_system():
            return False
        try:
            cmd = [
                'dbus-send', '--session', '--print-reply',
                '--dest=org.onboard.Onboard',
                '/org/onboard/Onboard/Keyboard',
                'org.freedesktop.DBus.Properties.Get',
                'string:org.onboard.Onboard.Keyboard',
                'string:Visible'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3)
            # 可见时的输出包含 'boolean true'
            return 'boolean true' in result.stdout.lower()
        except Exception as e:
            log.error(f"查询键盘可见性失败: {e}")
            return False  # 出错时假定为隐藏

    def show_keyboard(self) -> bool:
        """显示虚拟键盘"""
        if not self.is_uos_system(): return False
        
        if self._get_current_visibility():
            log.info("虚拟键盘已经为显示状态")
            return True
        
        log.info("虚拟键盘当前为隐藏状态，正在尝试显示...")
        return self._toggle_keyboard()

    def hide_keyboard(self) -> bool:
        """隐藏虚拟键盘"""
        if not self.is_uos_system(): return False

        if not self._get_current_visibility():
            log.info("虚拟键盘已经为隐藏状态")
            return True

        log.info("虚拟键盘当前为显示状态，正在尝试隐藏...")
        return self._toggle_keyboard()

    def _toggle_keyboard(self) -> bool:
        """通过DBus切换UOS虚拟键盘显示状态"""
        try:
            cmd = [
                'dbus-send', '--session',
                '--dest=org.onboard.Onboard',
                '--type=method_call',
                '/org/onboard/Onboard/Keyboard',
                'org.onboard.Onboard.Keyboard.ToggleVisible'
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                log.info("✓ DBus切换命令执行成功")
                # 短暂延时以确保状态更新
                time.sleep(0.5)
                return True
            else:
                log.error(f"✗ DBus切换命令执行失败: {result.stderr}")
                return False
        except Exception as e:
            log.error(f"✗ 执行UOS键盘DBus命令异常: {e}")
            return False

    def get_status(self) -> KeyboardStatus:
        """获取键盘的实时状态"""
        if not self.is_uos_system():
            return KeyboardStatus.UNKNOWN
        
        return KeyboardStatus.SHOWN if self._get_current_visibility() else KeyboardStatus.HIDDEN

# --- 重构后的 VirtualKeyboardModule ---

class VirtualKeyboardModule(BaseModule):
    """虚拟键盘功能模块 (UDP实现)"""
    
    devices_updated = pyqtSignal()
    keyboard_status_changed = pyqtSignal(str, str) # device_id, status

    def __init__(self):
        super().__init__(
            module_name="virtual_keyboard",
            display_name="虚拟键盘",
            version="2.0.0"
        )
        
        self.devices: Dict[str, KeyboardDeviceInfo] = {}
        self.local_keyboard = UOSVirtualKeyboard()
        self.hostname = socket.gethostname()
        self.local_ip = self._get_local_ip()
        self.device_id = self._generate_device_id()
        
        self.sock = None
        self.broadcast_sock = None
        self.running = False
        
        self.server_thread = None
        self.discovery_thread = None
        self.heartbeat_thread = None
        
        self.keyboard_window = None

    def _generate_device_id(self) -> str:
        return f"device_{self.hostname}_{self.local_ip.replace('.', '_')}"

    def _get_local_ip(self) -> str:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except:
            return "127.0.0.1"

    def _initialize_module(self) -> bool:
        try:
            self.keyboard_window = VirtualKeyboardWindow(self)
            self.start_server()
            
            self.logger.info("虚拟键盘模块初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"虚拟键盘模块初始化失败: {str(e)}")
            return False

    def _cleanup_module(self):
        self.stop_server()
        if self.keyboard_window:
            self.keyboard_window.close()
        self.logger.info("虚拟键盘模块资源清理完成")

    def get_widget(self):
        return self.keyboard_window

    def show(self):
        if self.keyboard_window:
            self.keyboard_window.show()
            self.discover_devices()

    # --- UDP服务器逻辑 ---

    def _pack_message(self, msg_type: MessageType, data: dict) -> bytes:
        message = {
            'version': PROTOCOL_VERSION,
            'type': msg_type.value,
            'device_id': self.device_id,
            'hostname': self.hostname,
            'ip': self.local_ip,
            'timestamp': time.time(),
            'data': data
        }
        json_data = json.dumps(message, ensure_ascii=False).encode('utf-8')
        return MAGIC_HEADER + len(json_data).to_bytes(4, 'big') + json_data

    def _unpack_message(self, packet: bytes) -> Optional[dict]:
        try:
            if not packet.startswith(MAGIC_HEADER): return None
            length = int.from_bytes(packet[4:8], 'big')
            json_data = packet[8:8+length]
            return json.loads(json_data.decode('utf-8'))
        except Exception as e:
            log.error(f"解包消息失败: {e}")
            return None

    def start_server(self):
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.sock.bind(('0.0.0.0', UDP_PORT))
            
            self.broadcast_sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.broadcast_sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            self.broadcast_sock.bind(('0.0.0.0', BROADCAST_PORT))
            
            self.running = True
            self.server_thread = threading.Thread(target=self._server_loop, daemon=True)
            self.server_thread.start()
            self.discovery_thread = threading.Thread(target=self._discovery_loop, daemon=True)
            self.discovery_thread.start()
            self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            self.heartbeat_thread.start()
            
            log.info(f"UDP服务器已启动，端口: {UDP_PORT}")
        except Exception as e:
            log.error(f"启动UDP服务器失败: {e}")

    def stop_server(self):
        self.running = False
        if self.sock: self.sock.close()
        if self.broadcast_sock: self.broadcast_sock.close()
        log.info("UDP服务器已停止")

    def _server_loop(self):
        self.sock.settimeout(1.0)
        while self.running:
            try:
                data, addr = self.sock.recvfrom(4096)
                message = self._unpack_message(data)
                if message:
                    self._handle_message(message, addr)
            except socket.timeout:
                continue
            except Exception as e:
                if self.running: log.error(f"服务器循环错误: {e}")

    def _discovery_loop(self):
        self.broadcast_sock.settimeout(1.0)
        while self.running:
            try:
                data, addr = self.broadcast_sock.recvfrom(4096)
                message = self._unpack_message(data)
                if message and message.get('type') == MessageType.DISCOVER.value:
                    self._send_discover_reply(addr[0])
            except socket.timeout:
                continue
            except Exception as e:
                if self.running: log.error(f"发现循环错误: {e}")

    def _heartbeat_loop(self):
        while self.running:
            try:
                current_time = time.time()
                offline_devices = [
                    dev_id for dev_id, dev in self.devices.items() 
                    if current_time - dev.last_seen > 30
                ]
                
                if offline_devices:
                    for dev_id in offline_devices:
                        del self.devices[dev_id]
                        log.info(f"设备 {dev_id} 已离线")
                    self.devices_updated.emit()
                
                time.sleep(10)
            except Exception as e:
                if self.running: log.error(f"心跳循环错误: {e}")

    def _handle_message(self, message: dict, addr: tuple):
        msg_type = message.get('type')
        device_id = message.get('device_id')

        if not device_id or device_id == self.device_id:
            return

        # 更新或添加设备信息
        if device_id not in self.devices:
            self.devices[device_id] = KeyboardDeviceInfo(
                device_id=device_id,
                hostname=message.get('hostname', addr[0]),
                ip=message.get('ip', addr[0])
            )
            log.info(f"发现新设备: {self.devices[device_id].hostname}")
        
        device = self.devices[device_id]
        device.last_seen = time.time()

        if msg_type in [MessageType.DISCOVER_REPLY.value, MessageType.STATUS_REPLY.value]:
            status_data = message.get('data', {})
            device.keyboard_status = KeyboardStatus(status_data.get('keyboard_status', 'unknown'))
            self.keyboard_status_changed.emit(device_id, device.keyboard_status.value)

        elif msg_type == MessageType.SHOW_KEYBOARD.value:
            self.local_keyboard.show_keyboard()
            self._send_status_reply(addr[0])

        elif msg_type == MessageType.HIDE_KEYBOARD.value:
            self.local_keyboard.hide_keyboard()
            self._send_status_reply(addr[0])

        elif msg_type == MessageType.STATUS_REQUEST.value:
            self._send_status_reply(addr[0])
        
        self.devices_updated.emit()

    def _send_discover_reply(self, target_ip: str):
        data = {'keyboard_status': self.local_keyboard.get_status().value}
        packet = self._pack_message(MessageType.DISCOVER_REPLY, data)
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.sendto(packet, (target_ip, UDP_PORT))
        except Exception as e:
            log.error(f"发送发现回复失败: {e}")

    def _send_status_reply(self, target_ip: str):
        data = {'keyboard_status': self.local_keyboard.get_status().value}
        packet = self._pack_message(MessageType.STATUS_REPLY, data)
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.sendto(packet, (target_ip, UDP_PORT))
        except Exception as e:
            log.error(f"发送状态回复失败: {e}")

    # --- 公共方法 ---

    def discover_devices(self):
        self.devices.clear()
        self.devices_updated.emit()
        log.info("正在发现网络中的设备...")
        packet = self._pack_message(MessageType.DISCOVER, {})
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
                sock.sendto(packet, ('<broadcast>', BROADCAST_PORT))
        except Exception as e:
            log.error(f"发送设备发现失败: {e}")

    def send_command(self, target_ip: str, command: MessageType):
        log.info(f"发送命令 {command.value} 到 {target_ip}")
        packet = self._pack_message(command, {})
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as sock:
                sock.sendto(packet, (target_ip, UDP_PORT))
        except Exception as e:
            log.error(f"发送命令失败: {e}")

    def get_available_devices(self) -> List[KeyboardDeviceInfo]:
        return list(self.devices.values())

# --- UI部分 ---

class VirtualKeyboardWindow(QWidget):
    """虚拟键盘控制窗口"""

    def __init__(self, module: VirtualKeyboardModule):
        super().__init__()
        self.module = module
        self.setWindowTitle("虚拟键盘控制")
        self.setGeometry(200, 200, 800, 500)
        
        self._init_ui()
        self._connect_signals()
        self.update_device_list()

    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        
        device_group = QGroupBox("设备列表")
        device_layout = QVBoxLayout(device_group)
        
        self.device_table = QTableWidget()
        self.device_table.setColumnCount(4)
        self.device_table.setHorizontalHeaderLabels(["设备名称", "IP地址", "键盘状态", "最后在线"])
        self.device_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.device_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.device_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.device_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        
        self.refresh_button = QPushButton("刷新列表")
        
        device_layout.addWidget(self.device_table)
        device_layout.addWidget(self.refresh_button)
        
        control_group = QGroupBox("远程控制")
        control_layout = QHBoxLayout(control_group)
        self.show_keyboard_button = QPushButton("显示键盘")
        self.hide_keyboard_button = QPushButton("隐藏键盘")
        control_layout.addWidget(self.show_keyboard_button)
        control_layout.addWidget(self.hide_keyboard_button)
        
        main_layout.addWidget(device_group)
        main_layout.addWidget(control_group)

        self.show_keyboard_button.setEnabled(False)
        self.hide_keyboard_button.setEnabled(False)

    def _connect_signals(self):
        self.refresh_button.clicked.connect(self.module.discover_devices)
        self.device_table.itemSelectionChanged.connect(self.on_device_selection_changed)
        
        self.show_keyboard_button.clicked.connect(self.show_remote_keyboard)
        self.hide_keyboard_button.clicked.connect(self.hide_remote_keyboard)

        self.module.devices_updated.connect(self.update_device_list)
        self.module.keyboard_status_changed.connect(self.update_keyboard_status)

    @pyqtSlot()
    def update_device_list(self):
        self.device_table.setRowCount(0)
        devices = self.module.get_available_devices()
        for device in devices:
            row_pos = self.device_table.rowCount()
            self.device_table.insertRow(row_pos)
            
            self.device_table.setItem(row_pos, 0, QTableWidgetItem(device.hostname))
            self.device_table.setItem(row_pos, 1, QTableWidgetItem(device.ip))
            self.device_table.setItem(row_pos, 2, QTableWidgetItem(device.keyboard_status.value))
            last_seen_str = datetime.fromtimestamp(device.last_seen).strftime("%H:%M:%S")
            self.device_table.setItem(row_pos, 3, QTableWidgetItem(last_seen_str))
            
            self.device_table.item(row_pos, 1).setData(Qt.UserRole, device.ip) # Store IP in UserRole
        
        self.on_device_selection_changed()

    @pyqtSlot(str, str)
    def update_keyboard_status(self, device_id: str, status: str):
        for row in range(self.device_table.rowCount()):
            ip_item = self.device_table.item(row, 1)
            # A bit of a hack since we don't have device_id in the table easily
            # We find the device by IP, assuming it's unique
            device_ip = ip_item.text()
            if device_id.endswith(device_ip.replace('.', '_')):
                 self.device_table.item(row, 2).setText(status)
                 break

    def get_selected_device_ip(self) -> Optional[str]:
        selected_items = self.device_table.selectedItems()
        if selected_items:
            # Get IP from the second column's UserRole data
            return self.device_table.item(selected_items[0].row(), 1).data(Qt.UserRole)
        return None

    @pyqtSlot()
    def on_device_selection_changed(self):
        is_selected = self.get_selected_device_ip() is not None
        self.show_keyboard_button.setEnabled(is_selected)
        self.hide_keyboard_button.setEnabled(is_selected)

    @pyqtSlot()
    def show_remote_keyboard(self):
        ip = self.get_selected_device_ip()
        if ip:
            self.module.send_command(ip, MessageType.SHOW_KEYBOARD)

    @pyqtSlot()
    def hide_remote_keyboard(self):
        ip = self.get_selected_device_ip()
        if ip:
            self.module.send_command(ip, MessageType.HIDE_KEYBOARD)

    def closeEvent(self, event):
        self.hide()
        event.ignore()