"""
LocalSend 组件模块
包含设备发现、HTTP请求处理和文件发送等核心组件
"""

import os
import socket
import threading
import time
import json
import queue
import requests
import hashlib
import mimetypes
import urllib.parse
import base64
from http.server import BaseHTTPRequestHandler
from PyQt5.QtCore import QThread, QTimer, QObject, QRunnable, pyqtSignal, pyqtSlot, QMetaObject, Qt, Q_ARG

# 从主模块导入常量
LOCALSEND_PORT = 53317
REQUEST_TIMEOUT = 30

class LocalSendRequestHandler(BaseHTTPRequestHandler):
    """处理LocalSend API请求"""
    
    def __init__(self, module, *args, **kwargs):
        self.module = module
        super().__init__(*args, **kwargs)

    def do_GET(self):
        """处理GET请求"""
        try:
            if self.path == '/api/localsend/v2/info':
                self._send_info_response()
            elif self.path == '/':
                self._send_welcome_page()
            else:
                self._send_not_found()
        except Exception as e:
            self.module.logger.error(f"处理GET请求时出错: {e}")
            self._send_error_response(500, "Internal Server Error")

    def do_POST(self):
        """处理POST请求"""
        try:
            if self.path == '/api/localsend/v2/register':
                self._send_info_response()
            elif self.path == '/api/localsend/v2/send-request':
                self._handle_send_request()
            elif self.path == '/api/localsend/v2/send':
                self._handle_file_receive()
            else:
                self._send_not_found()
        except Exception as e:
            self.module.logger.error(f"处理POST请求时出错: {e}")
            self._send_error_response(500, "Internal Server Error")

    def _send_info_response(self):
        """发送设备信息响应"""
        response = {
            "alias": self.module.device_alias,
            "deviceModel": self.module.device_model,
            "ip": self.module.device_ip,
            "port": self.module.port,
            "https_": False,
            "version": "2.0",
            "protocol": "localsend"
        }
        self._send_json_response(200, response)

    def _send_welcome_page(self):
        """发送欢迎页面"""
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>LocalSend - {self.module.device_alias}</title>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background: white; 
                           padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #333; text-align: center; }}
                .info {{ background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .status {{ color: #4CAF50; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 LocalSend 文件分享服务</h1>
                <div class="info">
                    <p><strong>设备名称:</strong> {self.module.device_alias}</p>
                    <p><strong>IP地址:</strong> {self.module.device_ip}</p>
                    <p><strong>端口:</strong> {self.module.port}</p>
                    <p><strong>状态:</strong> <span class="status">运行中</span></p>
                </div>
                <p>此设备正在运行LocalSend文件分享服务，可以与其他LocalSend客户端进行文件传输。</p>
                <p>请使用LocalSend客户端应用程序来发送或接收文件。</p>
            </div>
        </body>
        </html>
        """
        self._send_html_response(200, html_content)

    def _handle_send_request(self):
        """处理文件发送请求"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self._send_error_response(400, "Missing Content-Length")
                return
                
            body = self.rfile.read(content_length)
            file_info = json.loads(body.decode('utf-8'))
            
            self.module.logger.info(f"收到来自 {self.client_address[0]} 的文件发送请求")
            
            response_queue = queue.Queue(maxsize=1)

            QMetaObject.invokeMethod(
                self.module.window, 'handle_incoming_file', 
                Qt.QueuedConnection,
                Q_ARG(str, self.client_address[0]),
                Q_ARG(dict, file_info),
                Q_ARG('PyQt_PyObject', response_queue)
            )
            
            try:
                accepted = response_queue.get(timeout=REQUEST_TIMEOUT)
                if accepted:
                    self.module.logger.info("用户接受了文件传输请求")
                    self._send_json_response(200, {})
                else:
                    self.module.logger.info("用户拒绝了文件传输请求")
                    self._send_error_response(403, "User declined the file transfer")
                    
            except queue.Empty:
                self.module.logger.warning("文件传输请求超时")
                self._send_error_response(408, "Request timeout")
                
        except json.JSONDecodeError as e:
            self.module.logger.error(f"解析JSON请求失败: {e}")
            self._send_error_response(400, "Invalid JSON")
        except Exception as e:
            self.module.logger.error(f"处理发送请求时出错: {e}")
            self._send_error_response(500, "Internal server error")

    def _handle_file_receive(self):
        """处理文件接收"""
        try:
            encoded_file_name = self.headers.get('X-File-Name')
            if not encoded_file_name:
                self._send_error_response(400, "Missing X-File-Name header")
                return

            # 解码URL编码的文件名
            try:
                file_name = urllib.parse.unquote(encoded_file_name)
                self.module.logger.debug(f"接收文件: 编码 {encoded_file_name} -> 解码 {file_name}")
            except Exception as e:
                self.module.logger.warning(f"文件名解码失败，使用原始名称: {e}")
                file_name = encoded_file_name

            safe_file_name = os.path.basename(file_name)
            if not safe_file_name:
                self._send_error_response(400, "Invalid file name")
                return

            save_path = os.path.join(self.module.download_path, safe_file_name)
            
            # 如果文件已存在，添加序号
            counter = 1
            original_save_path = save_path
            while os.path.exists(save_path):
                name, ext = os.path.splitext(original_save_path)
                save_path = f"{name}_{counter}{ext}"
                counter += 1

            content_length_str = self.headers.get('Content-Length')
            self.module.logger.info(f"开始接收文件: {safe_file_name} -> {save_path}")

            bytes_received = 0
            with open(save_path, 'wb') as f:
                if content_length_str:
                    content_length = int(content_length_str)
                    while bytes_received < content_length:
                        chunk_size = min(65536, content_length - bytes_received)
                        chunk = self.rfile.read(chunk_size)
                        if not chunk:
                            break
                        f.write(chunk)
                        bytes_received += len(chunk)
                else:
                    while True:
                        chunk = self.rfile.read(65536)
                        if not chunk:
                            break
                        f.write(chunk)
                        bytes_received += len(chunk)
            
            actual_size = os.path.getsize(save_path)
            self._send_json_response(200, {"status": "success"})
            self.module.logger.info(f"文件接收成功: {save_path} ({actual_size} bytes)")
            
            record = {
                'type': 'receive',
                'files': [safe_file_name],
                'status': 'success',
                'size': actual_size,
                'sender': self.client_address[0]
            }
            self.module.add_transfer_record(record)
            
        except Exception as e:
            self.module.logger.error(f"文件接收失败: {e}")
            self._send_error_response(500, f"File receive failed: {str(e)}")

    def _send_json_response(self, status_code: int, data: dict):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        response_data = json.dumps(data).encode('utf-8')
        self.wfile.write(response_data)

    def _send_html_response(self, status_code: int, html: str):
        """发送HTML响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def _send_error_response(self, status_code: int, message: str):
        """发送错误响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.end_headers()
        error_data = json.dumps({"error": message}).encode('utf-8')
        self.wfile.write(error_data)

    def _send_not_found(self):
        """发送404响应"""
        self._send_error_response(404, "Not Found")

    def log_message(self, format, *args):
        """重写日志方法"""
        message = format % args
        self.module.logger.debug(f"HTTP: {message}")

class FileSenderSignals(QObject):
    """文件发送器信号"""
    progress = pyqtSignal(int)
    finished = pyqtSignal(str)
    error = pyqtSignal(str)

class FileSender(QRunnable):
    """在工作线程中发送文件"""

    def __init__(self, file_path: str, target_ip: str, target_port: int, module):
        super().__init__()
        self.file_path = file_path
        self.target_ip = target_ip
        self.target_port = target_port
        self.module = module
        self.signals = FileSenderSignals()

    @pyqtSlot()
    def run(self):
        """执行文件发送"""
        try:
            if not os.path.exists(self.file_path):
                raise Exception(f"文件不存在: {self.file_path}")

            file_name = os.path.basename(self.file_path)
            file_size = os.path.getsize(self.file_path)

            self.module.logger.info(f"开始发送文件: {file_name} ({file_size} bytes) 到 {self.target_ip}")

            # 1. 发送传输请求
            request_payload = self._build_request_payload(file_name, file_size)

            url = f"http://{self.target_ip}:{self.target_port}/api/localsend/v2/send-request"
            self.module.logger.info(f"发送传输请求到: {url}")

            response = requests.post(
                url,
                json=request_payload,
                timeout=REQUEST_TIMEOUT,
                headers={'Content-Type': 'application/json'}
            )

            if response.status_code == 403:
                raise Exception("对方拒绝了文件传输请求")
            elif response.status_code == 408:
                raise Exception("传输请求超时，对方可能未响应")
            elif response.status_code != 200:
                raise Exception(f"传输请求失败 (状态码: {response.status_code})")

            # 2. 发送文件内容
            self.module.logger.info("对方已接受请求，开始发送文件内容...")
            self._send_file_content(file_name, file_size)

            self.signals.finished.emit(f"文件 '{file_name}' 发送成功！")

        except requests.exceptions.ConnectTimeout:
            self.signals.error.emit("连接超时，请检查目标设备是否在线")
        except requests.exceptions.ConnectionError:
            self.signals.error.emit("连接失败，请检查网络连接和目标设备")
        except requests.exceptions.RequestException as e:
            self.signals.error.emit(f"网络错误: {str(e)}")
        except Exception as e:
            self.signals.error.emit(str(e))

    def _build_request_payload(self, file_name: str, file_size: int) -> dict:
        """构建传输请求载荷"""
        file_type = self._detect_file_type(file_name)
        file_hash = self._calculate_file_hash() if file_size < 100 * 1024 * 1024 else None

        return {
            "info": {
                "alias": self.module.device_alias,
                "deviceModel": self.module.device_model,
                "ip": self.module.device_ip,
                "port": self.module.port,
                "https_": False,
                "version": "2.0"
            },
            "files": {
                "1": {
                    "id": "1",
                    "fileName": file_name,
                    "size": file_size,
                    "fileType": file_type,
                    "sha256": file_hash,
                    "preview": None
                }
            }
        }

    def _detect_file_type(self, file_name: str) -> str:
        """检测文件类型"""
        mime_type, _ = mimetypes.guess_type(file_name)
        if mime_type:
            if mime_type.startswith('image/'):
                return 'image'
            elif mime_type.startswith('video/'):
                return 'video'
            elif mime_type.startswith('audio/'):
                return 'audio'
            elif mime_type.startswith('text/'):
                return 'text'
        return 'other'

    def _calculate_file_hash(self) -> str:
        """计算文件SHA256哈希"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(self.file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return None

    def _send_file_content(self, file_name: str, file_size: int):
        """发送文件内容"""
        send_url = f"http://{self.target_ip}:{self.target_port}/api/localsend/v2/send"

        # 对文件名进行URL编码以支持中文字符
        encoded_file_name = urllib.parse.quote(file_name, safe='')

        headers = {
            'Content-Type': 'application/octet-stream',
            'X-File-Name': encoded_file_name,
            'Content-Length': str(file_size)
        }

        self.module.logger.debug(f"发送文件: {file_name} -> 编码后: {encoded_file_name}")

        with open(self.file_path, 'rb') as f:
            response = requests.post(
                send_url,
                data=self._read_in_chunks(f, file_size),
                headers=headers,
                timeout=300
            )

        if response.status_code != 200:
            raise Exception(f"文件发送失败 (状态码: {response.status_code})")

    def _read_in_chunks(self, file_object, file_size, chunk_size=65536):
        """文件分块读取生成器"""
        bytes_sent = 0
        last_progress = 0

        while True:
            data = file_object.read(chunk_size)
            if not data:
                break

            bytes_sent += len(data)
            progress = int((bytes_sent / file_size) * 100)

            if progress != last_progress:
                self.signals.progress.emit(progress)
                last_progress = progress

            yield data
