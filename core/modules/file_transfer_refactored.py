"""
重构后的文件传输模块 - 基于新的模块化架构
支持多种传输方式和进度显示
"""
import os
import sys
import socket
import socketserver
import threading
import time
import json
import hashlib
import qrcode
from datetime import datetime
from typing import Dict, Any, List, Optional, Callable
from PyQt5.QtWidgets import *
from PyQt5.QtGui import *
from PyQt5.QtCore import *
from http.server import SimpleHTTPRequestHandler
import logging
import shutil
from .base_module import CommonModule

class FileTransferModule(CommonModule):
    """重构后的文件传输模块"""
    
    def __init__(self):
        super().__init__(
            module_name="file_transfer",
            display_name="文件传输",
            version="2.0.0"
        )
        
        # 传输相关属性
        self.transfer_window = None
        self.server = None
        self.server_thread = None
        self.port = 8001
        self.shared_folder = "shared_files"
        self.transfer_history = []
        self.active_transfers = {}
        
        # 传输统计
        self.total_bytes_sent = 0
        self.total_bytes_received = 0
        self.transfer_count = 0
        
        # 支持的传输方式
        self.transfer_methods = {
            "http_server": "HTTP服务器",
            "tcp_direct": "TCP直连",
            "localsend": "LocalSend集成"
        }
        
        # 当前传输方式
        self.current_method = "http_server"
        
        # 进度回调
        self.progress_callbacks = []
    
    def _initialize_module(self) -> bool:
        """初始化文件传输模块"""
        try:
            # 创建共享文件夹
            if not os.path.exists(self.shared_folder):
                os.makedirs(self.shared_folder)
            
            # 创建传输窗口
            self.transfer_window = FileTransferWindow(self)
            
            # 设置模块配置
            self.config.update({
                "default_port": 8001,
                "shared_folder": "shared_files",
                "max_file_size": 1024 * 1024 * 1024,  # 1GB
                "enable_compression": True,
                "enable_encryption": False,
                "auto_accept_transfers": False,
                "transfer_timeout": 300  # 5分钟
            })
            
            # 加载传输历史
            self._load_transfer_history()
            
            self.logger.info("文件传输模块初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"文件传输模块初始化失败: {str(e)}")
            return False
    
    def _cleanup_module(self):
        """清理文件传输模块资源"""
        try:
            # 停止服务器
            self.stop_server()
            
            # 取消所有活跃传输
            self.cancel_all_transfers()
            
            # 保存传输历史
            self._save_transfer_history()
            
            # 关闭传输窗口
            if self.transfer_window:
                self.transfer_window.close()
                self.transfer_window = None
            
            self.logger.info("文件传输模块资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理文件传输模块资源时出错: {str(e)}")
    
    def _handle_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """处理接收到的消息"""
        if message_type == "file_transfer_request":
            self.handle_transfer_request(data)
        elif message_type == "file_transfer_response":
            self.handle_transfer_response(data)
        elif message_type == "file_transfer_progress":
            self.handle_transfer_progress(data)
        elif message_type == "file_transfer_complete":
            self.handle_transfer_complete(data)
        elif message_type == "file_transfer_error":
            self.handle_transfer_error(data)
    
    def show(self):
        """显示文件传输窗口"""
        if self.transfer_window:
            self.transfer_window.show()
            self.transfer_window.raise_()
            self.transfer_window.activateWindow()
    
    def hide(self):
        """隐藏文件传输窗口"""
        if self.transfer_window:
            self.transfer_window.hide()
    
    def close(self):
        """关闭文件传输模块"""
        self.cleanup()
    
    def get_widget(self):
        """返回模块的主窗口或主小部件"""
        return self.transfer_window
    
    def get_local_ip(self) -> str:
        """获取本地IP地址"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("**************", 1))
            ip = s.getsockname()[0]
            s.close()
            return ip
        except Exception:
            return "127.0.0.1"
    
    def is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("", port))
                return True
        except OSError:
            return False
    
    def find_available_port(self, start_port: int = 8001) -> int:
        """查找可用端口"""
        port = start_port
        while not self.is_port_available(port) and port < 65535:
            port += 1
        return port if port < 65535 else None
    
    def start_server(self, port: int = None) -> bool:
        """启动HTTP文件服务器"""
        try:
            if self.server:
                self.stop_server()
            
            if port is None:
                port = self.find_available_port(self.config.get("default_port", 8001))
            
            if port is None:
                self.logger.error("无法找到可用端口")
                return False
            
            self.port = port
            
            # 切换到共享文件夹
            original_dir = os.getcwd()
            os.chdir(self.shared_folder)
            
            # 创建服务器
            self.server = socketserver.TCPServer(("", self.port), CustomHTTPHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            # 恢复原始目录
            os.chdir(original_dir)
            
            # 通知其他模块服务器已启动
            self.broadcast_message("file_server_started", {
                "ip": self.get_local_ip(),
                "port": self.port,
                "url": f"http://{self.get_local_ip()}:{self.port}",
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info(f"文件服务器已启动: http://{self.get_local_ip()}:{self.port}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动文件服务器失败: {str(e)}")
            return False
    
    def stop_server(self):
        """停止HTTP文件服务器"""
        try:
            if self.server:
                self.server.shutdown()
                self.server.server_close()
                self.server = None
            
            if self.server_thread:
                self.server_thread.join(timeout=1)
                self.server_thread = None
            
            # 通知其他模块服务器已停止
            self.broadcast_message("file_server_stopped", {
                "timestamp": datetime.now().isoformat()
            })
            
            self.logger.info("文件服务器已停止")
            
        except Exception as e:
            self.logger.error(f"停止文件服务器失败: {str(e)}")
    
    def generate_qr_code(self) -> tuple:
        """生成访问二维码"""
        try:
            url = f"http://{self.get_local_ip()}:{self.port}"
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(url)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            qr_path = os.path.join(self.shared_folder, "qr_code.png")
            img.save(qr_path)
            
            return url, qr_path
            
        except Exception as e:
            self.logger.error(f"生成二维码失败: {str(e)}")
            return None, None
    
    def send_file(self, file_path: str, target_ip: str, target_port: int = None, 
                  progress_callback: Callable = None) -> str:
        """发送文件到目标设备"""
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 生成传输ID
            transfer_id = hashlib.md5(f"{file_path}{target_ip}{time.time()}".encode()).hexdigest()
            
            # 创建传输记录
            transfer_info = {
                "id": transfer_id,
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_size": os.path.getsize(file_path),
                "target_ip": target_ip,
                "target_port": target_port or 8001,
                "status": "preparing",
                "progress": 0,
                "start_time": datetime.now().isoformat(),
                "method": self.current_method
            }
            
            self.active_transfers[transfer_id] = transfer_info
            
            if progress_callback:
                self.progress_callbacks.append(progress_callback)
            
            # 根据传输方式执行传输
            if self.current_method == "tcp_direct":
                self._send_file_tcp(transfer_id, transfer_info)
            elif self.current_method == "localsend":
                self._send_file_localsend(transfer_id, transfer_info)
            else:
                # 默认HTTP方式
                self._send_file_http(transfer_id, transfer_info)
            
            return transfer_id
            
        except Exception as e:
            self.logger.error(f"发送文件失败: {str(e)}")
            return None
    
    def _send_file_tcp(self, transfer_id: str, transfer_info: Dict[str, Any]):
        """使用TCP直连发送文件"""
        # TCP直连实现
        pass
    
    def _send_file_http(self, transfer_id: str, transfer_info: Dict[str, Any]):
        """使用HTTP方式发送文件"""
        # HTTP发送实现
        pass
    
    def _send_file_localsend(self, transfer_id: str, transfer_info: Dict[str, Any]):
        """使用LocalSend发送文件"""
        # LocalSend集成实现
        pass
    
    def cancel_transfer(self, transfer_id: str):
        """取消指定传输"""
        if transfer_id in self.active_transfers:
            self.active_transfers[transfer_id]["status"] = "cancelled"
            # 实际取消传输逻辑
            del self.active_transfers[transfer_id]
    
    def cancel_all_transfers(self):
        """取消所有活跃传输"""
        for transfer_id in list(self.active_transfers.keys()):
            self.cancel_transfer(transfer_id)
    
    def get_transfer_status(self, transfer_id: str) -> Dict[str, Any]:
        """获取传输状态"""
        return self.active_transfers.get(transfer_id)
    
    def get_transfer_history(self) -> List[Dict[str, Any]]:
        """获取传输历史"""
        return self.transfer_history.copy()
    
    def _load_transfer_history(self):
        """加载传输历史"""
        try:
            from resource_manager import path_manager
            history_file = path_manager.get_database_path("transfer_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.transfer_history = json.load(f)
        except Exception as e:
            self.logger.error(f"加载传输历史失败: {str(e)}")
            self.transfer_history = []

    def _save_transfer_history(self):
        """保存传输历史"""
        try:
            from resource_manager import path_manager
            history_file = path_manager.get_database_path("transfer_history.json")
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.transfer_history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存传输历史失败: {str(e)}")
    
    def handle_transfer_request(self, data: Dict[str, Any]):
        """处理传输请求"""
        # 处理来自其他设备的传输请求
        pass
    
    def handle_transfer_response(self, data: Dict[str, Any]):
        """处理传输响应"""
        # 处理传输响应
        pass
    
    def handle_transfer_progress(self, data: Dict[str, Any]):
        """处理传输进度"""
        # 更新传输进度
        pass
    
    def handle_transfer_complete(self, data: Dict[str, Any]):
        """处理传输完成"""
        # 处理传输完成
        pass
    
    def handle_transfer_error(self, data: Dict[str, Any]):
        """处理传输错误"""
        # 处理传输错误
        pass

class CustomHTTPHandler(SimpleHTTPRequestHandler):
    """自定义HTTP处理器"""
    
    def handle_one_request(self):
        try:
            super().handle_one_request()
        except ConnectionResetError:
            logging.warning("客户端断开了连接")
        except Exception as e:
            logging.error(f"处理请求时发生错误: {e}")
    
    def copyfile(self, source, outputfile):
        try:
            shutil.copyfileobj(source, outputfile, length=16 * 1024 * 1024)  # 16MB 缓冲区
        except ConnectionResetError:
            logging.warning("客户端断开了连接")
        except Exception as e:
            logging.error(f"文件传输失败: {e}")

class FileTransferWindow(QMainWindow):
    """文件传输窗口"""

    def __init__(self, module: FileTransferModule):
        super().__init__()
        self.module = module

        # 窗口设置
        self.setWindowTitle("文件传输工具")
        self.setGeometry(100, 100, 800, 600)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint | Qt.WindowMinimizeButtonHint)

        # 拖拽相关
        self.dragging = False
        self.offset = QPoint()

        # 创建UI
        self.setup_ui()

        # 连接信号
        self.setup_signals()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建选项卡
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 发送文件选项卡
        self.send_tab = self.create_send_tab()
        self.tab_widget.addTab(self.send_tab, "发送文件")

        # 接收文件选项卡
        self.receive_tab = self.create_receive_tab()
        self.tab_widget.addTab(self.receive_tab, "接收文件")

        # 传输历史选项卡
        self.history_tab = self.create_history_tab()
        self.tab_widget.addTab(self.history_tab, "传输历史")

        # 设置选项卡
        self.settings_tab = self.create_settings_tab()
        self.tab_widget.addTab(self.settings_tab, "设置")

    def create_send_tab(self):
        """创建发送文件选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 文件选择区域
        file_group = QGroupBox("选择文件")
        file_layout = QVBoxLayout(file_group)

        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setAcceptDrops(True)
        self.file_list.setDragDropMode(QAbstractItemView.DropOnly)
        file_layout.addWidget(self.file_list)

        # 文件操作按钮
        file_buttons = QHBoxLayout()
        self.add_file_btn = QPushButton("添加文件")
        self.add_folder_btn = QPushButton("添加文件夹")
        self.remove_file_btn = QPushButton("移除文件")
        self.clear_files_btn = QPushButton("清空列表")

        file_buttons.addWidget(self.add_file_btn)
        file_buttons.addWidget(self.add_folder_btn)
        file_buttons.addWidget(self.remove_file_btn)
        file_buttons.addWidget(self.clear_files_btn)
        file_buttons.addStretch()

        file_layout.addLayout(file_buttons)
        layout.addWidget(file_group)

        # 目标设备区域
        target_group = QGroupBox("目标设备")
        target_layout = QFormLayout(target_group)

        self.target_ip_edit = QLineEdit()
        self.target_ip_edit.setPlaceholderText("输入目标设备IP地址")
        target_layout.addRow("IP地址:", self.target_ip_edit)

        self.target_port_edit = QLineEdit("8001")
        target_layout.addRow("端口:", self.target_port_edit)

        # 传输方式选择
        self.method_combo = QComboBox()
        for key, value in self.module.transfer_methods.items():
            self.method_combo.addItem(value, key)
        target_layout.addRow("传输方式:", self.method_combo)

        layout.addWidget(target_group)

        # 传输控制区域
        control_layout = QHBoxLayout()
        self.send_btn = QPushButton("开始发送")
        self.send_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        self.cancel_send_btn = QPushButton("取消发送")
        self.cancel_send_btn.setEnabled(False)

        control_layout.addWidget(self.send_btn)
        control_layout.addWidget(self.cancel_send_btn)
        control_layout.addStretch()

        layout.addLayout(control_layout)

        # 传输进度区域
        progress_group = QGroupBox("传输进度")
        progress_layout = QVBoxLayout(progress_group)

        self.progress_bar = QProgressBar()
        self.progress_label = QLabel("准备就绪")
        self.speed_label = QLabel("传输速度: 0 KB/s")

        progress_layout.addWidget(self.progress_label)
        progress_layout.addWidget(self.progress_bar)
        progress_layout.addWidget(self.speed_label)

        layout.addWidget(progress_group)

        return widget

    def create_receive_tab(self):
        """创建接收文件选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 服务器状态区域
        server_group = QGroupBox("文件服务器")
        server_layout = QVBoxLayout(server_group)

        # 服务器控制
        server_control = QHBoxLayout()
        self.start_server_btn = QPushButton("启动服务器")
        self.stop_server_btn = QPushButton("停止服务器")
        self.stop_server_btn.setEnabled(False)

        server_control.addWidget(self.start_server_btn)
        server_control.addWidget(self.stop_server_btn)
        server_control.addStretch()

        server_layout.addLayout(server_control)

        # 服务器信息
        self.server_info_label = QLabel("服务器未启动")
        server_layout.addWidget(self.server_info_label)

        # 二维码显示
        self.qr_label = QLabel()
        self.qr_label.setAlignment(Qt.AlignCenter)
        self.qr_label.setMinimumSize(200, 200)
        self.qr_label.setStyleSheet("border: 1px solid gray;")
        server_layout.addWidget(self.qr_label)

        layout.addWidget(server_group)

        # 接收设置
        receive_group = QGroupBox("接收设置")
        receive_layout = QFormLayout(receive_group)

        self.save_path_edit = QLineEdit(self.module.shared_folder)
        self.browse_save_btn = QPushButton("浏览")

        save_layout = QHBoxLayout()
        save_layout.addWidget(self.save_path_edit)
        save_layout.addWidget(self.browse_save_btn)

        receive_layout.addRow("保存路径:", save_layout)

        self.auto_accept_cb = QCheckBox("自动接受文件")
        receive_layout.addRow("", self.auto_accept_cb)

        layout.addWidget(receive_group)

        layout.addStretch()

        return widget

    def create_history_tab(self):
        """创建传输历史选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 历史记录表格
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(6)
        self.history_table.setHorizontalHeaderLabels([
            "文件名", "大小", "方向", "状态", "时间", "目标/来源"
        ])

        # 设置表格属性
        header = self.history_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.Stretch)

        layout.addWidget(self.history_table)

        # 历史记录操作
        history_buttons = QHBoxLayout()
        self.refresh_history_btn = QPushButton("刷新")
        self.clear_history_btn = QPushButton("清空历史")
        self.export_history_btn = QPushButton("导出历史")

        history_buttons.addWidget(self.refresh_history_btn)
        history_buttons.addWidget(self.clear_history_btn)
        history_buttons.addWidget(self.export_history_btn)
        history_buttons.addStretch()

        layout.addLayout(history_buttons)

        return widget

    def create_settings_tab(self):
        """创建设置选项卡"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 网络设置
        network_group = QGroupBox("网络设置")
        network_layout = QFormLayout(network_group)

        self.port_edit = QLineEdit(str(self.module.config.get("default_port", 8001)))
        network_layout.addRow("默认端口:", self.port_edit)

        self.timeout_edit = QLineEdit(str(self.module.config.get("transfer_timeout", 300)))
        network_layout.addRow("传输超时(秒):", self.timeout_edit)

        layout.addWidget(network_group)

        # 文件设置
        file_group = QGroupBox("文件设置")
        file_layout = QFormLayout(file_group)

        self.max_size_edit = QLineEdit(str(self.module.config.get("max_file_size", 1024)))
        file_layout.addRow("最大文件大小(MB):", self.max_size_edit)

        self.compression_cb = QCheckBox("启用压缩")
        self.compression_cb.setChecked(self.module.config.get("enable_compression", True))
        file_layout.addRow("", self.compression_cb)

        self.encryption_cb = QCheckBox("启用加密")
        self.encryption_cb.setChecked(self.module.config.get("enable_encryption", False))
        file_layout.addRow("", self.encryption_cb)

        layout.addWidget(file_group)

        # 保存设置按钮
        save_settings_btn = QPushButton("保存设置")
        save_settings_btn.clicked.connect(self.save_settings)
        layout.addWidget(save_settings_btn)

        layout.addStretch()

        return widget

    def setup_signals(self):
        """设置信号连接"""
        # 发送选项卡信号
        self.add_file_btn.clicked.connect(self.add_files)
        self.add_folder_btn.clicked.connect(self.add_folder)
        self.remove_file_btn.clicked.connect(self.remove_selected_files)
        self.clear_files_btn.clicked.connect(self.clear_file_list)
        self.send_btn.clicked.connect(self.start_send)
        self.cancel_send_btn.clicked.connect(self.cancel_send)

        # 接收选项卡信号
        self.start_server_btn.clicked.connect(self.start_server)
        self.stop_server_btn.clicked.connect(self.stop_server)
        self.browse_save_btn.clicked.connect(self.browse_save_path)

        # 历史选项卡信号
        self.refresh_history_btn.clicked.connect(self.refresh_history)
        self.clear_history_btn.clicked.connect(self.clear_history)
        self.export_history_btn.clicked.connect(self.export_history)

    def add_files(self):
        """添加文件"""
        files, _ = QFileDialog.getOpenFileNames(self, "选择文件")
        for file_path in files:
            if file_path:
                self.file_list.addItem(file_path)

    def add_folder(self):
        """添加文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择文件夹")
        if folder:
            for root, dirs, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    self.file_list.addItem(file_path)

    def remove_selected_files(self):
        """移除选中的文件"""
        for item in self.file_list.selectedItems():
            self.file_list.takeItem(self.file_list.row(item))

    def clear_file_list(self):
        """清空文件列表"""
        self.file_list.clear()

    def start_send(self):
        """开始发送文件"""
        if self.file_list.count() == 0:
            QMessageBox.warning(self, "警告", "请先选择要发送的文件")
            return

        target_ip = self.target_ip_edit.text().strip()
        if not target_ip:
            QMessageBox.warning(self, "警告", "请输入目标IP地址")
            return

        try:
            target_port = int(self.target_port_edit.text())
        except ValueError:
            QMessageBox.warning(self, "警告", "端口号必须是数字")
            return

        # 获取文件列表
        files = []
        for i in range(self.file_list.count()):
            files.append(self.file_list.item(i).text())

        # 开始传输
        self.send_btn.setEnabled(False)
        self.cancel_send_btn.setEnabled(True)

        # 这里应该启动实际的文件传输
        QMessageBox.information(self, "信息", f"开始向 {target_ip}:{target_port} 发送 {len(files)} 个文件")

    def cancel_send(self):
        """取消发送"""
        self.send_btn.setEnabled(True)
        self.cancel_send_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        self.progress_label.setText("传输已取消")

    def start_server(self):
        """启动服务器"""
        if self.module.start_server():
            self.start_server_btn.setEnabled(False)
            self.stop_server_btn.setEnabled(True)

            ip = self.module.get_local_ip()
            port = self.module.port
            self.server_info_label.setText(f"服务器已启动: http://{ip}:{port}")

            # 生成并显示二维码
            url, qr_path = self.module.generate_qr_code()
            if qr_path and os.path.exists(qr_path):
                pixmap = QPixmap(qr_path)
                scaled_pixmap = pixmap.scaled(200, 200, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.qr_label.setPixmap(scaled_pixmap)
        else:
            QMessageBox.warning(self, "错误", "启动服务器失败")

    def stop_server(self):
        """停止服务器"""
        self.module.stop_server()
        self.start_server_btn.setEnabled(True)
        self.stop_server_btn.setEnabled(False)
        self.server_info_label.setText("服务器已停止")
        self.qr_label.clear()
        self.qr_label.setText("服务器未启动")

    def browse_save_path(self):
        """浏览保存路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择保存路径", self.save_path_edit.text())
        if folder:
            self.save_path_edit.setText(folder)
            self.module.shared_folder = folder

    def refresh_history(self):
        """刷新传输历史"""
        history = self.module.get_transfer_history()
        self.history_table.setRowCount(len(history))

        for row, record in enumerate(history):
            self.history_table.setItem(row, 0, QTableWidgetItem(record.get("file_name", "")))
            self.history_table.setItem(row, 1, QTableWidgetItem(str(record.get("file_size", 0))))
            self.history_table.setItem(row, 2, QTableWidgetItem(record.get("direction", "")))
            self.history_table.setItem(row, 3, QTableWidgetItem(record.get("status", "")))
            self.history_table.setItem(row, 4, QTableWidgetItem(record.get("timestamp", "")))
            self.history_table.setItem(row, 5, QTableWidgetItem(record.get("target", "")))

    def clear_history(self):
        """清空传输历史"""
        reply = QMessageBox.question(self, "确认", "确定要清空传输历史吗？")
        if reply == QMessageBox.Yes:
            self.module.transfer_history.clear()
            self.module._save_transfer_history()
            self.refresh_history()

    def export_history(self):
        """导出传输历史"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "导出传输历史", "transfer_history.json", "JSON Files (*.json)"
        )
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.module.transfer_history, f, ensure_ascii=False, indent=2)
                QMessageBox.information(self, "成功", f"传输历史已导出到: {filename}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"导出失败: {str(e)}")

    def save_settings(self):
        """保存设置"""
        try:
            self.module.config.update({
                "default_port": int(self.port_edit.text()),
                "transfer_timeout": int(self.timeout_edit.text()),
                "max_file_size": int(self.max_size_edit.text()) * 1024 * 1024,  # 转换为字节
                "enable_compression": self.compression_cb.isChecked(),
                "enable_encryption": self.encryption_cb.isChecked()
            })
            QMessageBox.information(self, "成功", "设置已保存")
        except ValueError:
            QMessageBox.warning(self, "错误", "请输入有效的数字")

# 为了兼容性，保留原始类名
FileSharingApp = FileTransferModule
