import sys
import hashlib
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QLineEdit, QPushButton, QMessageBox, QTextEdit
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
import psutil
import uuid

# def generate_hardware_fingerprint():
#     """生成实时硬件指纹（基于系统实际硬件信息）"""
#     try:
#         # 获取 CPU 信息
#         cpu_freq = psutil.cpu_freq()  # 获取 CPU 频率（MHz）
#         cpu_info = str(cpu_freq.max) if cpu_freq else "UNKNOWN_CPU"

#         # 获取硬盘容量
#         disk_info = psutil.disk_usage('/').total  # 获取 '/' 磁盘分区的总容量（字节）
#         disk_capacity = str(disk_info)

#         # 获取 MAC 地址
#         mac_address = hex(uuid.getnode())[2:].upper()  # 获取本机唯一的网络 MAC 地址

#         # 拼接硬件信息为指纹
#         fingerprint = f"{cpu_info}-{disk_capacity}-{mac_address}"
#         return hashlib.md5(fingerprint.encode()).hexdigest().strip().upper()

#     except Exception as e:
#         # 如果出现异常，返回默认指纹
#         print(f"Error generating hardware fingerprint: {e}")
#         return "DEFAULT_FINGERPRINT"


def generate_hardware_fingerprint():
    """生成固定的硬件指纹（基于稳定的硬件信息）"""
    try:
        # 获取 CPU 核心数（固定）
        cpu_cores = str(psutil.cpu_count(logical=False))  # 物理核心数

        # 获取硬盘序列号（固定）
        disk_serial = ""
        try:
            import win32api
            disk_serial = str(win32api.GetVolumeInformation(
                "C:\\")[1])  # Windows 获取 C 盘序列号
        except:
            disk_serial = "UNKNOWN_DISK"

        # 获取主板序列号（固定）
        motherboard_serial = ""
        try:
            import wmi
            c = wmi.WMI()
            motherboard_serial = c.Win32_BaseBoard()[0].SerialNumber.strip()
        except:
            motherboard_serial = "UNKNOWN_MOTHERBOARD"

        # 拼接硬件信息为指纹
        fingerprint = f"{cpu_cores}-{disk_serial}-{motherboard_serial}"
        return hashlib.md5(fingerprint.encode()).hexdigest().strip().upper()

    except Exception as e:
        print(f"Error generating hardware fingerprint: {e}")
        return "DEFAULT_FINGERPRINT"

class UserClient(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        self.setWindowTitle("软件注册（用户端）")
        self.setGeometry(300, 300, 500, 300)

        layout = QVBoxLayout()

        # 硬件指纹标签和文本框（用户可复制）
        self.label_fingerprint = QLabel("您的硬件指纹（请发送给开发者）：")
        self.text_fingerprint = QTextEdit()
        self.text_fingerprint.setReadOnly(True)
        self.text_fingerprint.setFont(QFont("Courier New", 10))
        self.text_fingerprint.setPlainText(generate_hardware_fingerprint())

        # 注册码输入框
        self.label_code = QLabel("请输入开发者提供的注册码：")
        self.input_code = QLineEdit()
        self.input_code.setPlaceholderText("格式：XXXXX-XXXXX-XXXXX-XXXXX-XXXXX")

        # 验证按钮
        self.btn_validate = QPushButton("验证注册码")
        self.btn_validate.clicked.connect(self.validate_registration_code)

        # 添加到布局
        layout.addWidget(self.label_fingerprint)
        layout.addWidget(self.text_fingerprint)
        layout.addWidget(self.label_code)
        layout.addWidget(self.input_code)
        layout.addWidget(self.btn_validate)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def validate_registration_code(self):
        """验证注册码格式和有效性"""
        input_code = self.input_code.text().strip().upper()
        hardware_fingerprint = self.text_fingerprint.toPlainText().strip().upper()

        if len(input_code) != 29 or input_code.count("-") != 4:
            QMessageBox.warning(self, "错误", "注册码格式不正确！")
            return

        # 验证注册码是否符合逻辑
        expected_code = self.generate_registration_code(hardware_fingerprint)
        if input_code == expected_code:
            QMessageBox.information(self, "成功", "注册成功！")
        else:
            QMessageBox.warning(self, "错误", "注册码无效！")

    def generate_registration_code(self, hardware_fingerprint):
        """根据硬件指纹生成注册码"""
        hash_value = hashlib.md5(hardware_fingerprint.encode()).hexdigest()
        code = "-".join([hash_value[i:i+5].upper() for i in range(0, 25, 5)])
        return code


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = UserClient()
    window.show()
    sys.exit(app.exec_())
