# 智慧课堂教学工具 Core v2.0.0

## 概述

智慧课堂教学工具是一套面向教师的综合性教学辅助软件，提供设备管理、教学互动、协作学习等多种功能。

## 主要功能

### 教师专用功能
- **设备看板**: 实时监控教室内所有设备状态
- **随机点名**: 智能点名算法和历史记录管理
- **白板协作**: 多设备实时同步和协作管理
- **设备控制**: 远程设备管理和安全认证
- **主副屏模式**: 双屏内容分离和联动控制
- **虚拟键盘**: 跨设备键盘调用和输入同步
- **小组屏幕看板**: 多路视频流监控和布局管理

### 通用功能
- **UDP设备发现**: 自动发现网络设备
- **弹幕系统**: 实时消息显示和动画效果
- **认证管理**: 用户登录和权限控制
- **通信管理**: 模块间消息通信

## 系统要求

### 最低配置
- 操作系统: Windows 10 / Ubuntu 18.04 / macOS 10.14
- Python: 3.8 或更高版本
- 内存: 4GB RAM
- 存储: 2GB 可用空间
- 网络: 支持UDP广播的局域网环境

### 推荐配置
- 操作系统: Windows 11 / Ubuntu 20.04 / macOS 12.0
- Python: 3.9 或更高版本
- 内存: 8GB RAM
- 存储: 5GB 可用空间
- 网络: 千兆以太网

## 安装和运行

### 1. 安装依赖

```bash
pip install PyQt5 pyyaml requests psutil
```

### 2. 自动安装（推荐）

```bash
# 运行安装脚本，自动安装所有依赖
python install.py

# 或者跳过某些组件
python install.py --skip-mediamtx  # 跳过MediaMTX安装
python install.py --skip-deps      # 跳过Python依赖安装
```

### 3. 手动安装依赖

```bash
# Linux下的虚拟键盘和输入模拟
sudo apt-get install onboard xdotool

# MediaMTX会通过安装脚本自动下载和配置
# 如需手动安装，请参考部署文档
```

### 4. 运行应用程序

#### 方式一：使用安装脚本生成的启动脚本（推荐）
```bash
# Linux/macOS
./start_app.sh

# Windows
start_app.bat
```

#### 方式二：使用启动脚本
```bash
python run_app.py
```

#### 方式三：直接运行主程序
```bash
python main_app.py
```

## 配置

### 配置文件位置
- `config/app_config.yaml`: 应用程序主配置
- `config/user_config.yaml`: 用户个人配置
- `config/modules/`: 各模块的专用配置

### 主要配置项

#### 网络配置
```yaml
network:
  udp_port: 8888      # UDP设备发现端口
  tcp_port: 8889      # TCP通信端口
  http_port: 8890     # HTTP接口端口
  websocket_port: 8891 # WebSocket端口
```

#### 模块配置
```yaml
modules:
  auto_load: true     # 自动加载模块
  load_order:         # 模块加载顺序
    - "udp_discovery"
    - "device_dashboard"
    # ... 其他模块
```

## 使用说明

### 首次启动
1. 运行应用程序
2. 使用教师账号登录（默认: admin/admin）
3. 根据需要启用相应的功能模块
4. 配置网络参数和设备信息

### 功能模块使用

#### 设备看板
- 自动发现网络中的设备
- 实时显示设备状态
- 设备异常告警

#### 随机点名
- 导入学生名单
- 设置点名参数
- 查看点名历史

#### 白板协作
- 启用协作模式
- 选择协作设备
- 实时同步绘制内容

#### 设备控制
- 选择目标设备
- 执行开关机操作
- 确认操作结果

## 故障排除

### 常见问题

1. **应用程序无法启动**
   - 检查Python版本（需要3.8+）
   - 确认依赖包已正确安装
   - 查看日志文件 `logs/app.log`

2. **设备发现失败**
   - 检查网络连接
   - 确认防火墙设置
   - 验证UDP端口8888可用

3. **模块加载失败**
   - 检查模块配置文件
   - 确认文件权限
   - 重置配置到默认值

4. **高DPI显示问题**
   - 应用程序已自动处理高DPI缩放
   - 如有问题，请检查系统显示设置

### 日志文件
- 位置: `logs/app.log`
- 级别: INFO（可在配置中修改）
- 自动轮转: 10MB，保留5个备份

### 重置配置
如果遇到配置问题，可以删除 `config/` 目录，应用程序会自动创建默认配置。

## 开发信息

### 项目结构
```
core/
├── main_app.py              # 主应用程序
├── run_app.py               # 启动脚本
├── auth_manager.py          # 认证管理
├── module_manager.py        # 模块管理
├── communication_manager.py # 通信管理
├── performance_optimizer.py # 性能优化
├── ui_optimizer.py          # UI优化
├── deployment_manager.py    # 部署管理
├── modules/                 # 功能模块目录
├── config/                  # 配置文件目录
├── logs/                    # 日志文件目录
├── tests/                   # 测试文件目录
└── docs/                    # 文档目录
```

### 模块架构
- 基于统一的模块架构设计
- 支持动态加载和卸载
- 模块间通过消息系统通信
- 完整的权限控制机制

## 技术支持

如有问题或建议，请联系开发团队。

## 版本历史

### v2.0.0 (当前版本)
- 重构模块架构
- 新增教师专用功能
- 优化性能和稳定性
- 改进用户界面

### v1.0.0
- 初始版本
- 基础功能实现
