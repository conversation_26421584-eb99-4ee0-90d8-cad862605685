"""
MediaMTX 安装和管理器
用于在生产环境中自动安装和配置MediaMTX
"""
import os
import sys
import platform
import subprocess
import requests
import zipfile
import tarfile
import shutil
from pathlib import Path
from typing import Optional, Dict, Any
import logging
from resource_manager import path_manager

class MediaMTXInstaller:
    """MediaMTX安装器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.system = platform.system().lower()
        self.arch = platform.machine().lower()
        
        # MediaMTX版本和下载URL
        self.version = "v1.8.4"  # 可配置的版本
        self.base_url = "https://github.com/bluenviron/mediamtx/releases/download"
        
        # 安装路径 - 使用统一的路径管理器
        self.install_dir = Path(path_manager.get_app_data_path("mediamtx"))
        if self.system == "windows":
            self.binary_name = "mediamtx.exe"
        else:
            self.binary_name = "mediamtx"

        self.binary_path = self.install_dir / self.binary_name
        # 配置文件路径 - 使用统一的路径管理器
        self.config_path = Path(path_manager.get_config_path("mediamtx.yml"))
    
    def get_download_info(self) -> Optional[Dict[str, str]]:
        """获取下载信息"""
        try:
            # 根据系统和架构确定下载文件
            if self.system == "windows":
                if "64" in self.arch or "amd64" in self.arch:
                    filename = f"mediamtx_{self.version}_windows_amd64.zip"
                else:
                    filename = f"mediamtx_{self.version}_windows_386.zip"
            elif self.system == "linux":
                if "aarch64" in self.arch or "arm64" in self.arch:
                    filename = f"mediamtx_{self.version}_linux_arm64v8.tar.gz"
                elif "arm" in self.arch:
                    filename = f"mediamtx_{self.version}_linux_armv7.tar.gz"
                elif "64" in self.arch or "amd64" in self.arch:
                    filename = f"mediamtx_{self.version}_linux_amd64.tar.gz"
                else:
                    filename = f"mediamtx_{self.version}_linux_386.tar.gz"
            elif self.system == "darwin":  # macOS
                if "arm64" in self.arch:
                    filename = f"mediamtx_{self.version}_darwin_arm64.tar.gz"
                else:
                    filename = f"mediamtx_{self.version}_darwin_amd64.tar.gz"
            else:
                self.logger.error(f"不支持的系统: {self.system}")
                return None
            
            download_url = f"{self.base_url}/{self.version}/{filename}"
            
            return {
                "filename": filename,
                "url": download_url,
                "is_zip": filename.endswith(".zip")
            }
            
        except Exception as e:
            self.logger.error(f"获取下载信息失败: {e}")
            return None
    
    def is_installed(self) -> bool:
        """检查MediaMTX是否已安装"""
        return self.binary_path.exists() and self.binary_path.is_file()
    
    def get_installed_version(self) -> Optional[str]:
        """获取已安装的版本"""
        try:
            if not self.is_installed():
                return None
            
            result = subprocess.run([str(self.binary_path), "--version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                # 解析版本信息
                version_line = result.stdout.strip().split('\n')[0]
                return version_line.split()[-1] if version_line else None
            return None
        except Exception as e:
            self.logger.error(f"获取版本信息失败: {e}")
            return None
    
    def download_mediamtx(self, download_dir: Path) -> Optional[Path]:
        """下载MediaMTX"""
        try:
            download_info = self.get_download_info()
            if not download_info:
                return None
            
            download_dir.mkdir(parents=True, exist_ok=True)
            download_file = download_dir / download_info["filename"]
            
            self.logger.info(f"开始下载MediaMTX: {download_info['url']}")
            
            response = requests.get(download_info["url"], stream=True, timeout=30)
            response.raise_for_status()
            
            with open(download_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            self.logger.info(f"下载完成: {download_file}")
            return download_file
            
        except Exception as e:
            self.logger.error(f"下载MediaMTX失败: {e}")
            return None
    
    def extract_mediamtx(self, archive_path: Path, extract_dir: Path) -> bool:
        """解压MediaMTX"""
        try:
            extract_dir.mkdir(parents=True, exist_ok=True)
            
            if archive_path.suffix == ".zip":
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_dir)
            else:  # tar.gz
                with tarfile.open(archive_path, 'r:gz') as tar_ref:
                    tar_ref.extractall(extract_dir)
            
            self.logger.info(f"解压完成: {extract_dir}")
            return True
            
        except Exception as e:
            self.logger.error(f"解压失败: {e}")
            return False
    
    def install_mediamtx(self, force_reinstall: bool = False) -> bool:
        """安装MediaMTX"""
        try:
            # 检查是否已安装
            if self.is_installed() and not force_reinstall:
                self.logger.info("MediaMTX已安装")
                return True
            
            # 创建临时目录
            temp_dir = Path("temp_mediamtx_install")
            temp_dir.mkdir(exist_ok=True)
            
            try:
                # 下载
                archive_path = self.download_mediamtx(temp_dir)
                if not archive_path:
                    return False
                
                # 解压
                extract_dir = temp_dir / "extracted"
                if not self.extract_mediamtx(archive_path, extract_dir):
                    return False
                
                # 创建安装目录
                self.install_dir.mkdir(parents=True, exist_ok=True)
                
                # 复制二进制文件
                extracted_binary = None
                for file_path in extract_dir.rglob(self.binary_name):
                    extracted_binary = file_path
                    break
                
                if not extracted_binary:
                    self.logger.error(f"未找到二进制文件: {self.binary_name}")
                    return False
                
                shutil.copy2(extracted_binary, self.binary_path)
                
                # 设置执行权限（Linux/macOS）
                if self.system != "windows":
                    os.chmod(self.binary_path, 0o755)
                
                # 创建默认配置
                self.create_default_config()
                
                self.logger.info(f"MediaMTX安装成功: {self.binary_path}")
                return True
                
            finally:
                # 清理临时文件
                shutil.rmtree(temp_dir, ignore_errors=True)
                
        except Exception as e:
            self.logger.error(f"安装MediaMTX失败: {e}")
            return False
    
    def create_default_config(self):
        """创建默认配置文件"""
        try:
            config_content = """
# MediaMTX configuration for Smart Classroom
logLevel: info

# 协议地址配置
rtspAddress: :8554
rtmpAddress: :1935
hlsAddress: :8888
webrtcAddress: :8889

# 性能配置
readTimeout: 30s
writeTimeout: 30s

# API配置
api: yes
apiAddress: :9997

paths:
  # 默认路径配置
  ~^.*$:
    source: publisher
    publishUser: ""
    publishPass: ""
    readUser: ""
    readPass: ""
"""
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                f.write(config_content.strip())
            
            self.logger.info(f"默认配置已创建: {self.config_path}")
            
        except Exception as e:
            self.logger.error(f"创建配置文件失败: {e}")
    
    def start_mediamtx(self, config_file: Optional[str] = None) -> Optional[subprocess.Popen]:
        """启动MediaMTX"""
        try:
            if not self.is_installed():
                self.logger.error("MediaMTX未安装")
                return None
            
            config_path = config_file or str(self.config_path)
            cmd = [str(self.binary_path), config_path]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=str(self.install_dir)
            )
            
            self.logger.info(f"MediaMTX已启动，PID: {process.pid}")
            return process
            
        except Exception as e:
            self.logger.error(f"启动MediaMTX失败: {e}")
            return None
    
    def stop_mediamtx(self, process: subprocess.Popen) -> bool:
        """停止MediaMTX"""
        try:
            if process and process.poll() is None:
                process.terminate()
                process.wait(timeout=10)
                self.logger.info("MediaMTX已停止")
                return True
            return False
        except Exception as e:
            self.logger.error(f"停止MediaMTX失败: {e}")
            return False
    
    def uninstall_mediamtx(self) -> bool:
        """卸载MediaMTX"""
        try:
            if self.install_dir.exists():
                shutil.rmtree(self.install_dir)
                self.logger.info("MediaMTX已卸载")
                return True
            return False
        except Exception as e:
            self.logger.error(f"卸载MediaMTX失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取MediaMTX状态"""
        return {
            "installed": self.is_installed(),
            "version": self.get_installed_version(),
            "binary_path": str(self.binary_path),
            "config_path": str(self.config_path),
            "install_dir": str(self.install_dir)
        }

# 全局实例
mediamtx_installer = MediaMTXInstaller()