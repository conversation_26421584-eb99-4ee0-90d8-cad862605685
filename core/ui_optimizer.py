"""
用户界面优化器
统一各功能模块的界面风格，优化用户操作流程和体验，实现界面响应式设计和适配
"""
import sys
import os
from typing import Dict, List, Any, Optional, Tuple
from PyQt5.QtWidgets import (QWidget, QApplication, QMainWindow, QVBoxLayout, 
                            QHBoxLayout, QLabel, QPushButton, QFrame, QScrollArea,
                            QSizePolicy, QSpacerItem, QGridLayout, QTabWidget,
                            QSplitter, QStackedWidget, QToolBar, QStatusBar,
                            QMenuBar, QMenu, QAction, QActionGroup)
from PyQt5.QtCore import Qt, QSize, QRect, QTimer, pyqtSignal, QObject, QEvent
from PyQt5.QtGui import (QFont, QColor, QPalette, QIcon, QPixmap, QPainter,
                        QFontMetrics, QScreen, QResizeEvent, QShowEvent)
import logging

class UITheme:
    """UI主题类"""
    
    # 颜色方案
    COLORS = {
        'primary': '#007acc',           # 主色调
        'primary_dark': '#005a9e',      # 主色调深色
        'primary_light': '#4da6e0',     # 主色调浅色
        'secondary': '#28a745',         # 次要色调
        'secondary_dark': '#1e7e34',    # 次要色调深色
        'secondary_light': '#5cb85c',   # 次要色调浅色
        'accent': '#ffc107',            # 强调色
        'accent_dark': '#e0a800',       # 强调色深色
        'danger': '#dc3545',            # 危险色
        'warning': '#fd7e14',           # 警告色
        'info': '#17a2b8',              # 信息色
        'success': '#28a745',           # 成功色
        'light': '#f8f9fa',             # 浅色
        'dark': '#343a40',              # 深色
        'white': '#ffffff',             # 白色
        'black': '#000000',             # 黑色
        'gray_100': '#f8f9fa',          # 灰色系列
        'gray_200': '#e9ecef',
        'gray_300': '#dee2e6',
        'gray_400': '#ced4da',
        'gray_500': '#adb5bd',
        'gray_600': '#6c757d',
        'gray_700': '#495057',
        'gray_800': '#343a40',
        'gray_900': '#212529'
    }
    
    # 字体配置
    FONTS = {
        'default_family': 'Microsoft YaHei, SimHei, Arial, sans-serif',
        'monospace_family': 'Consolas, Monaco, monospace',
        'default_size': 12,
        'small_size': 10,
        'large_size': 14,
        'title_size': 16,
        'header_size': 18
    }
    
    # 尺寸配置
    SIZES = {
        'border_radius': 4,
        'border_width': 1,
        'padding_small': 4,
        'padding_medium': 8,
        'padding_large': 16,
        'margin_small': 4,
        'margin_medium': 8,
        'margin_large': 16,
        'button_height': 32,
        'input_height': 28,
        'toolbar_height': 40,
        'statusbar_height': 24
    }
    
    @classmethod
    def get_color(cls, name: str) -> str:
        """获取颜色"""
        return cls.COLORS.get(name, cls.COLORS['primary'])
    
    @classmethod
    def get_font(cls, size: str = 'default', family: str = 'default') -> QFont:
        """获取字体"""
        font_family = cls.FONTS.get(f'{family}_family', cls.FONTS['default_family'])
        font_size = cls.FONTS.get(f'{size}_size', cls.FONTS['default_size'])
        return QFont(font_family, font_size)
    
    @classmethod
    def get_size(cls, name: str) -> int:
        """获取尺寸"""
        return cls.SIZES.get(name, 0)

class StyleSheetGenerator:
    """样式表生成器"""
    
    @staticmethod
    def generate_button_style(
        bg_color: str = None,
        text_color: str = None,
        border_color: str = None,
        hover_bg_color: str = None,
        pressed_bg_color: str = None,
        disabled_bg_color: str = None
    ) -> str:
        """生成按钮样式"""
        theme = UITheme()
        
        bg_color = bg_color or theme.get_color('primary')
        text_color = text_color or theme.get_color('white')
        border_color = border_color or bg_color
        hover_bg_color = hover_bg_color or theme.get_color('primary_dark')
        pressed_bg_color = pressed_bg_color or theme.get_color('primary_dark')
        disabled_bg_color = disabled_bg_color or theme.get_color('gray_400')
        
        return f"""
        QPushButton {{
            background-color: {bg_color};
            color: {text_color};
            border: {theme.get_size('border_width')}px solid {border_color};
            border-radius: {theme.get_size('border_radius')}px;
            padding: {theme.get_size('padding_medium')}px {theme.get_size('padding_large')}px;
            font-size: {theme.get_size('default_size')}px;
            font-weight: bold;
            min-height: {theme.get_size('button_height')}px;
        }}
        QPushButton:hover {{
            background-color: {hover_bg_color};
        }}
        QPushButton:pressed {{
            background-color: {pressed_bg_color};
        }}
        QPushButton:disabled {{
            background-color: {disabled_bg_color};
            color: {theme.get_color('gray_600')};
        }}
        """
    
    @staticmethod
    def generate_input_style(
        bg_color: str = None,
        text_color: str = None,
        border_color: str = None,
        focus_border_color: str = None
    ) -> str:
        """生成输入框样式"""
        theme = UITheme()
        
        bg_color = bg_color or theme.get_color('white')
        text_color = text_color or theme.get_color('dark')
        border_color = border_color or theme.get_color('gray_400')
        focus_border_color = focus_border_color or theme.get_color('primary')
        
        return f"""
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {{
            background-color: {bg_color};
            color: {text_color};
            border: {theme.get_size('border_width')}px solid {border_color};
            border-radius: {theme.get_size('border_radius')}px;
            padding: {theme.get_size('padding_medium')}px;
            font-size: {theme.get_size('default_size')}px;
            min-height: {theme.get_size('input_height')}px;
        }}
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, 
        QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {{
            border-color: {focus_border_color};
        }}
        """
    
    @staticmethod
    def generate_panel_style(
        bg_color: str = None,
        border_color: str = None,
        title_bg_color: str = None,
        title_text_color: str = None
    ) -> str:
        """生成面板样式"""
        theme = UITheme()
        
        bg_color = bg_color or theme.get_color('white')
        border_color = border_color or theme.get_color('gray_300')
        title_bg_color = title_bg_color or theme.get_color('gray_100')
        title_text_color = title_text_color or theme.get_color('dark')
        
        return f"""
        QGroupBox {{
            background-color: {bg_color};
            border: {theme.get_size('border_width')}px solid {border_color};
            border-radius: {theme.get_size('border_radius')}px;
            margin-top: {theme.get_size('margin_medium')}px;
            padding-top: {theme.get_size('padding_large')}px;
            font-weight: bold;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: {theme.get_size('padding_medium')}px;
            padding: 0 {theme.get_size('padding_medium')}px 0 {theme.get_size('padding_medium')}px;
            background-color: {title_bg_color};
            color: {title_text_color};
        }}
        QFrame {{
            background-color: {bg_color};
            border: {theme.get_size('border_width')}px solid {border_color};
            border-radius: {theme.get_size('border_radius')}px;
        }}
        """
    
    @staticmethod
    def generate_table_style(
        bg_color: str = None,
        alternate_bg_color: str = None,
        header_bg_color: str = None,
        header_text_color: str = None,
        selection_bg_color: str = None
    ) -> str:
        """生成表格样式"""
        theme = UITheme()
        
        bg_color = bg_color or theme.get_color('white')
        alternate_bg_color = alternate_bg_color or theme.get_color('gray_100')
        header_bg_color = header_bg_color or theme.get_color('gray_200')
        header_text_color = header_text_color or theme.get_color('dark')
        selection_bg_color = selection_bg_color or theme.get_color('primary_light')
        
        return f"""
        QTableWidget, QTableView {{
            background-color: {bg_color};
            alternate-background-color: {alternate_bg_color};
            selection-background-color: {selection_bg_color};
            gridline-color: {theme.get_color('gray_300')};
            border: {theme.get_size('border_width')}px solid {theme.get_color('gray_300')};
        }}
        QHeaderView::section {{
            background-color: {header_bg_color};
            color: {header_text_color};
            padding: {theme.get_size('padding_medium')}px;
            border: none;
            border-bottom: {theme.get_size('border_width')}px solid {theme.get_color('gray_400')};
            font-weight: bold;
        }}
        """

class ResponsiveLayout:
    """响应式布局管理器"""
    
    BREAKPOINTS = {
        'xs': 576,   # 超小屏幕
        'sm': 768,   # 小屏幕
        'md': 992,   # 中等屏幕
        'lg': 1200,  # 大屏幕
        'xl': 1400   # 超大屏幕
    }
    
    @classmethod
    def get_screen_size_category(cls, width: int) -> str:
        """获取屏幕尺寸类别"""
        if width < cls.BREAKPOINTS['xs']:
            return 'xs'
        elif width < cls.BREAKPOINTS['sm']:
            return 'sm'
        elif width < cls.BREAKPOINTS['md']:
            return 'md'
        elif width < cls.BREAKPOINTS['lg']:
            return 'lg'
        else:
            return 'xl'
    
    @classmethod
    def adjust_layout_for_screen_size(cls, widget: QWidget, screen_category: str):
        """根据屏幕尺寸调整布局"""
        if screen_category in ['xs', 'sm']:
            # 小屏幕：垂直布局，隐藏非必要元素
            cls._apply_mobile_layout(widget)
        elif screen_category == 'md':
            # 中等屏幕：紧凑布局
            cls._apply_compact_layout(widget)
        else:
            # 大屏幕：标准布局
            cls._apply_standard_layout(widget)
    
    @classmethod
    def _apply_mobile_layout(cls, widget: QWidget):
        """应用移动端布局"""
        # 减小边距和间距
        if hasattr(widget, 'layout') and widget.layout():
            layout = widget.layout()
            layout.setContentsMargins(4, 4, 4, 4)
            layout.setSpacing(4)
        
        # 隐藏工具栏文字，只显示图标
        for toolbar in widget.findChildren(QToolBar):
            for action in toolbar.actions():
                action.setText("")
    
    @classmethod
    def _apply_compact_layout(cls, widget: QWidget):
        """应用紧凑布局"""
        if hasattr(widget, 'layout') and widget.layout():
            layout = widget.layout()
            layout.setContentsMargins(8, 8, 8, 8)
            layout.setSpacing(6)
    
    @classmethod
    def _apply_standard_layout(cls, widget: QWidget):
        """应用标准布局"""
        if hasattr(widget, 'layout') and widget.layout():
            layout = widget.layout()
            layout.setContentsMargins(16, 16, 16, 16)
            layout.setSpacing(8)

class UIOptimizer(QObject):
    """UI优化器主类"""
    
    theme_changed = pyqtSignal(str)  # 主题改变信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = 'default'
        self.responsive_enabled = True
        self.animation_enabled = True
        self.accessibility_enabled = False
        
        self.logger = logging.getLogger(__name__)
        
        # 监听屏幕变化
        self._setup_screen_monitoring()
    
    def _setup_screen_monitoring(self):
        """设置屏幕监控"""
        try:
            app = QApplication.instance()
            if app:
                app.screenAdded.connect(self._on_screen_changed)
                app.screenRemoved.connect(self._on_screen_changed)
                app.primaryScreenChanged.connect(self._on_screen_changed)
        except Exception as e:
            self.logger.error(f"设置屏幕监控失败: {e}")
    
    def _on_screen_changed(self):
        """屏幕变化处理"""
        if self.responsive_enabled:
            self._update_all_widgets_layout()
    
    def _update_all_widgets_layout(self):
        """更新所有窗口的布局"""
        try:
            app = QApplication.instance()
            if app:
                for widget in app.allWidgets():
                    if isinstance(widget, QMainWindow) and widget.isVisible():
                        self.optimize_widget_ui(widget)
        except Exception as e:
            self.logger.error(f"更新窗口布局失败: {e}")
    
    def apply_theme_to_application(self, theme_name: str = 'default'):
        """为整个应用程序应用主题"""
        try:
            app = QApplication.instance()
            if not app:
                return
            
            # 生成全局样式表
            stylesheet = self._generate_global_stylesheet(theme_name)
            app.setStyleSheet(stylesheet)
            
            # 设置应用程序字体
            app.setFont(UITheme.get_font())
            
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)
            
            self.logger.info(f"已应用主题: {theme_name}")
            
        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
    
    def _generate_global_stylesheet(self, theme_name: str) -> str:
        """生成全局样式表"""
        generator = StyleSheetGenerator()
        
        # 基础样式
        base_style = f"""
        QApplication {{
            font-family: {UITheme.FONTS['default_family']};
            font-size: {UITheme.FONTS['default_size']}px;
        }}
        QMainWindow {{
            background-color: {UITheme.get_color('light')};
        }}
        QWidget {{
            background-color: {UITheme.get_color('white')};
            color: {UITheme.get_color('dark')};
        }}
        """
        
        # 组合所有样式
        button_style = generator.generate_button_style()
        input_style = generator.generate_input_style()
        panel_style = generator.generate_panel_style()
        table_style = generator.generate_table_style()
        
        return base_style + button_style + input_style + panel_style + table_style
    
    def optimize_widget_ui(self, widget: QWidget):
        """优化单个窗口的UI"""
        try:
            # 获取屏幕信息
            screen = widget.screen() if hasattr(widget, 'screen') else QApplication.primaryScreen()
            screen_size = screen.size()
            screen_category = ResponsiveLayout.get_screen_size_category(screen_size.width())
            
            # 应用响应式布局
            if self.responsive_enabled:
                ResponsiveLayout.adjust_layout_for_screen_size(widget, screen_category)
            
            # 优化字体大小
            self._optimize_font_sizes(widget, screen_category)
            
            # 优化控件尺寸
            self._optimize_widget_sizes(widget, screen_category)
            
            # 应用无障碍优化
            if self.accessibility_enabled:
                self._apply_accessibility_features(widget)
            
            self.logger.debug(f"已优化窗口UI: {widget.__class__.__name__}")
            
        except Exception as e:
            self.logger.error(f"优化窗口UI失败: {e}")
    
    def _optimize_font_sizes(self, widget: QWidget, screen_category: str):
        """优化字体大小"""
        try:
            # 根据屏幕尺寸调整字体
            if screen_category in ['xs', 'sm']:
                base_size = UITheme.FONTS['small_size']
            elif screen_category == 'md':
                base_size = UITheme.FONTS['default_size']
            else:
                base_size = UITheme.FONTS['large_size']
            
            # 应用到所有子控件
            for child in widget.findChildren(QWidget):
                if hasattr(child, 'font'):
                    font = child.font()
                    font.setPointSize(base_size)
                    child.setFont(font)
                    
        except Exception as e:
            self.logger.error(f"优化字体大小失败: {e}")
    
    def _optimize_widget_sizes(self, widget: QWidget, screen_category: str):
        """优化控件尺寸"""
        try:
            # 根据屏幕尺寸调整控件大小
            if screen_category in ['xs', 'sm']:
                # 小屏幕：增大触摸目标
                min_button_size = QSize(44, 44)  # 移动端推荐最小触摸尺寸
            else:
                min_button_size = QSize(32, 32)
            
            # 应用到按钮
            for button in widget.findChildren(QPushButton):
                button.setMinimumSize(min_button_size)
                
        except Exception as e:
            self.logger.error(f"优化控件尺寸失败: {e}")
    
    def _apply_accessibility_features(self, widget: QWidget):
        """应用无障碍功能"""
        try:
            # 设置工具提示
            for child in widget.findChildren(QWidget):
                if not child.toolTip() and hasattr(child, 'text'):
                    if hasattr(child, 'text') and callable(child.text):
                        text = child.text()
                        if text:
                            child.setToolTip(text)
            
            # 设置快捷键
            for button in widget.findChildren(QPushButton):
                if not button.shortcut().isEmpty():
                    continue
                text = button.text()
                if '&' not in text and text:
                    # 为没有快捷键的按钮添加快捷键
                    first_char = text[0].upper()
                    button.setText(f"&{first_char}{text[1:]}")
                    
        except Exception as e:
            self.logger.error(f"应用无障碍功能失败: {e}")
    
    def enable_responsive_design(self, enabled: bool = True):
        """启用/禁用响应式设计"""
        self.responsive_enabled = enabled
        if enabled:
            self._update_all_widgets_layout()
    
    def enable_accessibility(self, enabled: bool = True):
        """启用/禁用无障碍功能"""
        self.accessibility_enabled = enabled
        if enabled:
            self._update_all_widgets_layout()
    
    def get_ui_metrics(self) -> Dict[str, Any]:
        """获取UI指标"""
        try:
            app = QApplication.instance()
            if not app:
                return {}
            
            screens = app.screens()
            widgets = app.allWidgets()
            
            return {
                'screen_count': len(screens),
                'primary_screen_size': screens[0].size().toTuple() if screens else (0, 0),
                'widget_count': len(widgets),
                'visible_widgets': len([w for w in widgets if w.isVisible()]),
                'current_theme': self.current_theme,
                'responsive_enabled': self.responsive_enabled,
                'accessibility_enabled': self.accessibility_enabled
            }
        except Exception as e:
            self.logger.error(f"获取UI指标失败: {e}")
            return {}
