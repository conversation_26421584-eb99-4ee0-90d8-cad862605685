#!/usr/bin/env python3
"""
路径管理器测试脚本
用于验证新的文件路径管理系统在开发环境和打包环境中的正确性
"""
import os
import sys
import json
import tempfile
import shutil
from pathlib import Path

def test_path_manager():
    """测试路径管理器功能"""
    print("=" * 60)
    print("路径管理器测试")
    print("=" * 60)
    
    try:
        from resource_manager import path_manager
        
        # 测试基本路径获取
        print("\n1. 测试基本路径获取:")
        print(f"应用数据目录: {path_manager.app_data_dir}")
        print(f"配置目录: {path_manager.config_dir}")
        print(f"数据库目录: {path_manager.database_dir}")
        print(f"日志目录: {path_manager.logs_dir}")
        print(f"临时目录: {path_manager.temp_dir}")
        print(f"是否为打包环境: {path_manager.is_packaged}")
        
        # 测试文件路径获取
        print("\n2. 测试文件路径获取:")
        config_path = path_manager.get_config_path("network_config.yaml")
        database_path = path_manager.get_database_path("recording_history.json")
        log_path = path_manager.get_log_path("smart_classroom.log")
        app_data_path = path_manager.get_app_data_path("auth_token.json")
        
        print(f"配置文件路径: {config_path}")
        print(f"数据库文件路径: {database_path}")
        print(f"日志文件路径: {log_path}")
        print(f"应用数据文件路径: {app_data_path}")
        
        # 测试目录创建
        print("\n3. 测试目录创建:")
        directories = [
            path_manager.config_dir,
            path_manager.database_dir,
            path_manager.logs_dir,
            path_manager.temp_dir
        ]
        
        for directory in directories:
            if os.path.exists(directory):
                print(f"✓ 目录存在: {directory}")
            else:
                print(f"✗ 目录不存在: {directory}")
        
        # 测试文件读写
        print("\n4. 测试文件读写:")
        test_config = {
            "test": True,
            "timestamp": "2024-01-01T00:00:00",
            "data": {"key": "value"}
        }
        
        # 测试配置文件写入
        test_config_path = path_manager.get_config_path("test_config.json")
        try:
            with open(test_config_path, 'w', encoding='utf-8') as f:
                json.dump(test_config, f, ensure_ascii=False, indent=2)
            print(f"✓ 配置文件写入成功: {test_config_path}")
            
            # 测试读取
            with open(test_config_path, 'r', encoding='utf-8') as f:
                loaded_config = json.load(f)
            if loaded_config == test_config:
                print("✓ 配置文件读取成功")
            else:
                print("✗ 配置文件读取失败")
                
        except Exception as e:
            print(f"✗ 配置文件操作失败: {e}")
        
        # 测试数据库文件写入
        test_db_path = path_manager.get_database_path("test_history.json")
        try:
            test_history = [
                {"id": 1, "action": "test", "timestamp": "2024-01-01T00:00:00"},
                {"id": 2, "action": "test2", "timestamp": "2024-01-01T00:01:00"}
            ]
            
            with open(test_db_path, 'w', encoding='utf-8') as f:
                json.dump(test_history, f, ensure_ascii=False, indent=2)
            print(f"✓ 数据库文件写入成功: {test_db_path}")
            
        except Exception as e:
            print(f"✗ 数据库文件操作失败: {e}")
        
        # 测试用户数据目录
        print("\n5. 测试用户数据目录:")
        user_data_dir = path_manager.get_user_data_directory()
        user_subdir = path_manager.get_user_data_directory("screenshots")
        print(f"用户数据目录: {user_data_dir}")
        print(f"用户子目录: {user_subdir}")
        
        if os.path.exists(user_data_dir):
            print("✓ 用户数据目录创建成功")
        else:
            print("✗ 用户数据目录创建失败")
            
        if os.path.exists(user_subdir):
            print("✓ 用户子目录创建成功")
        else:
            print("✗ 用户子目录创建失败")
        
        print("\n6. 测试文件迁移功能:")
        # 创建一些测试文件来模拟旧文件
        core_dir = os.path.dirname(os.path.abspath(__file__))
        test_files = {
            "test_auth_token.json": {"token": "test_token", "user": "test_user"},
            "test_broadcast_history.json": [{"id": 1, "action": "broadcast"}]
        }
        
        # 创建测试文件
        for filename, content in test_files.items():
            test_file_path = os.path.join(core_dir, filename)
            try:
                with open(test_file_path, 'w', encoding='utf-8') as f:
                    json.dump(content, f, ensure_ascii=False, indent=2)
                print(f"✓ 创建测试文件: {test_file_path}")
            except Exception as e:
                print(f"✗ 创建测试文件失败: {e}")
        
        print("\n测试完成!")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_network_config():
    """测试网络配置管理器"""
    print("\n" + "=" * 60)
    print("网络配置管理器测试")
    print("=" * 60)
    
    try:
        from network_config import NetworkConfigManager
        
        # 创建网络配置管理器实例
        config_manager = NetworkConfigManager()
        
        print(f"配置文件路径: {config_manager.config_file}")
        print(f"配置内容: {config_manager.config}")
        
        # 测试配置保存和加载
        test_config = config_manager.config.copy()
        test_config['test_key'] = 'test_value'
        
        # 这里不直接修改配置，只是验证路径是否正确
        if os.path.exists(config_manager.config_file.parent):
            print("✓ 网络配置目录存在")
        else:
            print("✗ 网络配置目录不存在")
            
        return True
        
    except Exception as e:
        print(f"网络配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    print("\n清理测试文件...")
    
    try:
        from resource_manager import path_manager
        
        # 清理测试配置文件
        test_files = [
            path_manager.get_config_path("test_config.json"),
            path_manager.get_database_path("test_history.json")
        ]
        
        for test_file in test_files:
            if os.path.exists(test_file):
                os.remove(test_file)
                print(f"✓ 删除测试文件: {test_file}")
        
        # 清理core目录下的测试文件
        core_dir = os.path.dirname(os.path.abspath(__file__))
        core_test_files = [
            "test_auth_token.json",
            "test_broadcast_history.json"
        ]
        
        for filename in core_test_files:
            test_file_path = os.path.join(core_dir, filename)
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
                print(f"✓ 删除测试文件: {test_file_path}")
                
    except Exception as e:
        print(f"清理测试文件失败: {e}")

def main():
    """主函数"""
    print("智慧课堂路径管理器测试工具")
    
    success = True
    
    # 测试路径管理器
    if not test_path_manager():
        success = False
    
    # 测试网络配置管理器
    if not test_network_config():
        success = False
    
    # 清理测试文件
    cleanup_test_files()
    
    print("\n" + "=" * 60)
    if success:
        print("所有测试通过! ✓")
        return 0
    else:
        print("部分测试失败! ✗")
        return 1

if __name__ == "__main__":
    sys.exit(main())
