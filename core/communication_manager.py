"""
模块间通信管理器
"""
from PyQt5.QtCore import QObject, pyqtSignal
from typing import Dict, Any, List, Optional, Callable
import logging
from collections import defaultdict
import json

class CommunicationManager(QObject):
    """模块间通信管理器"""
    
    # 通信信号
    message_sent = pyqtSignal(str, str, str, dict)  # sender, target, type, data
    message_broadcast = pyqtSignal(str, str, dict)  # sender, type, data
    
    def __init__(self):
        super().__init__()
        self.modules: Dict[str, object] = {}  # 注册的模块
        self.message_handlers: Dict[str, List[Callable]] = defaultdict(list)  # 消息处理器
        self.message_history: List[Dict[str, Any]] = []  # 消息历史
        self.logger = logging.getLogger("communication_manager")
        
        # 消息统计
        self.message_stats = {
            "sent": 0,
            "broadcast": 0,
            "failed": 0
        }
    
    def register_module(self, module):
        """注册模块"""
        module_name = module.module_name
        self.modules[module_name] = module
        self.logger.info(f"模块已注册: {module_name}")
        
        # 连接模块的消息信号
        if hasattr(module, 'module_message'):
            module.module_message.connect(self._on_module_message)
    
    def unregister_module(self, module_name: str):
        """注销模块"""
        if module_name in self.modules:
            del self.modules[module_name]
            self.logger.info(f"模块已注销: {module_name}")
    
    def send_message(self, sender: str, target: str, message_type: str, data: Dict[str, Any]):
        """发送消息到指定模块"""
        try:
            # 检查目标模块是否存在
            if target not in self.modules:
                self.logger.warning(f"目标模块不存在: {target}")
                self.message_stats["failed"] += 1
                return False
            
            # 记录消息
            message_record = {
                "timestamp": self._get_timestamp(),
                "sender": sender,
                "target": target,
                "type": message_type,
                "data": data,
                "delivery_type": "direct"
            }
            self.message_history.append(message_record)
            
            # 发送消息
            target_module = self.modules[target]
            if hasattr(target_module, 'handle_message'):
                target_module.handle_message(sender, message_type, data)
                self.message_sent.emit(sender, target, message_type, data)
                self.message_stats["sent"] += 1
                self.logger.debug(f"消息已发送: {sender} -> {target} ({message_type})")
                return True
            else:
                self.logger.warning(f"目标模块不支持消息处理: {target}")
                self.message_stats["failed"] += 1
                return False
                
        except Exception as e:
            self.logger.error(f"发送消息失败: {str(e)}")
            self.message_stats["failed"] += 1
            return False
    
    def broadcast_message(self, sender: str, message_type: str, data: Dict[str, Any]):
        """广播消息到所有模块"""
        try:
            # 记录消息
            message_record = {
                "timestamp": self._get_timestamp(),
                "sender": sender,
                "target": "all",
                "type": message_type,
                "data": data,
                "delivery_type": "broadcast"
            }
            self.message_history.append(message_record)
            
            # 广播到所有模块（除了发送者）
            delivered_count = 0
            for module_name, module in self.modules.items():
                if module_name != sender and hasattr(module, 'handle_message'):
                    try:
                        module.handle_message(sender, message_type, data)
                        delivered_count += 1
                    except Exception as e:
                        self.logger.error(f"广播消息到 {module_name} 失败: {str(e)}")
            
            self.message_broadcast.emit(sender, message_type, data)
            self.message_stats["broadcast"] += 1
            self.logger.debug(f"消息已广播: {sender} ({message_type}) -> {delivered_count} 个模块")
            return delivered_count > 0
            
        except Exception as e:
            self.logger.error(f"广播消息失败: {str(e)}")
            self.message_stats["failed"] += 1
            return False
    
    def register_message_handler(self, message_type: str, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type].append(handler)
        self.logger.debug(f"消息处理器已注册: {message_type}")
    
    def unregister_message_handler(self, message_type: str, handler: Callable):
        """注销消息处理器"""
        if message_type in self.message_handlers:
            try:
                self.message_handlers[message_type].remove(handler)
                self.logger.debug(f"消息处理器已注销: {message_type}")
            except ValueError:
                pass
    
    def get_registered_modules(self) -> List[str]:
        """获取已注册的模块列表"""
        return list(self.modules.keys())
    
    def get_module_info(self, module_name: str) -> Optional[Dict[str, Any]]:
        """获取模块信息"""
        module = self.modules.get(module_name)
        if module and hasattr(module, 'get_module_info'):
            return module.get_module_info()
        return None
    
    def get_all_modules_info(self) -> Dict[str, Dict[str, Any]]:
        """获取所有模块信息"""
        info = {}
        for name, module in self.modules.items():
            if hasattr(module, 'get_module_info'):
                info[name] = module.get_module_info()
        return info
    
    def get_message_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取消息历史"""
        return self.message_history[-limit:]
    
    def get_message_stats(self) -> Dict[str, int]:
        """获取消息统计"""
        return self.message_stats.copy()
    
    def clear_message_history(self):
        """清空消息历史"""
        self.message_history.clear()
        self.logger.info("消息历史已清空")
    
    def is_module_registered(self, module_name: str) -> bool:
        """检查模块是否已注册"""
        return module_name in self.modules
    
    def _on_module_message(self, message_type: str, data: Dict[str, Any]):
        """处理模块发出的消息信号"""
        # 调用注册的消息处理器
        for handler in self.message_handlers.get(message_type, []):
            try:
                handler(data)
            except Exception as e:
                self.logger.error(f"消息处理器执行失败: {str(e)}")
    
    def _get_timestamp(self) -> str:
        """获取时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()

class MessageBus(QObject):
    """消息总线 - 提供更高级的消息传递功能"""
    
    def __init__(self, comm_manager: CommunicationManager):
        super().__init__()
        self.comm_manager = comm_manager
        self.subscriptions: Dict[str, List[str]] = defaultdict(list)  # 主题订阅
        self.logger = logging.getLogger("message_bus")
    
    def subscribe(self, module_name: str, topic: str):
        """订阅主题"""
        if module_name not in self.subscriptions[topic]:
            self.subscriptions[topic].append(module_name)
            self.logger.debug(f"模块 {module_name} 订阅主题: {topic}")
    
    def unsubscribe(self, module_name: str, topic: str):
        """取消订阅主题"""
        if module_name in self.subscriptions[topic]:
            self.subscriptions[topic].remove(module_name)
            self.logger.debug(f"模块 {module_name} 取消订阅主题: {topic}")
    
    def publish(self, sender: str, topic: str, data: Dict[str, Any]):
        """发布消息到主题"""
        subscribers = self.subscriptions.get(topic, [])
        if not subscribers:
            self.logger.debug(f"主题 {topic} 没有订阅者")
            return 0
        
        delivered_count = 0
        for subscriber in subscribers:
            if subscriber != sender:  # 不发送给自己
                if self.comm_manager.send_message(sender, subscriber, f"topic:{topic}", data):
                    delivered_count += 1
        
        self.logger.debug(f"主题消息已发布: {topic} -> {delivered_count} 个订阅者")
        return delivered_count
    
    def get_topic_subscribers(self, topic: str) -> List[str]:
        """获取主题订阅者"""
        return self.subscriptions.get(topic, []).copy()
    
    def get_all_topics(self) -> List[str]:
        """获取所有主题"""
        return list(self.subscriptions.keys())

# 全局通信管理器实例
_global_comm_manager = None
_global_message_bus = None

def get_communication_manager() -> CommunicationManager:
    """获取全局通信管理器实例"""
    global _global_comm_manager
    if _global_comm_manager is None:
        _global_comm_manager = CommunicationManager()
    return _global_comm_manager

def get_message_bus() -> MessageBus:
    """获取全局消息总线实例"""
    global _global_message_bus
    if _global_message_bus is None:
        _global_message_bus = MessageBus(get_communication_manager())
    return _global_message_bus
