
import subprocess

def find_apt_packages(pip_packages):
    apt_packages = []
    for pkg in pip_packages:
        try:
            # Sanitize package name for search
            search_term = pkg.split('==')[0].replace('_', '-')
            
            # Search for the package
            result = subprocess.run(['apt-cache', 'search', f'python3-{search_term}'], capture_output=True, text=True, check=True)
            
            # Find the best match
            best_match = ''
            for line in result.stdout.splitlines():
                package_name = line.split(' ')[0]
                if f'python3-{search_term}' == package_name:
                    best_match = package_name
                    break
                elif f'python3-{search_term.lower()}' == package_name:
                    best_match = package_name
                    break
            
            if best_match:
                apt_packages.append(best_match)
            else:
                # If no direct match, try a broader search
                result = subprocess.run(['apt-cache', 'search', search_term], capture_output=True, text=True, check=True)
                # A more complex logic might be needed here to pick the right package
                # For now, we'll just log that we couldn't find a direct match
                print(f"Could not find a direct match for {pkg}")

        except subprocess.CalledProcessError:
            print(f"Error searching for {pkg}")
            
    return apt_packages

if __name__ == '__main__':
    with open('/home/<USER>/Desktop/交付项目/智慧课堂系统（平台）/smart_classroom/core/requirements.txt', 'r') as f:
        pip_packages = f.read().splitlines()
    
    apt_packages = find_apt_packages(pip_packages)
    
    with open('/home/<USER>/Desktop/交付项目/智慧课堂系统（平台）/smart_classroom/deb_build/DEBIAN/control', 'w') as f:
        f.write('Package: smart-classroom-tool\n')
        f.write('Version: 2.0.0\n')
        f.write('Maintainer: Mastaji\n')
        f.write('Architecture: all\n')
        f.write(f'Depends: python3, python3-pip, {', '.join(apt_packages)}\n')
        f.write('Description: 智慧课堂系统工具\n')
        f.write(' 智慧课堂系统核心应用\n')
