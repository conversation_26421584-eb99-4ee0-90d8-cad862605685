智慧课堂教学工具 v1.0.0
2025-08-07 11:31:38,626 - root - INFO - Logging to /home/<USER>/Desktop/交付项目/智慧课堂系统（平台）/smart_classroom/core/dist/data/logs/smart_classroom.log
2025-08-07 11:31:38,626 - root - INFO - Current working directory: /home/<USER>/Desktop/交付项目/智慧课堂系统（平台）/smart_classroom/core/dist
2025-08-07 11:31:38,627 - root - INFO - Python executable: /home/<USER>/Desktop/交付项目/智慧课堂系统（平台）/smart_classroom/core/dist/smart_classroom
2025-08-07 11:31:38,627 - __main__ - INFO - 路径管理器初始化完成
2025-08-07 11:31:38,750 - __main__ - INFO - 启动智慧课堂教学工具...
2025-08-07 11:31:38,806 - resource_manager - INFO - Setting qtawesome font directory to: /tmp/_MEInDQMvu/qtawesome/fonts
2025-08-07 11:31:38,806 - resource_manager - ERROR - An error occurred during qtawesome initialization: "Invalid option 'font_dir'"
2025-08-07 11:31:38,822 - communication_manager - DEBUG - 消息处理器已注册: module_request
2025-08-07 11:31:38,822 - communication_manager - DEBUG - 消息处理器已注册: module_status
2025-08-07 11:31:38,822 - resource_manager - DEBUG - Using PyInstaller path: /tmp/_MEInDQMvu
2025-08-07 11:31:38,822 - resource_manager - DEBUG - Resource found: /tmp/_MEInDQMvu/assets/images/logo.png
2025-08-07 11:31:38,827 - resource_manager - DEBUG - Using PyInstaller path: /tmp/_MEInDQMvu
2025-08-07 11:31:38,827 - resource_manager - DEBUG - Resource found: /tmp/_MEInDQMvu/assets/images/logo.png
2025-08-07 11:31:38,827 - root - INFO - Loading logo from: /tmp/_MEInDQMvu/assets/images/logo.png
2025-08-07 11:31:38,827 - root - INFO - Logo file exists: True
2025-08-07 11:31:38,836 - resource_manager - DEBUG - Using PyInstaller path: /tmp/_MEInDQMvu
2025-08-07 11:31:38,836 - resource_manager - DEBUG - Resource found: /tmp/_MEInDQMvu/assets/images/logo.png
2025-08-07 11:31:38,837 - root - INFO - Loading logo for widget from: /tmp/_MEInDQMvu/assets/images/logo.png
2025-08-07 11:31:38,837 - root - INFO - Logo file for widget exists: True
2025-08-07 11:31:38,838 - root - INFO - Logo loaded successfully
2025-08-07 11:31:38,866 - communication_manager - INFO - 模块已注册: udp_discovery
2025-08-07 11:31:38,866 - module.udp_discovery - INFO - 正在初始化模块: UDP设备发现
2025-08-07 11:31:38,867 - getmac - DEBUG - Initializing 'default_iface' method cache (platform: 'linux')
2025-08-07 11:31:38,867 - getmac - DEBUG - Finished initializing 'default_iface' method cache
2025-08-07 11:31:38,867 - getmac - DEBUG - Initializing 'iface' method cache (platform: 'linux')
2025-08-07 11:31:38,867 - getmac - DEBUG - Finished initializing 'iface' method cache
2025-08-07 11:31:38,867 - getmac - DEBUG - Raw MAC found: ec:60:73:e8:05:79

2025-08-07 11:31:38,868 - module.udp_discovery - INFO - UDP设备发现模块初始化成功
2025-08-07 11:31:38,868 - module.udp_discovery - INFO - 模块初始化成功: UDP设备发现
模块 udp_discovery 加载成功
模块 udp_discovery 加载成功
2025-08-07 11:31:38,868 - module_manager - INFO - 模块加载成功: udp_discovery
2025-08-07 11:31:38,868 - module.udp_discovery - INFO - UDP设备发现已启动
UDP设备发现已自动启动
2025-08-07 11:31:39,117 - module.udp_discovery - INFO - 发现新设备: admin-PC (192.168.0.4)
2025-08-07 11:31:40,621 - communication_manager - INFO - 模块已注册: group_screen_monitor
2025-08-07 11:31:40,621 - module.group_screen_monitor - INFO - 正在初始化模块: 小组屏幕看板
2025-08-07 11:31:40,623 - module.group_screen_monitor - WARNING - MediaMTX不可用，将使用备用方案
2025-08-07 11:31:40,625 - module.group_screen_monitor - INFO - 设备发现设置完成，当前有 1 个设备
2025-08-07 11:31:40,626 - vlc_loader - INFO - 检测到打包环境: /tmp/_MEInDQMvu
2025-08-07 11:31:40,626 - vlc_loader - INFO - 设置LD_LIBRARY_PATH: /tmp/_MEInDQMvu:/tmp/_MEInDQMvu
2025-08-07 11:31:40,626 - vlc_loader - INFO - 尝试从打包目录预加载VLC库: /tmp/_MEInDQMvu
2025-08-07 11:31:40,627 - vlc_loader - INFO - 找到的VLC相关文件: ['libvlccore.so.9', 'libvlc.so.5.6.0', 'libvlccore.so.9.0.0', 'libvlc.so.5']
2025-08-07 11:31:40,628 - vlc_loader - INFO - 预加载库文件成功: libvlccore.so.9.0.0
2025-08-07 11:31:40,629 - vlc_loader - INFO - 预加载库文件成功: libvlccore.so.9
2025-08-07 11:31:40,629 - vlc_loader - INFO - 预加载库文件成功: libvlc.so.5.6.0
2025-08-07 11:31:40,629 - vlc_loader - INFO - 预加载库文件成功: libvlc.so.5
2025-08-07 11:31:40,629 - vlc_loader - DEBUG - 库文件不存在: /tmp/_MEInDQMvu/libvlccore.so
2025-08-07 11:31:40,629 - vlc_loader - DEBUG - 库文件不存在: /tmp/_MEInDQMvu/libvlc.so
2025-08-07 11:31:40,629 - vlc_loader - INFO - 成功预加载了 4 个VLC库文件
2025-08-07 11:31:40,642 - vlc_loader - INFO - VLC模块加载成功
2025-08-07 11:31:40,642 - vlc_loader - INFO - 尝试创建VLC实例，选项: ['--intf=dummy', '--quiet', '--no-video-title-show', '--no-audio', '--no-spu', '--no-osd', '--no-stats', '--no-media-library', '--no-playlist-enqueue', '--no-interact']
2025-08-07 11:31:40,643 - vlc_loader - ERROR - VLC功能测试失败：无法创建实例
VLC初始化失败: VLC功能测试失败
2025-08-07 11:31:40,644 - module_manager - DEBUG - 消息已广播: group_screen_monitor (stream_added)
2025-08-07 11:31:40,644 - communication_manager - DEBUG - 消息已广播: group_screen_monitor (stream_added) -> 1 个模块
2025-08-07 11:31:40,645 - module.group_screen_monitor - INFO - 布局已改变为: single
2025-08-07 11:31:40,646 - module.group_screen_monitor - INFO - 添加视频流: admin-PC
2025-08-07 11:31:40,646 - module.group_screen_monitor - INFO - 小组屏幕看板模块初始化成功
2025-08-07 11:31:40,646 - module.group_screen_monitor - INFO - 模块初始化成功: 小组屏幕看板
模块 group_screen_monitor 加载成功
模块 group_screen_monitor 加载成功
2025-08-07 11:31:40,646 - module_manager - INFO - 模块加载成功: group_screen_monitor
2025-08-07 11:31:41,532 - module.group_screen_monitor - INFO - 正在清理模块: 小组屏幕看板
2025-08-07 11:31:41,534 - module.group_screen_monitor - INFO - 小组屏幕看板模块资源清理完成
2025-08-07 11:31:41,534 - communication_manager - INFO - 模块已注销: group_screen_monitor
2025-08-07 11:31:41,534 - module.group_screen_monitor - INFO - 正在清理模块: 小组屏幕看板
2025-08-07 11:31:41,534 - module.group_screen_monitor - INFO - 小组屏幕看板模块资源清理完成
2025-08-07 11:31:41,534 - module_manager - INFO - 模块卸载成功: group_screen_monitor
2025-08-07 11:31:42,871 - communication_manager - INFO - 模块已注册: broadcast_player
2025-08-07 11:31:42,871 - broadcast_player - INFO - 正在初始化模块: 接收教师广播
2025-08-07 11:31:42,872 - vlc_player - INFO - VLC模块加载成功
2025-08-07 11:31:42,872 - vlc_loader - INFO - 尝试创建VLC实例，选项: ['--intf=dummy', '--quiet', '--no-video-title-show', '--no-audio', '--no-spu', '--no-osd', '--no-stats', '--no-media-library', '--no-playlist-enqueue', '--no-interact']
2025-08-07 11:31:42,872 - vlc_loader - ERROR - VLC功能测试失败：无法创建实例
2025-08-07 11:31:42,872 - vlc_player - ERROR - VLC初始化失败: VLC功能测试失败
2025-08-07 11:31:42,872 - broadcast_player - ERROR - 创建VLC播放器失败: VLC功能测试失败
2025-08-07 11:31:42,873 - broadcast_player - INFO - 成功获取UDP发现模块引用
2025-08-07 11:31:42,873 - broadcast_player - DEBUG - UDP发现模块中未找到在线教师设备
2025-08-07 11:31:42,873 - broadcast_player - INFO - 广播播放器模块初始化成功
2025-08-07 11:31:42,873 - broadcast_player - INFO - 模块初始化成功: 接收教师广播
模块 broadcast_player 加载成功
模块 broadcast_player 加载成功
2025-08-07 11:31:42,873 - module_manager - INFO - 模块加载成功: broadcast_player
2025-08-07 11:31:43,812 - broadcast_player - INFO - 正在清理模块: 接收教师广播
2025-08-07 11:31:43,812 - broadcast_player - INFO - 广播播放器模块已清理
2025-08-07 11:31:43,812 - communication_manager - INFO - 模块已注销: broadcast_player
2025-08-07 11:31:43,812 - module_manager - INFO - 模块卸载成功: broadcast_player
