#!/usr/bin/env python3
"""
智慧课堂教学工具安装脚本
自动安装应用程序和依赖项（包括MediaMTX）
"""
import os
import sys
import subprocess
import platform
import argparse
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def install_python_dependencies():
    """安装Python依赖"""
    print("正在安装Python依赖...")
    
    requirements_file = Path("core/requirements.txt")
    if not requirements_file.exists():
        print("警告: requirements.txt文件不存在")
        return False
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("Python依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装Python依赖失败: {e}")
        return False

def install_mediamtx():
    """安装MediaMTX"""
    print("正在安装MediaMTX...")
    
    try:
        # 导入MediaMTX安装器
        sys.path.insert(0, str(Path("core").absolute()))
        from core.mediamtx_installer import mediamtx_installer
        
        if mediamtx_installer.install_mediamtx():
            print("MediaMTX安装完成")
            return True
        else:
            print("MediaMTX安装失败")
            return False
    except Exception as e:
        print(f"安装MediaMTX时出错: {e}")
        return False

def setup_config():
    """设置配置文件"""
    print("正在设置配置文件...")
    
    try:
        sys.path.insert(0, str(Path("core").absolute()))
        from deployment_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 加载默认配置（会自动创建配置文件）
        app_config = config_manager.load_app_config()
        user_config = config_manager.load_user_config()
        
        print("配置文件设置完成")
        return True
    except Exception as e:
        print(f"设置配置文件失败: {e}")
        return False

def create_startup_script():
    """创建启动脚本"""
    print("正在创建启动脚本...")
    
    try:
        system = platform.system().lower()
        
        if system == "windows":
            # Windows批处理文件
            script_content = f"""@echo off
cd /d "{os.getcwd()}"
python core/run_app.py
pause
"""
            script_path = Path("start_app.bat")
        else:
            # Linux/macOS shell脚本
            script_content = f"""#!/bin/bash
cd "{os.getcwd()}"
python3 core/run_app.py
"""
            script_path = Path("start_app.sh")
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限（Linux/macOS）
        if system != "windows":
            os.chmod(script_path, 0o755)
        
        print(f"启动脚本已创建: {script_path}")
        return True
    except Exception as e:
        print(f"创建启动脚本失败: {e}")
        return False

def install_system_dependencies():
    """安装系统依赖"""
    system = platform.system().lower()
    
    if system == "linux":
        print("检查Linux系统依赖...")
        
        # 检查是否需要安装系统包
        packages_to_check = [
            ("xdotool", "虚拟键盘功能需要"),
            ("onboard", "屏幕键盘功能需要")
        ]
        
        missing_packages = []
        for package, description in packages_to_check:
            try:
                subprocess.run(["which", package], 
                             check=True, 
                             stdout=subprocess.DEVNULL, 
                             stderr=subprocess.DEVNULL)
            except subprocess.CalledProcessError:
                missing_packages.append((package, description))
        
        if missing_packages:
            print("以下系统包未安装，某些功能可能不可用:")
            for package, description in missing_packages:
                print(f"  - {package}: {description}")
            
            print("\n可以使用以下命令安装:")
            package_names = [pkg[0] for pkg in missing_packages]
            print(f"sudo apt-get install {' '.join(package_names)}")
    
    elif system == "darwin":  # macOS
        print("macOS系统依赖检查完成")
    
    elif system == "windows":
        print("Windows系统依赖检查完成")
    
    return True

def main():
    parser = argparse.ArgumentParser(description="智慧课堂教学工具安装程序")
    parser.add_argument("--skip-mediamtx", action="store_true", 
                       help="跳过MediaMTX安装")
    parser.add_argument("--skip-deps", action="store_true", 
                       help="跳过Python依赖安装")
    parser.add_argument("--config-only", action="store_true", 
                       help="仅设置配置文件")
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("智慧课堂教学工具安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        return 1
    
    # 检查系统依赖
    if not install_system_dependencies():
        print("系统依赖检查失败")
        return 1
    
    if args.config_only:
        # 仅设置配置
        if not setup_config():
            return 1
        print("\n配置设置完成!")
        return 0
    
    # 安装Python依赖
    if not args.skip_deps:
        if not install_python_dependencies():
            print("Python依赖安装失败")
            return 1
    
    # 安装MediaMTX
    if not args.skip_mediamtx:
        if not install_mediamtx():
            print("MediaMTX安装失败，但程序可以继续运行")
    
    # 设置配置文件
    if not setup_config():
        print("配置文件设置失败")
        return 1
    
    # 创建启动脚本
    if not create_startup_script():
        print("启动脚本创建失败")
        return 1
    
    print("\n" + "=" * 50)
    print("安装完成!")
    print("=" * 50)
    print("使用方法:")
    
    system = platform.system().lower()
    if system == "windows":
        print("  双击 start_app.bat 启动应用")
        print("  或运行: python core/run_app.py")
    else:
        print("  运行: ./start_app.sh")
        print("  或运行: python3 core/run_app.py")
    
    print("\n配置文件位置: core/config/")
    print("日志文件位置: core/logs/")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())