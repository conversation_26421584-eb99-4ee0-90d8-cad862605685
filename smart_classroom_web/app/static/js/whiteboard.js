document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const excalidrawContainer = document.getElementById('excalidraw-container');
    const saveBtn = document.getElementById('save-btn');
    const clearBtn = document.getElementById('clear-btn');
    const onlineUsersContainer = document.getElementById('online-users');
    
    // 获取用户和小组信息
    const groupId = document.getElementById('group-id').value;
    const userId = document.getElementById('user-id').value;
    const userName = document.getElementById('user-name').value;
    
    // 白板状态和操作历史
    let excalidrawAPI = null;
    let lastElements = [];

    // 事件防抖函数 (Debounce)
    const debounce = (func, delay) => {
        let timeoutId;
        return function(...args) {
            const context = this;
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => func.apply(context, args), delay);
        };
    };

    // 初始化Socket.IO连接
    const socket = io('/whiteboard', {
        query: {
            'group_id': groupId,
            'user_id': userId,
            'user_name': userName
        }
    });
    
    // 初始化Excalidraw
    const Excalidraw = window.ExcalidrawLib.Excalidraw;
    const excalidrawElement = React.createElement(Excalidraw, {
        ref: (api) => (excalidrawAPI = api),
        // 使用防抖机制，确保在用户停止操作后，才发送最终的、最准确的数据。
        // 这能从根本上解决数据不一致和“笔迹坍缩”的问题。
        onChange: debounce((elements, appState) => {
            const nextElements = JSON.parse(JSON.stringify(elements));
            const { added, updated, deleted } = getChangedElements(lastElements, nextElements);

            if (added.length === 0 && updated.length === 0 && deleted.length === 0) {
                return; // 没有变化则不发送
            }

            const operation = {
                userId: userId,
                elements: [...added, ...updated],
                deletedIds: deleted.map(el => el.id),
                timestamp: Date.now()
            };

            socket.emit('whiteboard_operation', {
                group_id: groupId,
                operation: operation
            });

            // 为下一次比较存储当前状态的深拷贝
            lastElements = nextElements;
        }, 100) // 防抖延迟：停止操作100毫秒后发送更新
    });

    ReactDOM.render(excalidrawElement, excalidrawContainer);

    // 计算增量变化的函数
    const getChangedElements = (prevElements, nextElements) => {
        const prevMap = new Map(prevElements.map(el => [el.id, el]));
        const nextMap = new Map(nextElements.map(el => [el.id, el]));

        const added = [];
        const updated = [];
        const deleted = [];

        // 查找新增和修改的元素
        for (const [id, element] of nextMap) {
            if (!prevMap.has(id)) {
                added.push(element);
            } else {
                const prevElement = prevMap.get(id);
                if (element.version > prevElement.version || 
                    (element.type === 'freedraw' && element.points.length !== prevElement.points.length) ||
                    (element.isCommitted && !prevElement.isCommitted)) {
                    updated.push(element);
                }
            }
        }

        // 查找删除的元素
        for (const [id, element] of prevMap) {
            if (!nextMap.has(id)) {
                deleted.push(element);
            }
        }

        return { added, updated, deleted };
    };
    
    // 更新在线用户列表
    const updateOnlineUsers = (users) => {
        onlineUsersContainer.innerHTML = '';
        
        users.forEach(user => {
            const userSpan = document.createElement('span');
            userSpan.textContent = user.name;
            userSpan.classList.add('user-badge');
            
            if (user.id === userId) {
                userSpan.classList.add('active');
                userSpan.textContent += ' (你)';
            }
            
            onlineUsersContainer.appendChild(userSpan);
        });
    };
    
    // Socket.IO事件处理
    socket.on('connect', () => {
        console.log('已连接到白板服务器');
        socket.emit('request_whiteboard_state', { group_id: groupId });
    });
    
    socket.on('disconnect', () => {
        console.log('与白板服务器断开连接');
    });
    
    socket.on('whiteboard_operation', (data) => {
        if (excalidrawAPI && data.operation.userId !== userId) {
            const { elements: updatedOrAddedElements, deletedIds } = data.operation;

            const currentElementsMap = new Map(excalidrawAPI.getSceneElements().map(el => [el.id, el]));

            if (deletedIds && deletedIds.length > 0) {
                for (const id of deletedIds) {
                    currentElementsMap.delete(id);
                }
            }

            if (updatedOrAddedElements && updatedOrAddedElements.length > 0) {
                for (const element of updatedOrAddedElements) {
                    currentElementsMap.set(element.id, element);
                }
            }

            const newElements = Array.from(currentElementsMap.values());

            excalidrawAPI.updateScene({
                elements: newElements
            });

            lastElements = newElements;
        }
    });
    
    socket.on('whiteboard_state', (data) => {
        if (excalidrawAPI && data.elements) {
            lastElements = data.elements;
            excalidrawAPI.updateScene({
                elements: data.elements
            });
        }
    });
    
    socket.on('online_users', (data) => {
        updateOnlineUsers(data.users);
    });
    
    // 按钮事件处理
    saveBtn.addEventListener('click', async () => {
        if (!excalidrawAPI) return;
        
        const { exportToBlob } = await window.ExcalidrawLib;
        
        try {
            const blob = await exportToBlob({
                elements: excalidrawAPI.getSceneElements(),
                appState: excalidrawAPI.getAppState(),
                mimeType: 'image/png',
                quality: 1
            });
            
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `whiteboard-${groupId}-${new Date().toISOString().slice(0, 10)}.png`;
            link.click();
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('保存白板失败:', error);
            alert('保存白板失败，请重试');
        }
    });
    
    clearBtn.addEventListener('click', () => {
        if (!excalidrawAPI) return;
        
        if (confirm('确定要清除白板内容吗？此操作不可撤销。')) {
            excalidrawAPI.updateScene({
                elements: []
            });
            lastElements = [];
            
            socket.emit('whiteboard_clear', {
                group_id: groupId,
                user_id: userId
            });
        }
    });
    
    // 页面卸载前断开连接
    window.addEventListener('beforeunload', () => {
        if (socket.connected) {
            socket.emit('leave_group', { 
                group_id: groupId, 
                user_id: userId 
            });
            socket.disconnect();
        }
    });
});
