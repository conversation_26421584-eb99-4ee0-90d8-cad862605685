from app import db
from datetime import datetime

class CourseSession(db.Model):
    """课程会话模型，用于管理课程的上课状态"""
    __tablename__ = 'course_sessions'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.<PERSON>ger, db.<PERSON>ey('courses.id'), nullable=False)
    session_name = db.Column(db.String(200))  # 本节课名称，如"第1课：Python基础"
    start_time = db.Column(db.DateTime, default=datetime.utcnow)  # 开始上课时间
    end_time = db.Column(db.DateTime, nullable=True)  # 结束上课时间
    status = db.Column(db.String(20), default='active')  # 会话状态：'active', 'ended'
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者（教师）ID
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    course = db.relationship('Course', backref=db.backref('sessions', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('course_sessions', lazy='dynamic'))
    
    def __repr__(self):
        return f'<CourseSession {self.id}: {self.session_name or "未命名"}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'course_id': self.course_id,
            'session_name': self.session_name,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'duration_minutes': self.get_duration_minutes()
        }
    
    def get_duration_minutes(self):
        """获取课程时长（分钟）"""
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            return int(duration.total_seconds() / 60)
        elif self.start_time and self.status == 'active':
            duration = datetime.utcnow() - self.start_time
            return int(duration.total_seconds() / 60)
        return 0
    
    def end_session(self):
        """结束课程会话"""
        self.end_time = datetime.utcnow()
        self.status = 'ended'
        db.session.commit()
    
    @classmethod
    def get_active_session(cls, course_id):
        """获取课程的当前活跃会话"""
        return cls.query.filter_by(
            course_id=course_id,
            status='active'
        ).first()
    
    @classmethod
    def start_new_session(cls, course_id, teacher_id, session_name=None):
        """开始新的课程会话"""
        # 先结束当前活跃的会话（如果有）
        active_session = cls.get_active_session(course_id)
        if active_session:
            active_session.end_session()
        
        # 创建新会话
        new_session = cls(
            course_id=course_id,
            session_name=session_name,
            created_by=teacher_id
        )
        db.session.add(new_session)
        db.session.commit()
        return new_session