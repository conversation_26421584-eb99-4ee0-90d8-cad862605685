from app import db
from datetime import datetime

class Resource(db.Model):
    """资源模型"""
    __tablename__ = 'resources'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    type = db.Column(db.String(20))  # 'document', 'image', 'video', 'audio', 'other'
    url = db.Column(db.String(200))
    size = db.Column(db.Integer)  # 文件大小（字节）
    format = db.Column(db.String(20))  # 文件格式
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    
    # 关系
    owner = db.relationship('User', backref=db.backref('resources', lazy='dynamic'))
    
    # 元数据（JSON格式）
    file_metadata = db.Column(db.JSON)
    
    def __repr__(self):
        return f'<Resource {self.name}>'

class Question(db.Model):
    """习题模型"""
    __tablename__ = 'questions'
    
    id = db.Column(db.Integer, primary_key=True)
    type = db.Column(db.String(20))  # 'single', 'multiple', 'truefalse', 'fillblank', 'subjective'
    content = db.Column(db.Text, nullable=False)
    options = db.Column(db.JSON)  # 选项（JSON格式）
    answer = db.Column(db.JSON)  # 答案（JSON格式）
    score = db.Column(db.Float, default=1.0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    creator = db.relationship('User', backref=db.backref('questions', lazy='dynamic'))
    exams = db.relationship('ExamQuestion', backref='question', lazy='dynamic')
    
    def __repr__(self):
        return f'<Question {self.id}>'

class Exam(db.Model):
    """试卷模型"""
    __tablename__ = 'exams'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    total_score = db.Column(db.Float)
    time_limit = db.Column(db.Integer)  # 时间限制（分钟）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    creator = db.relationship('User', backref=db.backref('exams', lazy='dynamic'))
    questions = db.relationship('ExamQuestion', backref='exam', lazy='dynamic')
    
    def __repr__(self):
        return f'<Exam {self.name}>'

class ExamQuestion(db.Model):
    """试卷习题关联模型"""
    __tablename__ = 'exam_questions'
    
    id = db.Column(db.Integer, primary_key=True)
    exam_id = db.Column(db.Integer, db.ForeignKey('exams.id'))
    question_id = db.Column(db.Integer, db.ForeignKey('questions.id'))
    order = db.Column(db.Integer)  # 题目顺序
    
    def __repr__(self):
        return f'<ExamQuestion {self.exam_id}:{self.question_id}>'

class CoursePlan(db.Model):
    """课程安排模型"""
    __tablename__ = 'course_plans'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    location = db.Column(db.String(100))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 外键
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'))
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    # 关系
    course = db.relationship('Course', backref=db.backref('plans', lazy='dynamic'))
    creator = db.relationship('User', backref=db.backref('course_plans', lazy='dynamic'))
    
    # 资源关联（如课件、习题、试卷等）
    resources = db.Column(db.JSON)  # 存储资源ID列表
    
    def __repr__(self):
        return f'<CoursePlan {self.title}>'