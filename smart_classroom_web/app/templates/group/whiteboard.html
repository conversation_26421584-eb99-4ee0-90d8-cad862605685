{% extends "base.html" %}

{% block title %}小组白板协作{% endblock %}

{% block styles %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@excalidraw/excalidraw@0.15.3/dist/excalidraw.min.css">
<style>
    .whiteboard-container {
        width: 100%;
        height: 80vh;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
    }
    .controls {
        margin-bottom: 15px;
        display: flex;
        justify-content: space-between;
    }
    .user-list {
        background: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 15px;
    }
    .user-list span {
        margin-right: 10px;
        padding: 3px 8px;
        background: #e0e0e0;
        border-radius: 10px;
        font-size: 0.9em;
    }
    .user-list .active {
        background: #4caf50;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>小组白板协作</h2>
    <p>小组: {{ group.name }}</p>
    
    <div class="user-list">
        <strong>在线成员:</strong>
        <div id="online-users"></div>
    </div>
    
    <div class="controls">
        <div>
            <button id="save-btn" class="btn btn-primary">保存白板</button>
            <button id="clear-btn" class="btn btn-danger">清除白板</button>
        </div>
        <div>
            {% if current_user.is_teacher() %}
            <a href="{{ url_for('group.manage', course_id=course.id) }}" class="btn btn-secondary">返回小组管理</a>
            {% endif %}
        </div>
    </div>
    
    <div class="whiteboard-container" id="excalidraw-container"></div>
    
    <input type="hidden" id="group-id" value="{{ group.id }}">
    <input type="hidden" id="user-id" value="{{ current_user.id }}">
    <input type="hidden" id="user-name" value="{{ current_user.username }}">
</div>
{% endblock %}

{% block scripts %}
<script src="https://unpkg.com/react@17/umd/react.production.min.js"></script>
<script src="https://unpkg.com/react-dom@17/umd/react-dom.production.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@excalidraw/excalidraw@0.15.3/dist/excalidraw.production.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/socket.io/client-dist/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/whiteboard.js') }}"></script>
{% endblock %}