{% extends 'base.html' %}

{% block title %}分组管理 - {{ course.name }}{% endblock %}

{% block styles %}
<style>
    .group-container {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #f9f9f9;
    }
    
    .group-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .group-title {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .group-actions {
        display: flex;
        gap: 10px;
    }
    
    .student-list {
        min-height: 100px;
        border: 1px dashed #ccc;
        border-radius: 5px;
        padding: 10px;
        background-color: #fff;
    }
    
    .student-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        margin: 5px 0;
        border: 1px solid #eee;
        border-radius: 4px;
        background-color: #f5f5f5;
        cursor: move;
    }
    
    .student-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .student-avatar {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #ddd;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        color: #666;
    }
    
    .student-name {
        font-weight: 500;
    }
    
    .ungrouped-container {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #f0f8ff;
    }
    
    .dragging {
        opacity: 0.5;
    }
    
    .drop-target {
        border: 2px dashed #007bff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>分组管理 - {{ course.name }}</h1>
        </div>
        <div class="col-auto">
            <button id="btn-add-group" class="btn btn-primary">
                <i class="fas fa-plus"></i> 添加新分组
            </button>
            <button id="btn-random-group" class="btn btn-success">
                <i class="fas fa-random"></i> 随机分组
            </button>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div id="groups-container">
                {% for group in groups %}
                <div class="group-container" data-group-id="{{ group.id }}">
                    <div class="group-header">
                        <div class="group-title">{{ group.name }}</div>
                        <div class="group-actions">
                            <a href="{{ url_for('group.whiteboard', group_id=group.id) }}" class="btn btn-sm btn-outline-success" title="白板协作">
                                <i class="fas fa-chalkboard"></i>
                            </a>
                            <button class="btn btn-sm btn-outline-primary btn-edit-group">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger btn-delete-group">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="student-list" data-group-id="{{ group.id }}">
                        {% for member in group.members %}
                        <div class="student-item" data-student-id="{{ member.student.id }}">
                            <div class="student-info">
                                <div class="student-avatar">
                                    {% if member.student.avatar %}
                                    <img src="{{ member.student.avatar }}" alt="{{ member.student.username }}" width="30" height="30">
                                    {% else %}
                                    {{ member.student.username[0] }}
                                    {% endif %}
                                </div>
                                <div class="student-name">{{ member.student.username }}</div>
                            </div>
                            <button class="btn btn-sm btn-outline-danger btn-remove-student">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="ungrouped-container">
                <div class="group-header">
                    <div class="group-title">未分组学生</div>
                </div>
                <div class="student-list" id="ungrouped-students">
                    {% for student in ungrouped_students %}
                    <div class="student-item" data-student-id="{{ student.id }}">
                        <div class="student-info">
                            <div class="student-avatar">
                                {% if student.avatar %}
                                <img src="{{ student.avatar }}" alt="{{ student.username }}" width="30" height="30">
                                {% else %}
                                {{ student.username[0] }}
                                {% endif %}
                            </div>
                            <div class="student-name">{{ student.username }}</div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑分组模态框 -->
<div class="modal fade" id="groupModal" tabindex="-1" aria-labelledby="groupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="groupModalLabel">添加新分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="groupForm">
                    <input type="hidden" id="group-id">
                    <div class="mb-3">
                        <label for="group-name" class="form-label">分组名称</label>
                        <input type="text" class="form-control" id="group-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="device-id" class="form-label">设备ID（可选）</label>
                        <input type="text" class="form-control" id="device-id">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-save-group">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 随机分组模态框 -->
<div class="modal fade" id="randomGroupModal" tabindex="-1" aria-labelledby="randomGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="randomGroupModalLabel">随机分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="randomGroupForm">
                    <div class="mb-3">
                        <label for="group-count" class="form-label">分组数量</label>
                        <input type="number" class="form-control" id="group-count" min="2" value="4" required>
                    </div>
                    <div class="mb-3">
                        <label for="algorithm" class="form-label">分组算法</label>
                        <select class="form-select" id="algorithm">
                            <option value="equal" selected>均等分配</option>
                            <option value="random">完全随机</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-execute-random">执行分组</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const courseId = {{ course.id }};
        let draggingElement = null;
        
        // 初始化拖拽功能
        initDragAndDrop();
        
        // 添加新分组按钮点击事件
        document.getElementById('btn-add-group').addEventListener('click', function() {
            // 重置表单
            document.getElementById('groupForm').reset();
            document.getElementById('group-id').value = '';
            document.getElementById('groupModalLabel').textContent = '添加新分组';
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('groupModal'));
            modal.show();
        });
        
        // 随机分组按钮点击事件
        document.getElementById('btn-random-group').addEventListener('click', function() {
            const totalStudents = document.querySelectorAll('.student-item').length;
            const groupCountInput = document.getElementById('group-count');

            if (totalStudents === 0) {
                alert('课程中没有学生，无法进行分组。');
                return;
            }

            // 动态设置分组数量输入框的最大值
            groupCountInput.max = totalStudents;
            groupCountInput.placeholder = `最多 ${totalStudents} 组`;

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('randomGroupModal'));
            modal.show();
        });
        
        // 执行随机分组
        document.getElementById('btn-execute-random').addEventListener('click', function() {
            const groupCount = parseInt(document.getElementById('group-count').value, 10);
            const algorithm = document.getElementById('algorithm').value;
            const totalStudents = document.querySelectorAll('.student-item').length;

            if (isNaN(groupCount) || groupCount < 1 || groupCount > totalStudents) {
                alert(`请输入有效的分组数量 (1 到 ${totalStudents} 之间)。`);
                return;
            }

            // 发送随机分组请求
            fetch(`/group/api/random/${courseId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    group_count: groupCount,
                    algorithm: algorithm
                })
            })
            .then(response => {
                if (!response.ok) {
                    // 尝试解析错误信息
                    return response.json().then(err => { throw new Error(err.error || '随机分组失败'); });
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('randomGroupModal')).hide();
                
                // 刷新页面显示新的分组
                alert('随机分组成功！');
                window.location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('随机分组失败: ' + error.message);
            });
        });
        
        // 保存分组按钮点击事件
        document.getElementById('btn-save-group').addEventListener('click', function() {
            const groupId = document.getElementById('group-id').value;
            const groupName = document.getElementById('group-name').value;
            const deviceId = document.getElementById('device-id').value;
            
            if (!groupName) {
                alert('请输入分组名称');
                return;
            }
            
            const groupData = {
                name: groupName,
                device_id: deviceId
            };
            
            let url, method;
            
            if (groupId) {
                // 更新现有分组
                url = `/group/api/groups/${groupId}`;
                method = 'PUT';
            } else {
                // 创建新分组
                url = `/group/api/groups/${courseId}`;
                method = 'POST';
            }
            
            fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(groupData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('保存分组失败');
                }
                return response.json();
            })
            .then(data => {
                // 关闭模态框
                bootstrap.Modal.getInstance(document.getElementById('groupModal')).hide();
                
                if (!groupId) {
                    // 如果是新建分组，添加到页面
                    addGroupToPage(data);
                } else {
                    // 如果是更新分组，更新页面上的分组名称
                    const groupContainer = document.querySelector(`.group-container[data-group-id="${groupId}"]`);
                    if (groupContainer) {
                        groupContainer.querySelector('.group-title').textContent = data.name;
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存分组失败: ' + error.message);
            });
        });
        
        // 编辑分组按钮点击事件委托
        document.addEventListener('click', function(event) {
            if (event.target.closest('.btn-edit-group')) {
                const groupContainer = event.target.closest('.group-container');
                const groupId = groupContainer.dataset.groupId;
                const groupName = groupContainer.querySelector('.group-title').textContent;
                
                // 设置表单值
                document.getElementById('group-id').value = groupId;
                document.getElementById('group-name').value = groupName;
                document.getElementById('device-id').value = ''; // 这里可以从数据中获取设备ID
                
                // 更新模态框标题
                document.getElementById('groupModalLabel').textContent = '编辑分组';
                
                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('groupModal'));
                modal.show();
            }
        });
        
        // 删除分组按钮点击事件委托
        document.addEventListener('click', function(event) {
            if (event.target.closest('.btn-delete-group')) {
                if (!confirm('确定要删除此分组吗？分组中的学生将被移至未分组列表。')) {
                    return;
                }
                
                const groupContainer = event.target.closest('.group-container');
                const groupId = groupContainer.dataset.groupId;
                
                fetch(`/group/api/groups/${groupId}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('删除分组失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 从页面移除分组
                    groupContainer.remove();
                    
                    // 刷新未分组学生列表
                    refreshUngroupedStudents();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('删除分组失败: ' + error.message);
                });
            }
        });
        
        // 移除学生按钮点击事件委托
        document.addEventListener('click', function(event) {
            const removeButton = event.target.closest('.btn-remove-student');
            if (removeButton) {
                const studentItem = removeButton.closest('.student-item');
                const studentId = studentItem.dataset.studentId;
                const studentList = studentItem.parentElement;
                const groupId = studentList.dataset.groupId;

                if (!groupId) return; // 安全检查

                fetch(`/group/api/groups/${groupId}/members/${studentId}`, {
                    method: 'DELETE'
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('移除学生失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 将学生移动到未分组列表
                    const studentName = studentItem.querySelector('.student-name').textContent;
                    const ungroupedList = document.getElementById('ungrouped-students');
                    const newStudentElement = createStudentElement({ id: studentId, username: studentName }, false);
                    ungroupedList.appendChild(newStudentElement);
                    studentItem.remove(); // 移除旧的元素
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('移除学生失败: ' + error.message);
                });
            }
        });

        // 初始化拖拽功能
        function initDragAndDrop() {
            // 为所有学生项添加拖拽事件
            document.querySelectorAll('.student-item').forEach(item => {
                makeDraggable(item);
            });
            
            // 为所有学生列表添加放置事件
            document.querySelectorAll('.student-list').forEach(list => {
                list.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('drop-target');
                });
                
                list.addEventListener('dragleave', function() {
                    this.classList.remove('drop-target');
                });
                
                list.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('drop-target');
                    
                    const studentId = e.dataTransfer.getData('text/plain');
                    const studentElement = document.querySelector(`.student-item[data-student-id="${studentId}"]`);
                    const sourceList = studentElement.parentElement;
                    const targetList = this;
                    
                    if (sourceList === targetList) return;

                    const targetGroupId = targetList.dataset.groupId;
                    const studentName = studentElement.querySelector('.student-name').textContent;

                    const handleDrop = (newGroupId) => {
                        const newElement = createStudentElement({ id: studentId, username: studentName }, !!newGroupId);
                        targetList.appendChild(newElement);
                        studentElement.remove();
                    };

                    if (targetGroupId) {
                        // 添加学生到新分组
                        fetch(`/group/api/groups/${targetGroupId}/members`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ student_id: studentId })
                        })
                        .then(res => res.ok ? res.json() : Promise.reject('添加学生到分组失败'))
                        .then(() => handleDrop(targetGroupId))
                        .catch(err => alert(err));
                    } else {
                        // 从原分组移除学生
                        const sourceGroupId = sourceList.dataset.groupId;
                        fetch(`/group/api/groups/${sourceGroupId}/members/${studentId}`, { method: 'DELETE' })
                        .then(res => res.ok ? res.json() : Promise.reject('从分组移除学生失败'))
                        .then(() => handleDrop(null))
                        .catch(err => alert(err));
                    }
                });
            });
        }

        function createStudentElement(student, inGroup) {
            const studentDiv = document.createElement('div');
            studentDiv.className = 'student-item';
            studentDiv.dataset.studentId = student.id;

            let removeButtonHtml = '';
            if (inGroup) {
                removeButtonHtml = `
                    <button class="btn btn-sm btn-outline-danger btn-remove-student">
                        <i class="fas fa-times"></i>
                    </button>
                `;
            }

            studentDiv.innerHTML = `
                <div class="student-info">
                    <div class="student-avatar">${student.username[0]}</div>
                    <div class="student-name">${student.username}</div>
                </div>
                ${removeButtonHtml}
            `;
            makeDraggable(studentDiv);
            return studentDiv;
        }

        function makeDraggable(item) {
            item.draggable = true;
            item.addEventListener('dragstart', function(e) {
                e.dataTransfer.setData('text/plain', this.dataset.studentId);
                this.classList.add('dragging');
            });
            item.addEventListener('dragend', function() {
                this.classList.remove('dragging');
            });
        }
        
        // 添加新分组到页面
        function addGroupToPage(group) {
            const groupsContainer = document.getElementById('groups-container');
            
            const groupHtml = `
                <div class="group-container" data-group-id="${group.id}">
                    <div class="group-header">
                        <div class="group-title">${group.name}</div>
                        <div class="group-actions">
                            <a href="/group/whiteboard/${group.id}" class="btn btn-sm btn-outline-success" title="白板协作">
                                <i class="fas fa-chalkboard"></i>
                            </a>
                            <button class="btn btn-sm btn-outline-primary btn-edit-group">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger btn-delete-group">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="student-list" data-group-id="${group.id}">
                    </div>
                </div>
            `;
            
            groupsContainer.insertAdjacentHTML('beforeend', groupHtml);
            
            // 为新添加的元素初始化拖拽功能
            initDragAndDrop();
        }
        
        // 添加学生到未分组列表
        function addStudentToUngrouped(studentId, studentName) {
            const ungroupedList = document.getElementById('ungrouped-students');
            
            const studentHtml = `
                <div class="student-item" data-student-id="${studentId}" draggable="true">
                    <div class="student-info">
                        <div class="student-avatar">${studentName[0]}</div>
                        <div class="student-name">${studentName}</div>
                    </div>
                </div>
            `;
            
            ungroupedList.insertAdjacentHTML('beforeend', studentHtml);
            
            // 为新添加的元素初始化拖拽功能
            const newStudent = ungroupedList.lastElementChild;
            
            newStudent.addEventListener('dragstart', function(e) {
                draggingElement = this;
                this.classList.add('dragging');
                e.dataTransfer.setData('text/plain', this.dataset.studentId);
            });
            
            newStudent.addEventListener('dragend', function() {
                this.classList.remove('dragging');
                draggingElement = null;
            });
        }
        
        // 刷新未分组学生列表
        function refreshUngroupedStudents() {
            fetch(`/group/api/groups/${courseId}`)
            .then(response => response.json())
            .then(data => {
                const ungroupedList = document.getElementById('ungrouped-students');
                ungroupedList.innerHTML = '';
                
                data.ungrouped_students.forEach(student => {
                    const studentHtml = `
                        <div class="student-item" data-student-id="${student.id}" draggable="true">
                            <div class="student-info">
                                <div class="student-avatar">${student.name[0]}</div>
                                <div class="student-name">${student.name}</div>
                            </div>
                        </div>
                    `;
                    
                    ungroupedList.insertAdjacentHTML('beforeend', studentHtml);
                });
                
                // 重新初始化拖拽功能
                initDragAndDrop();
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }
    });
</script>
{% endblock %}