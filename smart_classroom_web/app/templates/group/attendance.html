{% extends 'base.html' %}

{% block title %}考勤管理 - {{ course.name }}{% endblock %}

{% block styles %}
<style>
    .attendance-container {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        background-color: #f9f9f9;
    }
    
    .attendance-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .attendance-title {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .attendance-actions {
        display: flex;
        gap: 10px;
    }
    
    .attendance-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .attendance-table th,
    .attendance-table td {
        padding: 10px;
        border: 1px solid #ddd;
    }
    
    .attendance-table th {
        background-color: #f2f2f2;
        text-align: left;
    }
    
    .attendance-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }
    
    .attendance-table tr:hover {
        background-color: #f5f5f5;
    }
    
    .status-present {
        color: #28a745;
        font-weight: bold;
    }
    
    .status-absent {
        color: #dc3545;
        font-weight: bold;
    }
    
    .status-late {
        color: #ffc107;
        font-weight: bold;
    }
    
    .status-select {
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    .status-select option[value="present"] {
        color: #28a745;
    }
    
    .status-select option[value="absent"] {
        color: #dc3545;
    }
    
    .status-select option[value="late"] {
        color: #ffc107;
    }
    
    .note-input {
        width: 100%;
        padding: 5px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    
    .summary-container {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
    }
    
    .summary-item {
        flex: 1;
        padding: 15px;
        border-radius: 5px;
        text-align: center;
    }
    
    .summary-present {
        background-color: #d4edda;
        color: #155724;
    }
    
    .summary-absent {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .summary-late {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .summary-count {
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .summary-label {
        font-size: 0.9rem;
    }
    
    .date-picker {
        padding: 5px 10px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>考勤管理 - {{ course.name }}</h1>
            <p>管理课程学生的考勤记录</p>
        </div>
        <div class="col-auto">
            <a href="{{ url_for('group.manage', course_id=course.id) }}" class="btn btn-primary">
                <i class="fas fa-users"></i> 分组管理
            </a>
        </div>
    </div>
    
    <div class="attendance-container">
        <div class="attendance-header">
            <div class="attendance-title">考勤记录</div>
            <div class="attendance-actions">
                <input type="date" id="attendance-date" class="date-picker" value="{{ today }}">
                <button id="btn-refresh" class="btn btn-outline-primary">
                    <i class="fas fa-sync"></i> 刷新
                </button>
                <button id="btn-save" class="btn btn-success">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
        
        <div class="summary-container">
            <div class="summary-item summary-present">
                <div class="summary-count" id="present-count">0</div>
                <div class="summary-label">出勤</div>
            </div>
            <div class="summary-item summary-absent">
                <div class="summary-count" id="absent-count">0</div>
                <div class="summary-label">缺勤</div>
            </div>
            <div class="summary-item summary-late">
                <div class="summary-count" id="late-count">0</div>
                <div class="summary-label">迟到</div>
            </div>
        </div>
        
        <table class="attendance-table">
            <thead>
                <tr>
                    <th width="5%">#</th>
                    <th width="15%">学生</th>
                    <th width="15%">状态</th>
                    <th width="20%">签到时间</th>
                    <th width="45%">备注</th>
                </tr>
            </thead>
            <tbody id="attendance-list">
                <!-- 考勤记录将通过JavaScript动态添加 -->
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const courseId = {{ course.id }};
        let attendanceData = [];
        
        // 设置日期选择器默认值为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('attendance-date').value = today;
        
        // 初始化考勤记录
        loadAttendanceData();
        
        // 刷新按钮点击事件
        document.getElementById('btn-refresh').addEventListener('click', function() {
            loadAttendanceData();
        });
        
        // 保存按钮点击事件
        document.getElementById('btn-save').addEventListener('click', function() {
            saveAttendanceData();
        });
        
        // 日期选择器变更事件
        document.getElementById('attendance-date').addEventListener('change', function() {
            loadAttendanceData();
        });
        
        // 加载考勤数据
        function loadAttendanceData() {
            const date = document.getElementById('attendance-date').value;
            
            fetch(`/group/api/attendance/${courseId}?date=${date}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取考勤记录失败');
                    }
                    return response.json();
                })
                .then(data => {
                    attendanceData = data.attendance;
                    renderAttendanceTable();
                    updateSummary();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('获取考勤记录失败: ' + error.message);
                });
        }
        
        // 保存考勤数据
        function saveAttendanceData() {
            const date = document.getElementById('attendance-date').value;
            
            // 收集表格中的考勤数据
            const rows = document.querySelectorAll('#attendance-list tr');
            const updatedData = [];
            
            rows.forEach(row => {
                const studentId = row.dataset.studentId;
                const status = row.querySelector('.status-select').value;
                const note = row.querySelector('.note-input').value;
                
                updatedData.push({
                    student_id: parseInt(studentId),
                    status: status,
                    note: note
                });
            });
            
            fetch(`/group/api/attendance/${courseId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    date: date,
                    attendance: updatedData
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('保存考勤记录失败');
                }
                return response.json();
            })
            .then(data => {
                alert('考勤记录保存成功');
                loadAttendanceData();  // 重新加载数据
            })
            .catch(error => {
                console.error('Error:', error);
                alert('保存考勤记录失败: ' + error.message);
            });
        }
        
        // 渲染考勤表格
        function renderAttendanceTable() {
            const tableBody = document.getElementById('attendance-list');
            tableBody.innerHTML = '';
            
            attendanceData.forEach((record, index) => {
                const row = document.createElement('tr');
                row.dataset.studentId = record.student_id;
                
                row.innerHTML = `
                    <td>${index + 1}</td>
                    <td>${record.student_name}</td>
                    <td>
                        <select class="status-select" data-original="${record.status}">
                            <option value="present" ${record.status === 'present' ? 'selected' : ''}>出勤</option>
                            <option value="absent" ${record.status === 'absent' ? 'selected' : ''}>缺勤</option>
                            <option value="late" ${record.status === 'late' ? 'selected' : ''}>迟到</option>
                        </select>
                    </td>
                    <td>${record.check_in_time ? new Date(record.check_in_time).toLocaleString() : '未签到'}</td>
                    <td>
                        <input type="text" class="note-input" value="${record.note || ''}" placeholder="添加备注...">
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // 添加状态选择器变更事件
            document.querySelectorAll('.status-select').forEach(select => {
                select.addEventListener('change', function() {
                    updateSummary();
                });
            });
        }
        
        // 更新考勤统计
        function updateSummary() {
            let presentCount = 0;
            let absentCount = 0;
            let lateCount = 0;
            
            document.querySelectorAll('.status-select').forEach(select => {
                const status = select.value;
                if (status === 'present') {
                    presentCount++;
                } else if (status === 'absent') {
                    absentCount++;
                } else if (status === 'late') {
                    lateCount++;
                }
            });
            
            document.getElementById('present-count').textContent = presentCount;
            document.getElementById('absent-count').textContent = absentCount;
            document.getElementById('late-count').textContent = lateCount;
        }
    });
</script>
{% endblock %}