{% extends 'base.html' %}

{% block title %}查看题目会话{% endblock %}

{% block styles %}
<style>
    .question-card {
        margin-bottom: 20px;
    }
    .question-content {
        margin-bottom: 15px;
    }
    .options-list {
        list-style-type: none;
        padding-left: 0;
    }
    .option-item {
        padding: 8px 12px;
        margin-bottom: 5px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
    }
    .option-item:hover {
        background-color: #f8f9fa;
    }
    .option-item.selected {
        background-color: #e9ecef;
        border-color: #0d6efd;
    }
    .screenshot-image {
        max-width: 100%;
        margin-bottom: 15px;
        border: 1px solid #ddd;
    }
    .answer-textarea {
        width: 100%;
        min-height: 100px;
    }
    .answer-status {
        margin-top: 10px;
        font-style: italic;
    }
    .countdown {
        font-size: 1.2rem;
        font-weight: bold;
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>{{ session.title }}</h2>
        <div>
            {% if session.end_time %}
            <div class="countdown" id="countdown" data-end-time="{{ session.end_time.isoformat() }}">
                剩余时间: <span id="countdown-time"></span>
            </div>
            {% endif %}
            
            <a href="{{ url_for('interaction.question_sessions') }}" class="btn btn-outline-secondary">
                返回列表
            </a>
            
            {% if current_user.role == 'teacher' %}
            <a href="{{ url_for('interaction.question_session_results', session_id=session.id) }}" class="btn btn-outline-info">
                查看结果
            </a>
            
            {% if session.status == 'active' %}
            <button id="end-session-btn" class="btn btn-outline-danger" data-session-id="{{ session.id }}">
                结束答题
            </button>
            {% endif %}
            {% endif %}
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5>基本信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>课程:</strong> {{ session.course.name }}</p>
                    <p><strong>创建时间:</strong> {{ session.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="col-md-6">
                    <p>
                        <strong>状态:</strong>
                        {% if session.status == 'active' %}
                        <span class="badge bg-success">进行中</span>
                        {% else %}
                        <span class="badge bg-secondary">已结束</span>
                        {% endif %}
                    </p>
                    <p>
                        <strong>截止时间:</strong>
                        {% if session.end_time %}
                        {{ session.end_time.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        无截止时间
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <h3>题目列表</h3>
    
    {% if session_questions %}
    <h4 class="mt-4">从题库选择的题目</h4>
    {% for sq in session_questions %}
    <div class="card question-card" id="sq-{{ sq.id }}">
        <div class="card-header">
            <h5>
                {{ sq.question.type | replace('single', '单选题') | replace('multiple', '多选题') | replace('truefalse', '判断题') | replace('fillblank', '填空题') | replace('subjective', '主观题') }}
                <span class="badge bg-info float-end">{{ sq.question.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="question-content">
                {{ sq.question.content | safe }}
            </div>
            
            {% if sq.question.type == 'single' %}
            <ul class="options-list" data-question-id="{{ sq.id }}" data-question-type="session" data-answer-type="single">
                {% for option in sq.question.options %}
                <li class="option-item" data-option-index="{{ loop.index0 }}">
                    {{ loop.index }}. {{ option }}
                </li>
                {% endfor %}
            </ul>
            
            {% elif sq.question.type == 'multiple' %}
            <ul class="options-list" data-question-id="{{ sq.id }}" data-question-type="session" data-answer-type="multiple">
                {% for option in sq.question.options %}
                <li class="option-item" data-option-index="{{ loop.index0 }}">
                    {{ loop.index }}. {{ option }}
                </li>
                {% endfor %}
            </ul>
            
            {% elif sq.question.type == 'truefalse' %}
            <ul class="options-list" data-question-id="{{ sq.id }}" data-question-type="session" data-answer-type="truefalse">
                <li class="option-item" data-option-value="true">正确</li>
                <li class="option-item" data-option-value="false">错误</li>
            </ul>
            
            {% elif sq.question.type == 'fillblank' %}
            <div class="mb-3">
                <label class="form-label">答案</label>
                <input type="text" class="form-control fillblank-answer" data-question-id="{{ sq.id }}" data-question-type="session">
            </div>
            
            {% elif sq.question.type == 'subjective' %}
            <div class="mb-3">
                <label class="form-label">答案</label>
                <textarea class="form-control answer-textarea subjective-answer" data-question-id="{{ sq.id }}" data-question-type="session"></textarea>
            </div>
            {% endif %}
            
            {% if current_user.role == 'student' %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button class="btn btn-primary submit-answer" data-question-id="{{ sq.id }}" data-question-type="session">
                    提交答案
                </button>
            </div>
            
            <div class="answer-status" id="status-sq-{{ sq.id }}">
                {% if student_answers and 'sq_' ~ sq.id in student_answers %}
                <div class="alert alert-success">
                    已提交答案
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    {% endif %}
    
    {% if temporary_questions %}
    <h4 class="mt-4">临时创建的题目</h4>
    {% for tq in temporary_questions %}
    <div class="card question-card" id="tq-{{ tq.id }}">
        <div class="card-header">
            <h5>
                {{ tq.type | replace('single', '单选题') | replace('multiple', '多选题') | replace('truefalse', '判断题') | replace('fillblank', '填空题') | replace('subjective', '主观题') }}
                <span class="badge bg-info float-end">{{ tq.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <div class="question-content">
                {{ tq.content | safe }}
            </div>
            
            {% if tq.type == 'single' %}
            <ul class="options-list" data-question-id="{{ tq.id }}" data-question-type="temporary" data-answer-type="single">
                {% for option in tq.options %}
                <li class="option-item" data-option-index="{{ loop.index0 }}">
                    {{ loop.index }}. {{ option }}
                </li>
                {% endfor %}
            </ul>
            
            {% elif tq.type == 'multiple' %}
            <ul class="options-list" data-question-id="{{ tq.id }}" data-question-type="temporary" data-answer-type="multiple">
                {% for option in tq.options %}
                <li class="option-item" data-option-index="{{ loop.index0 }}">
                    {{ loop.index }}. {{ option }}
                </li>
                {% endfor %}
            </ul>
            
            {% elif tq.type == 'truefalse' %}
            <ul class="options-list" data-question-id="{{ tq.id }}" data-question-type="temporary" data-answer-type="truefalse">
                <li class="option-item" data-option-value="true">正确</li>
                <li class="option-item" data-option-value="false">错误</li>
            </ul>
            
            {% elif tq.type == 'fillblank' %}
            <div class="mb-3">
                <label class="form-label">答案</label>
                <input type="text" class="form-control fillblank-answer" data-question-id="{{ tq.id }}" data-question-type="temporary">
            </div>
            
            {% elif tq.type == 'subjective' %}
            <div class="mb-3">
                <label class="form-label">答案</label>
                <textarea class="form-control answer-textarea subjective-answer" data-question-id="{{ tq.id }}" data-question-type="temporary"></textarea>
            </div>
            {% endif %}
            
            {% if current_user.role == 'student' %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button class="btn btn-primary submit-answer" data-question-id="{{ tq.id }}" data-question-type="temporary">
                    提交答案
                </button>
            </div>
            
            <div class="answer-status" id="status-tq-{{ tq.id }}">
                {% if student_answers and 'tq_' ~ tq.id in student_answers %}
                <div class="alert alert-success">
                    已提交答案
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    {% endif %}
    
    {% if screenshot_questions %}
    <h4 class="mt-4">截屏题目</h4>
    {% for scq in screenshot_questions %}
    <div class="card question-card" id="scq-{{ scq.id }}">
        <div class="card-header">
            <h5>
                截屏题目
                <span class="badge bg-info float-end">{{ scq.score }}分</span>
            </h5>
        </div>
        <div class="card-body">
            <img src="{{ scq.image_url }}" alt="截屏题目" class="screenshot-image">
            
            <div class="mb-3">
                <h6>题目描述</h6>
                <p>{{ scq.description }}</p>
            </div>
            
            <div class="mb-3">
                <label class="form-label">答案</label>
                <textarea class="form-control answer-textarea screenshot-answer" data-question-id="{{ scq.id }}" data-question-type="screenshot"></textarea>
            </div>
            
            {% if current_user.role == 'student' %}
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button class="btn btn-primary submit-answer" data-question-id="{{ scq.id }}" data-question-type="screenshot">
                    提交答案
                </button>
            </div>
            
            <div class="answer-status" id="status-scq-{{ scq.id }}">
                {% if student_answers and 'scq_' ~ scq.id in student_answers %}
                <div class="alert alert-success">
                    已提交答案
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    {% endif %}
    
    {% if not session_questions and not temporary_questions and not screenshot_questions %}
    <div class="alert alert-info">
        此题目会话中没有题目。
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 倒计时功能
    function updateCountdown() {
        const countdownElement = document.getElementById('countdown');
        if (!countdownElement) return;
        
        const endTimeStr = countdownElement.dataset.endTime;
        if (!endTimeStr) return;
        
        const endTime = new Date(endTimeStr);
        const now = new Date();
        
        // 计算剩余时间（毫秒）
        let timeLeft = endTime - now;
        
        if (timeLeft <= 0) {
            // 时间已到
            document.getElementById('countdown-time').textContent = '已结束';
            
            // 禁用所有提交按钮
            document.querySelectorAll('.submit-answer').forEach(function(button) {
                button.disabled = true;
            });
            
            return;
        }
        
        // 转换为时分秒
        const hours = Math.floor(timeLeft / (1000 * 60 * 60));
        timeLeft -= hours * (1000 * 60 * 60);
        
        const minutes = Math.floor(timeLeft / (1000 * 60));
        timeLeft -= minutes * (1000 * 60);
        
        const seconds = Math.floor(timeLeft / 1000);
        
        // 格式化显示
        let timeString = '';
        if (hours > 0) {
            timeString += hours + '小时 ';
        }
        timeString += minutes + '分 ' + seconds + '秒';
        
        document.getElementById('countdown-time').textContent = timeString;
    }
    
    // 初始化倒计时
    updateCountdown();
    setInterval(updateCountdown, 1000);
    
    // 选项选择
    document.querySelectorAll('.option-item').forEach(function(item) {
        item.addEventListener('click', function() {
            const optionsList = this.parentElement;
            const answerType = optionsList.dataset.answerType;
            
            if (answerType === 'single') {
                // 单选题只能选一个
                optionsList.querySelectorAll('.option-item').forEach(function(option) {
                    option.classList.remove('selected');
                });
                this.classList.add('selected');
            } else if (answerType === 'multiple') {
                // 多选题可以选多个
                this.classList.toggle('selected');
            } else if (answerType === 'truefalse') {
                // 判断题只能选一个
                optionsList.querySelectorAll('.option-item').forEach(function(option) {
                    option.classList.remove('selected');
                });
                this.classList.add('selected');
            }
        });
    });
    
    // 提交答案
    document.querySelectorAll('.submit-answer').forEach(function(button) {
        button.addEventListener('click', function() {
            const questionId = this.dataset.questionId;
            const questionType = this.dataset.questionType;
            let answer;
            
            // 根据题目类型获取答案
            if (questionType === 'session' || questionType === 'temporary') {
                const optionsList = document.querySelector(`.options-list[data-question-id="${questionId}"][data-question-type="${questionType}"]`);
                
                if (optionsList) {
                    const answerType = optionsList.dataset.answerType;
                    
                    if (answerType === 'single') {
                        // 单选题
                        const selectedOption = optionsList.querySelector('.option-item.selected');
                        if (selectedOption) {
                            answer = parseInt(selectedOption.dataset.optionIndex);
                        } else {
                            alert('请选择一个选项');
                            return;
                        }
                    } else if (answerType === 'multiple') {
                        // 多选题
                        const selectedOptions = optionsList.querySelectorAll('.option-item.selected');
                        if (selectedOptions.length > 0) {
                            answer = Array.from(selectedOptions).map(option => parseInt(option.dataset.optionIndex));
                        } else {
                            alert('请至少选择一个选项');
                            return;
                        }
                    } else if (answerType === 'truefalse') {
                        // 判断题
                        const selectedOption = optionsList.querySelector('.option-item.selected');
                        if (selectedOption) {
                            answer = selectedOption.dataset.optionValue === 'true';
                        } else {
                            alert('请选择正确或错误');
                            return;
                        }
                    }
                } else {
                    // 填空题或主观题
                    const fillblankInput = document.querySelector(`.fillblank-answer[data-question-id="${questionId}"][data-question-type="${questionType}"]`);
                    if (fillblankInput) {
                        // 填空题
                        answer = fillblankInput.value.split(',').map(a => a.trim());
                        if (!answer || answer.length === 0 || (answer.length === 1 && answer[0] === '')) {
                            alert('请输入答案');
                            return;
                        }
                    } else {
                        // 主观题
                        const subjectiveTextarea = document.querySelector(`.subjective-answer[data-question-id="${questionId}"][data-question-type="${questionType}"]`);
                        if (subjectiveTextarea) {
                            answer = subjectiveTextarea.value;
                            if (!answer) {
                                alert('请输入答案');
                                return;
                            }
                        }
                    }
                }
            } else if (questionType === 'screenshot') {
                // 截屏题目
                const screenshotTextarea = document.querySelector(`.screenshot-answer[data-question-id="${questionId}"][data-question-type="${questionType}"]`);
                if (screenshotTextarea) {
                    answer = screenshotTextarea.value;
                    if (!answer) {
                        alert('请输入答案');
                        return;
                    }
                }
            }
            
            // 发送答案
            fetch(`/interaction/question-sessions/{{ session.id }}/submit-answer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    question_type: questionType,
                    question_id: questionId,
                    answer: answer
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新状态
                    const statusElement = document.getElementById(`status-${questionType}-${questionId}`);
                    if (statusElement) {
                        statusElement.innerHTML = '<div class="alert alert-success">已提交答案</div>';
                    }
                    
                    // 禁用提交按钮
                    button.disabled = true;
                    button.textContent = '已提交';
                } else {
                    alert('提交失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('提交失败，请重试');
            });
        });
    });
    
    // 结束题目会话
    const endSessionBtn = document.getElementById('end-session-btn');
    if (endSessionBtn) {
        endSessionBtn.addEventListener('click', function() {
            if (confirm('确定要结束此题目会话吗？')) {
                const sessionId = this.dataset.sessionId;
                
                fetch(`/interaction/question-sessions/${sessionId}/end`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('题目会话已结束');
                        location.reload();
                    } else {
                        alert('操作失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        });
    }
    
    // 填充已有答案（如果有）
    {% if current_user.role == 'student' and student_answers %}
    document.addEventListener('DOMContentLoaded', function() {
        {% for key, answer in student_answers.items() %}
        const answerKey = '{{ key }}';
        const answerData = {{ answer.answer | tojson }};
        const [type, id] = answerKey.split('_');
        
        if (type === 'sq' || type === 'tq') {
            // 从题库选择的题目或临时创建的题目
            const questionType = type === 'sq' ? 'session' : 'temporary';
            const optionsList = document.querySelector(`.options-list[data-question-id="${id}"][data-question-type="${questionType}"]`);
            
            if (optionsList) {
                const answerType = optionsList.dataset.answerType;
                
                if (answerType === 'single') {
                    // 单选题
                    const option = optionsList.querySelector(`.option-item[data-option-index="${answerData}"]`);
                    if (option) {
                        option.classList.add('selected');
                    }
                } else if (answerType === 'multiple') {
                    // 多选题
                    answerData.forEach(function(index) {
                        const option = optionsList.querySelector(`.option-item[data-option-index="${index}"]`);
                        if (option) {
                            option.classList.add('selected');
                        }
                    });
                } else if (answerType === 'truefalse') {
                    // 判断题
                    const option = optionsList.querySelector(`.option-item[data-option-value="${answerData}"]`);
                    if (option) {
                        option.classList.add('selected');
                    }
                }
            } else {
                // 填空题或主观题
                const fillblankInput = document.querySelector(`.fillblank-answer[data-question-id="${id}"][data-question-type="${questionType}"]`);
                if (fillblankInput) {
                    // 填空题
                    fillblankInput.value = answerData.join(', ');
                } else {
                    // 主观题
                    const subjectiveTextarea = document.querySelector(`.subjective-answer[data-question-id="${id}"][data-question-type="${questionType}"]`);
                    if (subjectiveTextarea) {
                        subjectiveTextarea.value = answerData;
                    }
                }
            }
        } else if (type === 'scq') {
            // 截屏题目
            const screenshotTextarea = document.querySelector(`.screenshot-answer[data-question-id="${id}"][data-question-type="screenshot"]`);
            if (screenshotTextarea) {
                screenshotTextarea.value = answerData;
            }
        }
        
        // 禁用已提交题目的提交按钮
        const submitButton = document.querySelector(`.submit-answer[data-question-id="${id}"][data-question-type="${type === 'sq' ? 'session' : (type === 'tq' ? 'temporary' : 'screenshot')}"]`);
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = '已提交';
        }
        {% endfor %}
    });
    {% endif %}
</script>
{% endblock %}