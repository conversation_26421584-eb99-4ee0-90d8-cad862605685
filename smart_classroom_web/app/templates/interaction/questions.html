{% extends 'base.html' %}

{% block title %}习题管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">习题管理</h1>
        <a href="{{ url_for('interaction.create_question') }}" class="btn-hand-drawn">创建习题</a>
    </div>

    <div class="card hand-drawn-border">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">习题列表</h5>
        </div>
        <div class="card-body">
            {% if questions %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>类型</th>
                                <th>内容</th>
                                <th>分值</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for question in questions %}
                            <tr>
                                <td>{{ question.id }}</td>
                                <td>
                                    {% if question.type == 'single' %}
                                        单选题
                                    {% elif question.type == 'multiple' %}
                                        多选题
                                    {% elif question.type == 'truefalse' %}
                                        判断题
                                    {% elif question.type == 'fillblank' %}
                                        填空题
                                    {% elif question.type == 'subjective' %}
                                        主观题
                                    {% endif %}
                                </td>
                                <td>{{ question.content | safe }}</td>
                                <td>{{ question.score }}</td>
                                <td>{{ question.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <a href="{{ url_for('interaction.edit_question', question_id=question.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                                    <a href="{{ url_for('interaction.delete_question', question_id=question.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个习题吗？')">删除</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    暂无习题，请点击"创建习题"按钮添加。
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}