{% extends 'base.html' %}

{% block title %}学生评分{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1 class="mb-4">学生评分</h1>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">评分表单</h5>
                </div>
                <div class="card-body">
                    <form id="scoreForm" method="POST" action="{{ url_for('interaction.student_score',course_id=current_course.id) }}">
                        <div class="mb-3">
                            <label for="student_id" class="form-label">选择学生</label>
                            <select class="form-select" id="student_id" name="student_id" required>
                                <option value="">-- 请选择学生 --</option>
                                {% for student in current_course_students %}
                                <option value="{{ student.id }}">{{ student.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="score" class="form-label">评分</label>
                            <div class="input-group">
                                <button type="button" class="btn btn-outline-secondary" id="decreaseScore">-</button>
                                <input type="number" class="form-control text-center" id="score" name="score" value="1" min="-10" max="10" step="1" required>
                                <button type="button" class="btn btn-outline-secondary" id="increaseScore">+</button>
                            </div>
                            <div class="form-text">可以输入正数（加分）或负数（减分），范围：-10 到 10</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">评分理由</label>
                            <textarea class="form-control" id="reason" name="reason" rows="3" placeholder="请输入评分理由（可选）"></textarea>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">提交评分</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近评分记录</h5>
                </div>
                <div class="card-body">
                    {% if recent_scores %}
                    <ul class="list-group">
                        {% for score in recent_scores %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ score.student.username }}</strong>
                                    <span class="badge {% if score.score > 0 %}bg-success{% else %}bg-danger{% endif %}">{{ score.score }}</span>
                                </div>
                                <small class="text-muted">{{ score.created_at[:16] }}</small>
                            </div>
                            {% if score.reason %}
                            <small class="text-muted">{{ score.reason }}</small>
                            {% endif %}
                        </li>
                        {% endfor %}
                    </ul>
                    {% else %}
                    <p class="text-muted">暂无评分记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 增加分数按钮
document.getElementById('increaseScore').addEventListener('click', function() {
    const scoreInput = document.getElementById('score');
    let score = parseInt(scoreInput.value) || 0;
    score = Math.min(score + 1, 10);
    scoreInput.value = score;
});

// 减少分数按钮
document.getElementById('decreaseScore').addEventListener('click', function() {
    const scoreInput = document.getElementById('score');
    let score = parseInt(scoreInput.value) || 0;
    score = Math.max(score - 1, -10);
    scoreInput.value = score;
});
</script>
{% endblock %}