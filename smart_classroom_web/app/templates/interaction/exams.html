{% extends 'base.html' %}

{% block title %}试卷管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">试卷管理</h1>
        <a href="{{ url_for('interaction.create_exam') }}" class="btn-hand-drawn">创建试卷</a>
    </div>

    <div class="card hand-drawn-border">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">试卷列表</h5>
        </div>
        <div class="card-body">
            {% if exams %}
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>名称</th>
                                <th>总分</th>
                                <th>时间限制</th>
                                <th>题目数量</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for exam in exams %}
                            <tr>
                                <td>{{ exam.id }}</td>
                                <td>{{ exam.name }}</td>
                                <td>{{ exam.total_score }}</td>
                                <td>{{ exam.time_limit }} 分钟</td>
                                <td>{{ exam.questions.count() }}</td>
                                <td>{{ exam.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <a href="{{ url_for('interaction.view_exam', exam_id=exam.id) }}" class="btn btn-sm btn-outline-primary">查看</a>
                                    <a href="{{ url_for('interaction.edit_exam', exam_id=exam.id) }}" class="btn btn-sm btn-outline-secondary">编辑</a>
                                    <a href="{{ url_for('interaction.delete_exam', exam_id=exam.id) }}" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个试卷吗？')">删除</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    暂无试卷，请点击"创建试卷"按钮添加。
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}