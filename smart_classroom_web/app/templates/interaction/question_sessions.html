{% extends 'base.html' %}

{% block title %}题目会话{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2>题目会话列表</h2>
        {% if current_course and current_user.is_teacher() %}
        <a href="{{ url_for('interaction.send_question', course_id=current_course.id) }}" class="btn btn-primary">
            发送新题目
        </a>
        {% endif %}
    </div>

    {% if sessions %}
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>标题</th>
                    <th>课程</th>
                    <th>创建时间</th>
                    <th>截止时间</th>
                    <th>状态</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for session in sessions %}
                <tr>
                    <td>{{ session.title }}</td>
                    <td>{{ session.course.name }}</td>
                    <td>{{ session.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>
                        {% if session.end_time %}
                        {{ session.end_time.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        无截止时间
                        {% endif %}
                    </td>
                    <td>
                        {% if session.status == 'active' %}
                        <span class="badge bg-success">进行中</span>
                        {% else %}
                        <span class="badge bg-secondary">已结束</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('interaction.view_question_session', session_id=session.id) }}"
                            class="btn btn-sm btn-outline-primary">
                            {% if current_user.is_teacher() %}
                            查看详情
                            {% else %}
                            参与答题
                            {% endif %}
                        </a>

                        {% if current_user.is_teacher() %}
                        <a href="{{ url_for('interaction.question_session_results', session_id=session.id) }}"
                            class="btn btn-sm btn-outline-info">
                            查看结果
                        </a>

                        {% if session.status == 'active' %}
                        <button class="btn btn-sm btn-outline-danger end-session" data-session-id="{{ session.id }}">
                            结束答题
                        </button>
                        {% endif %}
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        {% if current_user.is_teacher() %}
        您还没有创建任何题目会话，点击右上角的按钮发送新题目。
        {% else %}
        您还没有参与任何题目会话。
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 结束题目会话
    document.querySelectorAll('.end-session').forEach(function (button) {
        button.addEventListener('click', function () {
            if (confirm('确定要结束此题目会话吗？')) {
                const sessionId = this.dataset.sessionId;

                fetch(`/interaction/question-sessions/${sessionId}/end`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('题目会话已结束');
                            location.reload();
                        } else {
                            alert('操作失败: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('操作失败，请重试');
                    });
            }
        });
    });
</script>
{% endblock %}