{% extends 'base.html' %}

{% block title %}答题 - {{ session.title }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .question-container {
        margin-top: 20px;
        margin-bottom: 40px;
    }
    
    .question-card {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .question-card.new {
        animation: highlight 2s ease;
    }
    
    @keyframes highlight {
        0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5); }
        70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
        100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
    }
    
    .question-content {
        margin-bottom: 15px;
    }
    
    .options-list {
        list-style-type: none;
        padding-left: 0;
    }
    
    .option-item {
        padding: 10px 15px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .option-item:hover {
        background-color: #f8f9fa;
    }
    
    .option-item.selected {
        background-color: #e3f2fd;
        border-color: #90caf9;
    }
    
    .option-item input[type="checkbox"],
    .option-item input[type="radio"] {
        margin-right: 10px;
    }
    
    .screenshot-image {
        max-width: 100%;
        margin-bottom: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
    
    .answer-textarea {
        width: 100%;
        min-height: 120px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        resize: vertical;
    }
    
    .timer-container {
        position: fixed;
        top: 70px;
        right: 20px;
        background-color: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px 15px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }
    
    .timer {
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    .timer.warning {
        color: #ff9800;
    }
    
    .timer.danger {
        color: #f44336;
    }
    
    .submit-container {
        position: sticky;
        bottom: 0;
        background-color: #fff;
        border-top: 1px solid #ddd;
        padding: 15px 0;
        margin-top: 20px;
        z-index: 900;
    }
    
    .question-nav {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
    }
    
    .question-nav-item {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .question-nav-item:hover {
        background-color: #f8f9fa;
    }
    
    .question-nav-item.active {
        background-color: #007bff;
        color: #fff;
        border-color: #007bff;
    }
    
    .question-nav-item.answered {
        background-color: #28a745;
        color: #fff;
        border-color: #28a745;
    }
    
    .notification-badge {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #007bff;
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        display: none;
    }
    
    .fillblank-input {
        width: 100%;
        padding: 8px 12px;
        margin-bottom: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mt-4 mb-3">{{ session.title }}</h1>
            
            {% if session.end_time %}
            <div class="timer-container">
                <div class="timer" id="timer" data-end-time="{{ session.end_time.isoformat() }}">
                    剩余时间: 计算中...
                </div>
            </div>
            {% endif %}
            
            <div class="question-nav" id="questionNav">
                <!-- 题目导航将通过JavaScript动态添加 -->
            </div>
            
            <div class="question-container" id="questionContainer">
                <!-- 题目将通过JavaScript动态添加 -->
                <div class="text-center py-5" id="loadingIndicator">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在加载题目...</p>
                </div>
            </div>
            
            <div class="submit-container">
                <div class="d-flex justify-content-between">
                    <button id="prevBtn" class="btn btn-outline-secondary" disabled>上一题</button>
                    <button id="submitBtn" class="btn btn-primary">提交答案</button>
                    <button id="nextBtn" class="btn btn-outline-secondary">下一题</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="notification-badge" id="newQuestionNotification">
    <i class="fas fa-bell mr-2"></i> 收到新的题目
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 全局变量
        let currentQuestionIndex = 0;
        let questions = [];
        let answers = {};
        
        // DOM元素
        const questionContainer = document.getElementById('questionContainer');
        const questionNav = document.getElementById('questionNav');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        const submitBtn = document.getElementById('submitBtn');
        const newQuestionNotification = document.getElementById('newQuestionNotification');
        const timer = document.getElementById('timer');
        
        // 初始化WebSocket连接
        const socket = io();
        
        // 加入课程房间
        socket.on('connect', function() {
            socket.emit('join', {
                room: 'course_{{ session.course_id }}',
                user_id: '{{ current_user.id }}',
                user_type: 'student'
            });
            
            // 加载题目
            loadQuestions();
        });
        
        // 监听新题目事件
        socket.on('new_question', function(data) {
            // 显示通知
            showNotification();
            
            // 添加新题目
            questions.push(data);
            
            // 更新题目导航
            updateQuestionNav();
            
            // 如果当前没有显示题目，显示第一题
            if (questions.length === 1) {
                showQuestion(0);
            }
        });
        
        // 加载题目
        function loadQuestions() {
            fetch(`/interaction/api/session-questions/{{ session.id }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        questions = data.questions;
                        loadingIndicator.style.display = 'none';
                        
                        // 加载已保存的答案
                        loadSavedAnswers();
                        
                        // 更新题目导航
                        updateQuestionNav();
                        
                        // 显示第一题
                        if (questions.length > 0) {
                            showQuestion(0);
                        } else {
                            questionContainer.innerHTML = `
                                <div class="alert alert-info">
                                    <h4 class="alert-heading">暂无题目</h4>
                                    <p>教师尚未发布题目，请等待。</p>
                                </div>
                            `;
                        }
                    } else {
                        questionContainer.innerHTML = `
                            <div class="alert alert-danger">
                                <h4 class="alert-heading">加载失败</h4>
                                <p>${data.message}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('加载题目失败:', error);
                    questionContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <h4 class="alert-heading">加载失败</h4>
                            <p>无法连接到服务器，请刷新页面重试。</p>
                        </div>
                    `;
                });
        }
        
        // 加载已保存的答案
        function loadSavedAnswers() {
            fetch(`/interaction/api/student-answers/{{ session.id }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        answers = data.answers;
                        
                        // 更新题目导航，标记已答题目
                        updateQuestionNav();
                    }
                })
                .catch(error => {
                    console.error('加载已保存答案失败:', error);
                });
        }
        
        // 更新题目导航
        function updateQuestionNav() {
            questionNav.innerHTML = '';
            
            questions.forEach((question, index) => {
                const navItem = document.createElement('div');
                navItem.className = `question-nav-item ${index === currentQuestionIndex ? 'active' : ''} ${hasAnswer(question) ? 'answered' : ''}`;
                navItem.textContent = index + 1;
                navItem.addEventListener('click', () => {
                    showQuestion(index);
                });
                
                questionNav.appendChild(navItem);
            });
            
            // 更新导航按钮状态
            updateNavButtons();
        }
        
        // 检查是否已回答
        function hasAnswer(question) {
            const questionKey = getQuestionKey(question);
            return answers.hasOwnProperty(questionKey);
        }
        
        // 获取题目的唯一键
        function getQuestionKey(question) {
            if (question.type === 'session_question') {
                return `sq_${question.id}`;
            } else if (question.type === 'temporary_question') {
                return `tq_${question.id}`;
            } else if (question.type === 'screenshot_question') {
                return `scq_${question.id}`;
            }
            return null;
        }
        
        // 显示题目
        function showQuestion(index) {
            if (index < 0 || index >= questions.length) {
                return;
            }
            
            currentQuestionIndex = index;
            const question = questions[index];
            
            // 更新题目导航
            const navItems = questionNav.querySelectorAll('.question-nav-item');
            navItems.forEach((item, i) => {
                item.classList.toggle('active', i === index);
            });
            
            // 更新导航按钮状态
            updateNavButtons();
            
            // 构建题目HTML
            let questionHtml = '';
            
            if (question.type === 'session_question') {
                const q = question.question;
                questionHtml = `
                    <div class="card question-card" id="question-${index}">
                        <div class="card-header">
                            <h5>
                                ${getQuestionTypeText(q.type)}
                                <span class="badge bg-info float-end">${q.score}分</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="question-content">
                                ${q.content}
                            </div>
                            
                            ${renderQuestionOptions(q, index)}
                        </div>
                    </div>
                `;
            } else if (question.type === 'temporary_question') {
                const q = question;
                questionHtml = `
                    <div class="card question-card" id="question-${index}">
                        <div class="card-header">
                            <h5>
                                ${getQuestionTypeText(q.question_type)}
                                <span class="badge bg-info float-end">${q.score}分</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="question-content">
                                ${q.content}
                            </div>
                            
                            ${renderQuestionOptions(q, index, true)}
                        </div>
                    </div>
                `;
            } else if (question.type === 'screenshot_question') {
                const q = question;
                questionHtml = `
                    <div class="card question-card" id="question-${index}">
                        <div class="card-header">
                            <h5>
                                截屏题目
                                <span class="badge bg-info float-end">${q.score}分</span>
                            </h5>
                        </div>
                        <div class="card-body">
                            <img src="${q.image_url}" alt="截屏题目" class="screenshot-image">
                            
                            <div class="mb-3">
                                <h6>题目描述</h6>
                                <p>${q.description}</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="screenshot-answer-${index}">您的答案:</label>
                                <textarea id="screenshot-answer-${index}" class="answer-textarea" placeholder="请输入您的答案...">${getAnswerValue(q) || ''}</textarea>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            questionContainer.innerHTML = questionHtml;
            
            // 为选项添加事件监听器
            if (question.type === 'session_question' || question.type === 'temporary_question') {
                const q = question.type === 'session_question' ? question.question : question;
                const questionType = question.type === 'session_question' ? q.type : q.question_type;
                
                if (questionType === 'single') {
                    const options = document.querySelectorAll(`#question-${index} .option-item`);
                    options.forEach((option, optionIndex) => {
                        option.addEventListener('click', () => {
                            options.forEach(opt => opt.classList.remove('selected'));
                            option.classList.add('selected');
                            
                            // 更新单选按钮
                            const radio = option.querySelector('input[type="radio"]');
                            if (radio) {
                                radio.checked = true;
                            }
                            
                            // 保存答案
                            saveAnswer(question, optionIndex);
                        });
                    });
                    
                    // 恢复已保存的答案
                    const savedAnswer = getAnswerValue(question);
                    if (savedAnswer !== null) {
                        const radio = document.querySelector(`#question-${index} input[name="option-${index}"][value="${savedAnswer}"]`);
                        if (radio) {
                            radio.checked = true;
                            radio.closest('.option-item').classList.add('selected');
                        }
                    }
                } else if (questionType === 'multiple') {
                    const options = document.querySelectorAll(`#question-${index} .option-item`);
                    options.forEach((option, optionIndex) => {
                        option.addEventListener('click', () => {
                            option.classList.toggle('selected');
                            
                            // 更新复选框
                            const checkbox = option.querySelector('input[type="checkbox"]');
                            if (checkbox) {
                                checkbox.checked = !checkbox.checked;
                            }
                            
                            // 保存答案
                            const selectedOptions = [];
                            document.querySelectorAll(`#question-${index} input[type="checkbox"]:checked`).forEach(cb => {
                                selectedOptions.push(parseInt(cb.value));
                            });
                            saveAnswer(question, selectedOptions);
                        });
                    });
                    
                    // 恢复已保存的答案
                    const savedAnswer = getAnswerValue(question);
                    if (savedAnswer !== null && Array.isArray(savedAnswer)) {
                        savedAnswer.forEach(optionIndex => {
                            const checkbox = document.querySelector(`#question-${index} input[name="option-${index}-${optionIndex}"]`);
                            if (checkbox) {
                                checkbox.checked = true;
                                checkbox.closest('.option-item').classList.add('selected');
                            }
                        });
                    }
                } else if (questionType === 'truefalse') {
                    const options = document.querySelectorAll(`#question-${index} .option-item`);
                    options.forEach((option, optionIndex) => {
                        option.addEventListener('click', () => {
                            options.forEach(opt => opt.classList.remove('selected'));
                            option.classList.add('selected');
                            
                            // 更新单选按钮
                            const radio = option.querySelector('input[type="radio"]');
                            if (radio) {
                                radio.checked = true;
                            }
                            
                            // 保存答案
                            saveAnswer(question, optionIndex === 0);
                        });
                    });
                    
                    // 恢复已保存的答案
                    const savedAnswer = getAnswerValue(question);
                    if (savedAnswer !== null) {
                        const radio = document.querySelector(`#question-${index} input[name="truefalse-${index}"][value="${savedAnswer ? 'true' : 'false'}"]`);
                        if (radio) {
                            radio.checked = true;
                            radio.closest('.option-item').classList.add('selected');
                        }
                    }
                } else if (questionType === 'fillblank') {
                    // 为填空题添加事件监听器
                    const inputs = document.querySelectorAll(`#question-${index} .fillblank-input`);
                    inputs.forEach((input, inputIndex) => {
                        input.addEventListener('input', () => {
                            const values = [];
                            document.querySelectorAll(`#question-${index} .fillblank-input`).forEach(inp => {
                                values.push(inp.value.trim());
                            });
                            saveAnswer(question, values);
                        });
                    });
                    
                    // 恢复已保存的答案
                    const savedAnswer = getAnswerValue(question);
                    if (savedAnswer !== null && Array.isArray(savedAnswer)) {
                        savedAnswer.forEach((value, i) => {
                            if (i < inputs.length) {
                                inputs[i].value = value;
                            }
                        });
                    }
                } else if (questionType === 'subjective') {
                    // 为主观题添加事件监听器
                    const textarea = document.querySelector(`#question-${index} .answer-textarea`);
                    if (textarea) {
                        textarea.addEventListener('input', () => {
                            saveAnswer(question, textarea.value);
                        });
                        
                        // 恢复已保存的答案
                        const savedAnswer = getAnswerValue(question);
                        if (savedAnswer !== null) {
                            textarea.value = savedAnswer;
                        }
                    }
                }
            } else if (question.type === 'screenshot_question') {
                // 为截屏题添加事件监听器
                const textarea = document.querySelector(`#screenshot-answer-${index}`);
                if (textarea) {
                    textarea.addEventListener('input', () => {
                        saveAnswer(question, textarea.value);
                    });
                }
            }
        }
        
        // 获取题目类型文本
        function getQuestionTypeText(type) {
            switch (type) {
                case 'single': return '单选题';
                case 'multiple': return '多选题';
                case 'truefalse': return '判断题';
                case 'fillblank': return '填空题';
                case 'subjective': return '主观题';
                default: return '未知题型';
            }
        }
        
        // 渲染题目选项
        function renderQuestionOptions(question, index, isTemporary = false) {
            const questionType = isTemporary ? question.question_type : question.type;
            const options = isTemporary ? question.options : question.options;
            
            if (questionType === 'single') {
                return `
                    <div class="options-container">
                        <h6>选项</h6>
                        <ul class="options-list">
                            ${options.map((option, i) => `
                                <li class="option-item">
                                    <input type="radio" id="option-${index}-${i}" name="option-${index}" value="${i}">
                                    <label for="option-${index}-${i}">${option}</label>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            } else if (questionType === 'multiple') {
                return `
                    <div class="options-container">
                        <h6>选项（可多选）</h6>
                        <ul class="options-list">
                            ${options.map((option, i) => `
                                <li class="option-item">
                                    <input type="checkbox" id="option-${index}-${i}" name="option-${index}-${i}" value="${i}">
                                    <label for="option-${index}-${i}">${option}</label>
                                </li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            } else if (questionType === 'truefalse') {
                return `
                    <div class="options-container">
                        <h6>选项</h6>
                        <ul class="options-list">
                            <li class="option-item">
                                <input type="radio" id="truefalse-${index}-true" name="truefalse-${index}" value="true">
                                <label for="truefalse-${index}-true">正确</label>
                            </li>
                            <li class="option-item">
                                <input type="radio" id="truefalse-${index}-false" name="truefalse-${index}" value="false">
                                <label for="truefalse-${index}-false">错误</label>
                            </li>
                        </ul>
                    </div>
                `;
            } else if (questionType === 'fillblank') {
                // 假设需要填写的空格数量为1，实际应根据题目内容确定
                const blankCount = isTemporary ? question.blank_count || 1 : 1;
                
                return `
                    <div class="fillblank-container">
                        <h6>填空</h6>
                        ${Array(blankCount).fill(0).map((_, i) => `
                            <div class="form-group mb-3">
                                <label for="fillblank-${index}-${i}">空格 ${i + 1}:</label>
                                <input type="text" id="fillblank-${index}-${i}" class="fillblank-input" placeholder="请输入答案...">
                            </div>
                        `).join('')}
                    </div>
                `;
            } else if (questionType === 'subjective') {
                return `
                    <div class="form-group">
                        <label for="subjective-answer-${index}">您的答案:</label>
                        <textarea id="subjective-answer-${index}" class="answer-textarea" placeholder="请输入您的答案..."></textarea>
                    </div>
                `;
            }
            
            return '';
        }
        
        // 保存答案
        function saveAnswer(question, value) {
            const questionKey = getQuestionKey(question);
            if (questionKey) {
                answers[questionKey] = value;
                
                // 更新题目导航
                updateQuestionNav();
                
                // 发送答案到服务器
                sendAnswer(question, value);
            }
        }
        
        // 获取已保存的答案
        function getAnswerValue(question) {
            const questionKey = getQuestionKey(question);
            return questionKey && answers.hasOwnProperty(questionKey) ? answers[questionKey] : null;
        }
        
        // 发送答案到服务器
        function sendAnswer(question, value) {
            const data = {
                session_id: {{ session.id }},
                question_type: question.type,
                question_id: question.id,
                answer: value
            };
            
            fetch('/interaction/api/submit-answer', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (!data.success) {
                    console.error('保存答案失败:', data.message);
                }
            })
            .catch(error => {
                console.error('保存答案失败:', error);
            });
        }
        
        // 更新导航按钮状态
        function updateNavButtons() {
            prevBtn.disabled = currentQuestionIndex <= 0;
            nextBtn.disabled = currentQuestionIndex >= questions.length - 1;
        }
        
        // 显示通知
        function showNotification() {
            newQuestionNotification.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(function() {
                newQuestionNotification.style.display = 'none';
            }, 5000);
            
            // 点击通知后隐藏
            newQuestionNotification.addEventListener('click', function() {
                newQuestionNotification.style.display = 'none';
            });
        }
        
        // 更新倒计时
        function updateTimer() {
            if (!timer) return;
            
            const endTimeStr = timer.getAttribute('data-end-time');
            if (!endTimeStr) return;
            
            const endTime = new Date(endTimeStr);
            const now = new Date();
            const diff = Math.max(0, Math.floor((endTime - now) / 1000));
            
            if (diff <= 0) {
                timer.textContent = '时间已结束';
                timer.classList.add('danger');
                
                // 自动提交答案
                submitAnswers();
                return;
            }
            
            const minutes = Math.floor(diff / 60);
            const seconds = diff % 60;
            timer.textContent = `剩余时间: ${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
            
            // 添加警告样式
            if (diff < 60) {
                timer.classList.add('danger');
            } else if (diff < 300) {
                timer.classList.add('warning');
            }
            
            // 继续更新
            setTimeout(updateTimer, 1000);
        }
        
        // 提交所有答案
        function submitAnswers() {
            fetch('/interaction/api/submit-all-answers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_id: {{ session.id }}
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('答案已成功提交！');
                    // 可以选择重定向到结果页面
                    // window.location.href = data.redirect_url;
                } else {
                    alert('提交答案失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('提交答案失败:', error);
                alert('提交答案失败，请重试。');
            });
        }
        
        // 事件监听器
        prevBtn.addEventListener('click', () => {
            if (currentQuestionIndex > 0) {
                showQuestion(currentQuestionIndex - 1);
            }
        });
        
        nextBtn.addEventListener('click', () => {
            if (currentQuestionIndex < questions.length - 1) {
                showQuestion(currentQuestionIndex + 1);
            }
        });
        
        submitBtn.addEventListener('click', submitAnswers);
        
        // 如果有倒计时，启动倒计时
        if (timer) {
            updateTimer();
        }
    });
</script>
{% endblock %}