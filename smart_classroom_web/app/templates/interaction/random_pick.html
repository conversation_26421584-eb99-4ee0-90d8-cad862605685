{% extends 'base.html' %}

{% block title %}随机点名{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>随机点名</h2>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">点名设置</h5>
                </div>
                <div class="card-body">
                    <form id="randomPickForm" method="POST">
                        
                        <div class="mb-3">
                            <label class="form-label">点名类型</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="pick_type" id="pick_type_student" value="student" checked>
                                <label class="form-check-label" for="pick_type_student">
                                    随机选择学生
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="pick_type" id="pick_type_group" value="group">
                                <label class="form-check-label" for="pick_type_group">
                                    随机选择小组
                                </label>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_picked" name="exclude_picked">
                            <label class="form-check-label" for="exclude_picked">排除已点过的学生/小组</label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">开始点名</button>
                    </form>
                </div>
            </div>
            
            {% if recent_records %}
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">最近点名记录</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        {% for record in recent_records %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ record.course.name }}</strong>: 
                                    {% if record.pick_type == 'student' %}
                                    学生 {{ record.target.name }}
                                    {% else %}
                                    小组 {{ record.target.name }}
                                    {% endif %}
                                </div>
                                <small class="text-muted">{{ record.picked_at.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
            {% endif %}
        </div>
        
        <div class="col-md-6">
            {% if picked %}
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">点名结果</h5>
                </div>
                <div class="card-body text-center">
                    <h3>{{ course.name }}</h3>
                    
                    {% if pick_type == 'student' %}
                    <div class="my-4">
                        <div class="display-1 mb-3">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <h2 class="mb-3">{{ picked_student.name }}</h2>
                        <p class="text-muted">学生ID: {{ picked_student.id }}</p>
                        {% if picked_student.email %}
                        <p class="text-muted">邮箱: {{ picked_student.email }}</p>
                        {% endif %}
                    </div>
                    
                    <div class="mt-4">
                        <button type="button" class="btn btn-outline-success me-2" data-bs-toggle="modal" data-bs-target="#scoreModal">
                            <i class="fas fa-star"></i> 评分
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="pickAgainBtn">
                            <i class="fas fa-redo"></i> 再次点名
                        </button>
                    </div>
                    
                    {% elif pick_type == 'group' %}
                    <div class="my-4">
                        <div class="display-1 mb-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <h2 class="mb-3">{{ picked_group.name }}</h2>
                        <p class="text-muted">小组ID: {{ picked_group.id }}</p>
                        
                        <div class="mt-3">
                            <h5>小组成员:</h5>
                            <ul class="list-group">
                                {% for member in group_members %}
                                <li class="list-group-item">{{ member.name }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="button" class="btn btn-outline-success me-2" data-bs-toggle="modal" data-bs-target="#scoreModal">
                            <i class="fas fa-star"></i> 评分
                        </button>
                        <button type="button" class="btn btn-outline-primary" id="pickAgainBtn">
                            <i class="fas fa-redo"></i> 再次点名
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 评分模态框 -->
            <div class="modal fade" id="scoreModal" tabindex="-1" aria-labelledby="scoreModalLabel" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="scoreModalLabel">
                                {% if pick_type == 'student' %}
                                为 {{ picked_student.name }} 评分
                                {% else %}
                                为 {{ picked_group.name }} 评分
                                {% endif %}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <form id="scoreForm" action="{{ url_for('interaction.add_pick_score') }}" method="POST">
                                <input type="hidden" name="record_id" value="{{ record_id }}">
                                <input type="hidden" name="course_id" value="{{ course.id }}">
                                <input type="hidden" name="pick_type" value="{{ pick_type }}">
                                <input type="hidden" name="target_id" value="{% if pick_type == 'student' %}{{ picked_student.id }}{% else %}{{ picked_group.id }}{% endif %}">
                                
                                <div class="mb-3">
                                    <label for="score" class="form-label">分数</label>
                                    <input type="number" class="form-control" id="score" name="score" min="-10" max="10" step="0.5" value="0">
                                    <div class="form-text">可以设置正分或负分，范围 -10 到 10</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="note" class="form-label">备注</label>
                                    <textarea class="form-control" id="note" name="note" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" id="submitScoreBtn">提交评分</button>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="display-1 text-muted mb-4">
                        <i class="fas fa-random"></i>
                    </div>
                    <h3 class="text-muted">请选择课程并开始点名</h3>
                    <p class="text-muted">点击左侧"开始点名"按钮随机选择学生或小组</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 再次点名按钮
        const pickAgainBtn = document.getElementById('pickAgainBtn');
        if (pickAgainBtn) {
            pickAgainBtn.addEventListener('click', function() {
                document.getElementById('randomPickForm').submit();
            });
        }
        
        // 提交评分按钮
        const submitScoreBtn = document.getElementById('submitScoreBtn');
        if (submitScoreBtn) {
            submitScoreBtn.addEventListener('click', function() {
                document.getElementById('scoreForm').submit();
            });
        }
    });
</script>
{% endblock %}