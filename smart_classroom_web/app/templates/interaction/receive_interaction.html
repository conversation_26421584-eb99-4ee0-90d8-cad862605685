{% extends 'base.html' %}

{% block title %}互动接收 - {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .interaction-container {
        margin-top: 20px;
        margin-bottom: 40px;
    }
    
    .interaction-card {
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    
    .interaction-card.new {
        animation: highlight 2s ease;
    }
    
    @keyframes highlight {
        0% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.5); }
        70% { box-shadow: 0 0 0 10px rgba(0, 123, 255, 0); }
        100% { box-shadow: 0 0 0 0 rgba(0, 123, 255, 0); }
    }
    
    .interaction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .interaction-time {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .interaction-content {
        margin-top: 15px;
    }
    
    .interaction-image {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin-top: 10px;
    }
    
    .file-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    
    .file-icon {
        font-size: 24px;
        margin-right: 15px;
    }
    
    .file-info {
        flex-grow: 1;
    }
    
    .file-name {
        font-weight: 500;
        margin-bottom: 0;
    }
    
    .file-size {
        font-size: 0.85rem;
        color: #6c757d;
    }
    
    .file-actions {
        display: flex;
        gap: 10px;
    }
    
    .no-interactions {
        text-align: center;
        padding: 40px 0;
    }
    
    .no-interactions i {
        font-size: 48px;
        color: #dee2e6;
        margin-bottom: 20px;
    }
    
    .notification-badge {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #007bff;
        color: white;
        padding: 15px 20px;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <h1 class="mt-4 mb-3">互动接收 - {{ course.name }}</h1>
            
            <div class="interaction-container">
                <div id="interactionsList">
                    {% if interactions %}
                        {% for interaction in interactions %}
                        <div class="card interaction-card" id="interaction-{{ interaction.id }}">
                            <div class="card-header interaction-header">
                                <h5 class="mb-0">{{ interaction.title }}</h5>
                                <span class="interaction-time">{{ interaction.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</span>
                            </div>
                            <div class="card-body">
                                {% if interaction.description %}
                                <p>{{ interaction.description }}</p>
                                {% endif %}
                                
                                <div class="interaction-content">
                                    {% if interaction.type == 'text' %}
                                        <div class="content-text">{{ interaction.content }}</div>
                                    
                                    {% elif interaction.type == 'image' %}
                                        <img src="{{ interaction.content }}" class="interaction-image" alt="互动图片">
                                    
                                    {% elif interaction.type == 'file' %}
                                        <div class="file-item">
                                            <div class="file-icon">
                                                <i class="fas fa-file"></i>
                                            </div>
                                            <div class="file-info">
                                                <p class="file-name">{{ interaction.file_name }}</p>
                                                <p class="file-size">{{ interaction.file_size }}</p>
                                            </div>
                                            <div class="file-actions">
                                                <a href="{{ interaction.content }}" class="btn btn-sm btn-primary" download>
                                                    <i class="fas fa-download"></i> 下载
                                                </a>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="no-interactions">
                            <i class="fas fa-comment-slash"></i>
                            <h3>暂无互动内容</h3>
                            <p class="text-muted">当教师发送互动内容时，将会显示在这里</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="notification-badge" id="newInteractionNotification">
    <i class="fas fa-bell mr-2"></i> 收到新的互动内容
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 连接WebSocket
        const socket = io();
        
        // 加入课程房间
        socket.on('connect', function() {
            socket.emit('join', {
                room: 'course_{{ course.id }}',
                user_id: '{{ current_user.id }}',
                user_type: 'student'
            });
        });
        
        // 监听互动事件
        socket.on('new_interaction', function(data) {
            // 显示通知
            showNotification();
            
            // 添加新互动到列表
            addNewInteraction(data);
        });
        
        // 显示通知
        function showNotification() {
            const notification = document.getElementById('newInteractionNotification');
            notification.style.display = 'block';
            
            // 5秒后自动隐藏
            setTimeout(function() {
                notification.style.display = 'none';
            }, 5000);
            
            // 点击通知后隐藏
            notification.addEventListener('click', function() {
                notification.style.display = 'none';
            });
        }
        
        // 添加新互动到列表
        function addNewInteraction(interaction) {
            const interactionsList = document.getElementById('interactionsList');
            
            // 如果当前显示"暂无互动内容"，则清空
            const noInteractions = interactionsList.querySelector('.no-interactions');
            if (noInteractions) {
                interactionsList.innerHTML = '';
            }
            
            // 创建新互动卡片
            const card = document.createElement('div');
            card.className = 'card interaction-card new';
            card.id = `interaction-${interaction.id}`;
            
            // 格式化时间
            const createdAt = new Date(interaction.created_at);
            const formattedTime = createdAt.toLocaleString('zh-CN');
            
            // 构建卡片内容
            let contentHtml = '';
            if (interaction.type === 'text') {
                contentHtml = `<div class="content-text">${interaction.content}</div>`;
            } else if (interaction.type === 'image') {
                contentHtml = `<img src="${interaction.content}" class="interaction-image" alt="互动图片">`;
            } else if (interaction.type === 'file') {
                contentHtml = `
                    <div class="file-item">
                        <div class="file-icon">
                            <i class="fas fa-file"></i>
                        </div>
                        <div class="file-info">
                            <p class="file-name">${interaction.file_name}</p>
                            <p class="file-size">${interaction.file_size}</p>
                        </div>
                        <div class="file-actions">
                            <a href="${interaction.content}" class="btn btn-sm btn-primary" download>
                                <i class="fas fa-download"></i> 下载
                            </a>
                        </div>
                    </div>
                `;
            }
            
            card.innerHTML = `
                <div class="card-header interaction-header">
                    <h5 class="mb-0">${interaction.title}</h5>
                    <span class="interaction-time">${formattedTime}</span>
                </div>
                <div class="card-body">
                    ${interaction.description ? `<p>${interaction.description}</p>` : ''}
                    <div class="interaction-content">
                        ${contentHtml}
                    </div>
                </div>
            `;
            
            // 添加到列表顶部
            interactionsList.insertBefore(card, interactionsList.firstChild);
            
            // 滚动到顶部
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
            
            // 2秒后移除高亮效果
            setTimeout(function() {
                card.classList.remove('new');
            }, 2000);
        }
    });
</script>
{% endblock %}