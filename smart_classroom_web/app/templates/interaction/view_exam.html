{% extends 'base.html' %}

{% block title %}查看试卷{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .question-item {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .question-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }
    .question-type {
        background-color: #f8f9fa;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 0.9rem;
    }
    .question-score {
        font-weight: bold;
    }
    .question-content {
        margin-bottom: 15px;
    }
    .option-item {
        margin-bottom: 5px;
    }
    .correct-answer {
        color: #28a745;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">{{ exam.name }}</h1>
        <div>
            <a href="{{ url_for('interaction.edit_exam', exam_id=exam.id) }}" class="btn-hand-drawn me-2">编辑试卷</a>
            <a href="{{ url_for('interaction.exams') }}" class="btn-hand-drawn">返回列表</a>
        </div>
    </div>

    <div class="card hand-drawn-border mb-4">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">试卷信息</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <p><strong>试卷名称:</strong> {{ exam.name }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>总分:</strong> {{ exam.total_score }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>时间限制:</strong> {{ exam.time_limit }} 分钟</p>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <p><strong>题目数量:</strong> {{ exam_questions|length }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>创建时间:</strong> {{ exam.created_at.strftime('%Y-%m-%d %H:%M') }}</p>
                </div>
                <div class="col-md-4">
                    <p><strong>创建者:</strong> {{ exam.creator.username }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="card hand-drawn-border">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">试卷内容</h5>
        </div>
        <div class="card-body">
            {% if exam_questions %}
                {% for eq in exam_questions %}
                    <div class="question-item">
                        <div class="question-header">
                            <div>
                                <span class="fw-bold">{{ loop.index }}. </span>
                                <span class="question-type">
                                    {% if eq.question.type == 'single' %}
                                        单选题
                                    {% elif eq.question.type == 'multiple' %}
                                        多选题
                                    {% elif eq.question.type == 'truefalse' %}
                                        判断题
                                    {% elif eq.question.type == 'fillblank' %}
                                        填空题
                                    {% elif eq.question.type == 'subjective' %}
                                        主观题
                                    {% endif %}
                                </span>
                            </div>
                            <span class="question-score">{{ eq.question.score }} 分</span>
                        </div>
                        
                        <div class="question-content">
                            {{ render_question_content(eq.question.content)|safe }}
                        </div>
                        
                        {% if eq.question.type in ['single', 'multiple'] and eq.question.options %}
                            <div class="options-container">
                                {% for option in eq.question.options %}
                                    <div class="option-item">
                                        <span class="option-label">{{ chr(65 + loop.index0) }}. </span>
                                        <span class="option-text {% if eq.question.type == 'single' and eq.question.answer == loop.index0 or eq.question.type == 'multiple' and loop.index0 in eq.question.answer %}correct-answer{% endif %}">
                                            {{ option }}
                                            {% if eq.question.type == 'single' and eq.question.answer == loop.index0 or eq.question.type == 'multiple' and loop.index0 in eq.question.answer %}
                                                <i class="fas fa-check"></i>
                                            {% endif %}
                                        </span>
                                    </div>
                                {% endfor %}
                            </div>
                        {% elif eq.question.type == 'truefalse' %}
                            <div class="answer-container">
                                <p><strong>答案:</strong> 
                                    {% if eq.question.answer %}
                                        <span class="correct-answer">正确 <i class="fas fa-check"></i></span>
                                    {% else %}
                                        <span class="correct-answer">错误 <i class="fas fa-times"></i></span>
                                    {% endif %}
                                </p>
                            </div>
                        {% elif eq.question.type == 'fillblank' and eq.question.answer %}
                            <div class="answer-container">
                                <p><strong>答案:</strong></p>
                                <ul>
                                    {% for answer in eq.question.answer %}
                                        <li class="correct-answer">{{ answer }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% elif eq.question.type == 'subjective' and eq.question.answer %}
                            <div class="answer-container">
                                <p><strong>参考答案:</strong></p>
                                <div class="correct-answer">
                                    {{ render_question_content(eq.question.answer)|safe }}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-info">
                    此试卷暂无题目
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 可以添加一些交互功能，如打印试卷等
</script>
{% endblock %}