{% extends 'base.html' %}

{% block title %}创建习题{% endblock %}

{% block styles %}
{{ super() }}
<link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
<style>
    .ql-editor {
        min-height: 200px;
    }
    .option-container {
        margin-bottom: 10px;
    }
    .option-row {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    .option-input {
        flex-grow: 1;
        margin-right: 10px;
    }
    .correct-answer {
        margin-right: 10px;
    }
    #question-content-container {
        margin-bottom: 20px;
    }
    .hidden {
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="hand-drawn">创建习题</h1>
        <a href="{{ url_for('interaction.questions') }}" class="btn-hand-drawn">返回习题列表</a>
    </div>

    <div class="card hand-drawn-border">
        <div class="card-header hand-drawn">
            <h5 class="mb-0">习题信息</h5>
        </div>
        <div class="card-body">
            <form id="question-form" method="POST">
                <div class="mb-3">
                    <label for="question-type" class="form-label">题目类型</label>
                    <select class="form-select hand-drawn-border" id="question-type" name="type" required>
                        <option value="">请选择题型</option>
                        <option value="single">单选题</option>
                        <option value="multiple">多选题</option>
                        <option value="truefalse">判断题</option>
                        <option value="fillblank">填空题</option>
                        <option value="subjective">主观题</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="question-content" class="form-label">题目内容</label>
                    <div id="question-content-container">
                        <div id="question-editor"></div>
                        <input type="hidden" id="question-content" name="content">
                    </div>
                </div>

                <!-- 选项区域 - 根据题型动态显示 -->
                <div id="options-container" class="mb-3 hidden">
                    <label class="form-label">选项</label>
                    <div id="options-list">
                        <!-- 选项将通过JS动态添加 -->
                    </div>
                    <button type="button" id="add-option" class="btn btn-outline-secondary btn-sm mt-2">
                        <i class="fas fa-plus"></i> 添加选项
                    </button>
                </div>

                <!-- 判断题选项 -->
                <div id="truefalse-container" class="mb-3 hidden">
                    <label class="form-label">正确答案</label>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="truefalse-answer" id="answer-true" value="true">
                        <label class="form-check-label" for="answer-true">
                            正确
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="truefalse-answer" id="answer-false" value="false">
                        <label class="form-check-label" for="answer-false">
                            错误
                        </label>
                    </div>
                </div>

                <!-- 填空题答案 -->
                <div id="fillblank-container" class="mb-3 hidden">
                    <label class="form-label">答案</label>
                    <div id="fillblank-answers">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control hand-drawn-border fillblank-answer" placeholder="请输入答案">
                            <button type="button" class="btn btn-outline-danger remove-answer">删除</button>
                        </div>
                    </div>
                    <button type="button" id="add-fillblank-answer" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-plus"></i> 添加答案
                    </button>
                    <small class="form-text text-muted">可添加多个可接受的答案</small>
                </div>

                <!-- 主观题参考答案 -->
                <div id="subjective-container" class="mb-3 hidden">
                    <label for="subjective-answer" class="form-label">参考答案</label>
                    <div id="subjective-editor"></div>
                    <input type="hidden" id="subjective-answer" name="subjective-answer">
                </div>

                <div class="mb-3">
                    <label for="question-score" class="form-label">分值</label>
                    <input type="number" class="form-control hand-drawn-border" id="question-score" name="score" min="0" step="0.5" value="1" required>
                </div>

                <div class="text-end">
                    <button type="submit" class="btn-hand-drawn">保存习题</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
<script>
    // 初始化富文本编辑器
    const questionEditor = new Quill('#question-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'script': 'sub'}, { 'script': 'super' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                ['link', 'image', 'formula'],
                ['clean']
            ]
        },
        placeholder: '请输入题目内容...'
    });

    const subjectiveEditor = new Quill('#subjective-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                ['link', 'image', 'formula'],
                ['clean']
            ]
        },
        placeholder: '请输入参考答案...'
    });

    // 题型切换逻辑
    document.getElementById('question-type').addEventListener('change', function() {
        const questionType = this.value;
        
        // 隐藏所有选项容器
        document.getElementById('options-container').classList.add('hidden');
        document.getElementById('truefalse-container').classList.add('hidden');
        document.getElementById('fillblank-container').classList.add('hidden');
        document.getElementById('subjective-container').classList.add('hidden');
        
        // 根据题型显示对应的选项容器
        switch(questionType) {
            case 'single':
            case 'multiple':
                document.getElementById('options-container').classList.remove('hidden');
                // 清空并初始化选项
                const optionsList = document.getElementById('options-list');
                optionsList.innerHTML = '';
                addOption();
                addOption();
                break;
            case 'truefalse':
                document.getElementById('truefalse-container').classList.remove('hidden');
                break;
            case 'fillblank':
                document.getElementById('fillblank-container').classList.remove('hidden');
                break;
            case 'subjective':
                document.getElementById('subjective-container').classList.remove('hidden');
                break;
        }
    });

    // 添加选项
    document.getElementById('add-option').addEventListener('click', addOption);

    function addOption() {
        const optionsList = document.getElementById('options-list');
        const questionType = document.getElementById('question-type').value;
        const optionIndex = optionsList.children.length;
        const optionLetter = String.fromCharCode(65 + optionIndex); // A, B, C, ...
        
        const optionRow = document.createElement('div');
        optionRow.className = 'option-row';
        
        const optionLabel = document.createElement('span');
        optionLabel.className = 'option-label me-2';
        optionLabel.textContent = optionLetter + '.';
        
        const optionInput = document.createElement('input');
        optionInput.type = 'text';
        optionInput.className = 'form-control hand-drawn-border option-input';
        optionInput.name = `option-${optionIndex}`;
        optionInput.placeholder = `选项 ${optionLetter}`;
        optionInput.required = true;
        
        const correctInput = document.createElement('input');
        correctInput.type = questionType === 'single' ? 'radio' : 'checkbox';
        correctInput.className = 'form-check-input correct-answer';
        correctInput.name = questionType === 'single' ? 'correct-option' : `correct-option-${optionIndex}`;
        correctInput.value = optionIndex;
        
        const correctLabel = document.createElement('label');
        correctLabel.className = 'form-check-label me-2';
        correctLabel.textContent = '正确答案';
        
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-outline-danger btn-sm';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.addEventListener('click', function() {
            optionsList.removeChild(optionRow);
            // 重新排序选项标签
            updateOptionLabels();
        });
        
        optionRow.appendChild(optionLabel);
        optionRow.appendChild(optionInput);
        optionRow.appendChild(correctInput);
        optionRow.appendChild(correctLabel);
        optionRow.appendChild(removeBtn);
        
        optionsList.appendChild(optionRow);
    }

    // 更新选项标签
    function updateOptionLabels() {
        const optionRows = document.querySelectorAll('#options-list .option-row');
        optionRows.forEach((row, index) => {
            const optionLetter = String.fromCharCode(65 + index);
            row.querySelector('.option-label').textContent = optionLetter + '.';
            row.querySelector('.option-input').name = `option-${index}`;
            
            const correctInput = row.querySelector('.correct-answer');
            if (document.getElementById('question-type').value === 'single') {
                correctInput.name = 'correct-option';
            } else {
                correctInput.name = `correct-option-${index}`;
            }
            correctInput.value = index;
        });
    }

    // 添加填空题答案
    document.getElementById('add-fillblank-answer').addEventListener('click', function() {
        const container = document.getElementById('fillblank-answers');
        const answerGroup = document.createElement('div');
        answerGroup.className = 'input-group mb-2';
        
        const answerInput = document.createElement('input');
        answerInput.type = 'text';
        answerInput.className = 'form-control hand-drawn-border fillblank-answer';
        answerInput.placeholder = '请输入答案';
        
        const removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'btn btn-outline-danger remove-answer';
        removeBtn.textContent = '删除';
        removeBtn.addEventListener('click', function() {
            container.removeChild(answerGroup);
        });
        
        answerGroup.appendChild(answerInput);
        answerGroup.appendChild(removeBtn);
        container.appendChild(answerGroup);
    });

    // 表单提交处理
    document.getElementById('question-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取富文本编辑器内容
        document.getElementById('question-content').value = JSON.stringify(questionEditor.getContents());
        
        // 根据题型收集答案
        const questionType = document.getElementById('question-type').value;
        let answer;
        
        switch(questionType) {
            case 'single':
                const selectedOption = document.querySelector('input[name="correct-option"]:checked');
                answer = selectedOption ? parseInt(selectedOption.value) : null;
                break;
            case 'multiple':
                answer = [];
                document.querySelectorAll('input[type="checkbox"].correct-answer:checked').forEach(checkbox => {
                    answer.push(parseInt(checkbox.value));
                });
                break;
            case 'truefalse':
                const truefalseAnswer = document.querySelector('input[name="truefalse-answer"]:checked');
                answer = truefalseAnswer ? truefalseAnswer.value === 'true' : null;
                break;
            case 'fillblank':
                answer = [];
                document.querySelectorAll('.fillblank-answer').forEach(input => {
                    if (input.value.trim()) {
                        answer.push(input.value.trim());
                    }
                });
                break;
            case 'subjective':
                document.getElementById('subjective-answer').value = JSON.stringify(subjectiveEditor.getContents());
                answer = document.getElementById('subjective-answer').value;
                break;
        }
        
        // 收集选项（对于单选和多选题）
        let options = [];
        if (questionType === 'single' || questionType === 'multiple') {
            document.querySelectorAll('.option-input').forEach(input => {
                options.push(input.value);
            });
        }
        
        // 创建表单数据
        const formData = new FormData(this);
        formData.append('answer', JSON.stringify(answer));
        if (options.length > 0) {
            formData.append('options', JSON.stringify(options));
        }
        
        // 发送请求
        fetch(this.action, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.redirect;
            } else {
                alert('保存失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('发生错误，请稍后重试');
        });
    });
</script>
{% endblock %}