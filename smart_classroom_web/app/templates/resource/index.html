{% extends 'base.html' %}

{% block title %}资源管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>资源管理</h1>
    
    <div class="row mb-4">
        <div class="col-md-6">
            <a href="{{ url_for('resource.upload') }}" class="btn btn-primary">
                <i class="fas fa-upload"></i> 上传资源
            </a>
            <a href="{{ url_for('resource.search') }}" class="btn btn-outline-secondary">
                <i class="fas fa-search"></i> 高级搜索
            </a>
        </div>
        <div class="col-md-6">
            <form action="{{ url_for('resource.search') }}" method="GET" class="d-flex">
                <input type="text" class="form-control me-2" name="q" placeholder="搜索资源...">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-search"></i>
                </button>
            </form>
        </div>
    </div>
    
    {% if course %}
    <div class="alert alert-info">
        <i class="fas fa-book"></i> 显示课程 <strong>{{ course.name }}</strong> 的资源
        <a href="{{ url_for('resource.index') }}" class="float-end">显示全部资源</a>
    </div>
    {% endif %}
    
    {% if resources %}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="btn-group" role="group">
                <a href="{{ url_for('resource.index') }}" class="btn btn-outline-secondary {% if not request.args.get('view') %}active{% endif %}">
                    <i class="fas fa-list"></i> 列表视图
                </a>
                <a href="{{ url_for('resource.index', view='grid') }}" class="btn btn-outline-secondary {% if request.args.get('view') == 'grid' %}active{% endif %}">
                    <i class="fas fa-th"></i> 网格视图
                </a>
            </div>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary" id="selectAllBtn">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary" id="deselectAllBtn" disabled>
                    <i class="fas fa-square"></i> 取消全选
                </button>
                <button type="button" class="btn btn-outline-danger" id="batchDeleteBtn" disabled>
                    <i class="fas fa-trash"></i> 批量删除
                </button>
            </div>
        </div>
    </div>
    
    {% if request.args.get('view') == 'grid' %}
    <!-- 网格视图 -->
    <div class="row row-cols-1 row-cols-md-3 g-4">
        {% for resource in resources %}
        <div class="col">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        {% if resource.type == 'document' %}
                            <i class="fas fa-file-alt"></i> 文档
                        {% elif resource.type == 'image' %}
                            <i class="fas fa-image"></i> 图片
                        {% elif resource.type == 'video' %}
                            <i class="fas fa-video"></i> 视频
                        {% elif resource.type == 'audio' %}
                            <i class="fas fa-music"></i> 音频
                        {% elif resource.type == 'archive' %}
                            <i class="fas fa-file-archive"></i> 压缩文件
                        {% elif resource.type == 'code' %}
                            <i class="fas fa-file-code"></i> 代码
                        {% else %}
                            <i class="fas fa-file"></i> 其他
                        {% endif %}
                    </div>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input resource-checkbox" value="{{ resource.id }}" data-name="{{ resource.name }}">
                    </div>
                </div>
                {% if resource.type == 'image' %}
                <img src="{{ url_for('static', filename='uploads/' + resource.url) }}" class="card-img-top" alt="{{ resource.name }}" style="height: 150px; object-fit: cover;">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ resource.name }}</h5>
                    <p class="card-text">
                        <small class="text-muted">
                            大小: {{ (resource.size / 1024)|round(1) }} KB<br>
                            上传时间: {{ resource.uploaded_at.strftime('%Y-%m-%d %H:%M') }}
                        </small>
                    </p>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <a href="{{ url_for('resource.detail', resource_id=resource.id) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> 查看
                        </a>
                        <a href="{{ url_for('resource.download', resource_id=resource.id) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-download"></i> 下载
                        </a>
                        <button type="button" class="btn btn-sm btn-outline-info rename-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- 列表视图 -->
    <div class="table-responsive">
        <table class="table table-striped" id="resourceTable">
            <thead>
                <tr>
                    <th width="40px">
                        <input type="checkbox" class="form-check-input" id="selectAll">
                    </th>
                    <th class="sortable" data-sort="name">名称 <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="type">类型 <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="size">大小 <i class="fas fa-sort"></i></th>
                    <th class="sortable" data-sort="date">上传时间 <i class="fas fa-sort"></i></th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for resource in resources %}
                <tr>
                    <td>
                        <input type="checkbox" class="form-check-input resource-checkbox" value="{{ resource.id }}" data-name="{{ resource.name }}">
                    </td>
                    <td>
                        <a href="{{ url_for('resource.detail', resource_id=resource.id) }}">
                            {{ resource.name }}
                        </a>
                    </td>
                    <td>
                        {% if resource.type == 'document' %}
                            <i class="fas fa-file-alt"></i> 文档
                        {% elif resource.type == 'image' %}
                            <i class="fas fa-image"></i> 图片
                        {% elif resource.type == 'video' %}
                            <i class="fas fa-video"></i> 视频
                        {% elif resource.type == 'audio' %}
                            <i class="fas fa-music"></i> 音频
                        {% elif resource.type == 'archive' %}
                            <i class="fas fa-file-archive"></i> 压缩文件
                        {% elif resource.type == 'code' %}
                            <i class="fas fa-file-code"></i> 代码
                        {% else %}
                            <i class="fas fa-file"></i> 其他
                        {% endif %}
                    </td>
                    <td data-size="{{ resource.size }}">{{ (resource.size / 1024)|round(1) }} KB</td>
                    <td data-date="{{ resource.uploaded_at.timestamp() }}">{{ resource.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td>
                        <div class="btn-group">
                            <a href="{{ url_for('resource.detail', resource_id=resource.id) }}" class="btn btn-sm btn-outline-info">
                                <i class="fas fa-eye"></i> 查看
                            </a>
                            <a href="{{ url_for('resource.download', resource_id=resource.id) }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-download"></i> 下载
                            </a>
                            <button type="button" class="btn btn-sm btn-outline-secondary rename-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                                <i class="fas fa-edit"></i> 重命名
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-btn" data-id="{{ resource.id }}" data-name="{{ resource.name }}">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    {% else %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> 您还没有上传任何资源。
    </div>
    {% endif %}
</div>

<!-- 重命名模态框 -->
<div class="modal fade" id="renameModal" tabindex="-1" aria-labelledby="renameModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="renameModalLabel">重命名资源</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="renameForm">
                    <input type="hidden" id="resourceId" name="resourceId">
                    <div class="mb-3">
                        <label for="newName" class="form-label">新名称</label>
                        <input type="text" class="form-control" id="newName" name="newName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveRenameBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除资源 <span id="deleteResourceName"></span> 吗？此操作不可撤销。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteForm" method="POST">
                    <button type="submit" class="btn btn-danger">删除</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 批量删除确认模态框 -->
<div class="modal fade" id="batchDeleteModal" tabindex="-1" aria-labelledby="batchDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchDeleteModalLabel">确认批量删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要删除选中的 <span id="selectedCount"></span> 个资源吗？此操作不可撤销。</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：批量删除操作将永久删除所选资源，无法恢复。
                </div>
                <div id="selectedResourcesList" class="mt-3" style="max-height: 200px; overflow-y: auto;">
                    <!-- 选中的资源列表将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmBatchDeleteBtn">确认删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 重命名功能
        const renameButtons = document.querySelectorAll('.rename-btn');
        const renameModal = new bootstrap.Modal(document.getElementById('renameModal'));
        const resourceIdInput = document.getElementById('resourceId');
        const newNameInput = document.getElementById('newName');
        const saveRenameBtn = document.getElementById('saveRenameBtn');
        
        renameButtons.forEach(button => {
            button.addEventListener('click', function() {
                const resourceId = this.dataset.id;
                const resourceName = this.dataset.name;
                
                resourceIdInput.value = resourceId;
                newNameInput.value = resourceName;
                
                renameModal.show();
            });
        });
        
        saveRenameBtn.addEventListener('click', function() {
            const resourceId = resourceIdInput.value;
            const newName = newNameInput.value;
            
            if (!newName.trim()) {
                alert('请输入有效的名称');
                return;
            }
            
            fetch(`/resource/${resourceId}/rename`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `new_name=${encodeURIComponent(newName)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || '重命名失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请稍后重试');
            });
        });
        
        // 删除功能
        const deleteButtons = document.querySelectorAll('.delete-btn');
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
        const deleteResourceName = document.getElementById('deleteResourceName');
        const deleteForm = document.getElementById('deleteForm');
        
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const resourceId = this.dataset.id;
                const resourceName = this.dataset.name;
                
                deleteResourceName.textContent = resourceName;
                deleteForm.action = `/resource/${resourceId}/delete`;
                
                deleteModal.show();
            });
        });
        
        // 表格排序功能
        const sortableHeaders = document.querySelectorAll('.sortable');
        let currentSort = { column: null, direction: 'asc' };
        
        sortableHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
                
                // 更新排序图标
                sortableHeaders.forEach(h => {
                    h.querySelector('i').className = 'fas fa-sort';
                });
                this.querySelector('i').className = `fas fa-sort-${direction === 'asc' ? 'up' : 'down'}`;
                
                // 执行排序
                sortTable(column, direction);
                
                // 更新当前排序状态
                currentSort = { column, direction };
            });
        });
        
        function sortTable(column, direction) {
            const table = document.getElementById('resourceTable');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            // 根据列类型和方向排序
            rows.sort((a, b) => {
                let valueA, valueB;
                
                if (column === 'name') {
                    valueA = a.querySelector('td:nth-child(2) a').textContent.trim().toLowerCase();
                    valueB = b.querySelector('td:nth-child(2) a').textContent.trim().toLowerCase();
                } else if (column === 'type') {
                    valueA = a.querySelector('td:nth-child(3)').textContent.trim().toLowerCase();
                    valueB = b.querySelector('td:nth-child(3)').textContent.trim().toLowerCase();
                } else if (column === 'size') {
                    valueA = parseFloat(a.querySelector('td:nth-child(4)').dataset.size);
                    valueB = parseFloat(b.querySelector('td:nth-child(4)').dataset.size);
                } else if (column === 'date') {
                    valueA = parseFloat(a.querySelector('td:nth-child(5)').dataset.date);
                    valueB = parseFloat(b.querySelector('td:nth-child(5)').dataset.date);
                }
                
                if (direction === 'asc') {
                    return valueA > valueB ? 1 : -1;
                } else {
                    return valueA < valueB ? 1 : -1;
                }
            });
            
            // 重新排列表格行
            rows.forEach(row => tbody.appendChild(row));
        }
        
        // 批量操作功能
        const selectAllCheckbox = document.getElementById('selectAll');
        const resourceCheckboxes = document.querySelectorAll('.resource-checkbox');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const deselectAllBtn = document.getElementById('deselectAllBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteBtn');
        const batchDeleteModal = new bootstrap.Modal(document.getElementById('batchDeleteModal'));
        const selectedCount = document.getElementById('selectedCount');
        const selectedResourcesList = document.getElementById('selectedResourcesList');
        const confirmBatchDeleteBtn = document.getElementById('confirmBatchDeleteBtn');
        
        // 全选/取消全选
        selectAllCheckbox?.addEventListener('change', function() {
            resourceCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchButtons();
        });
        
        // 单个复选框变化时更新按钮状态
        resourceCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchButtons);
        });
        
        // 全选按钮
        selectAllBtn.addEventListener('click', function() {
            resourceCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            if (selectAllCheckbox) selectAllCheckbox.checked = true;
            updateBatchButtons();
        });
        
        // 取消全选按钮
        deselectAllBtn.addEventListener('click', function() {
            resourceCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            if (selectAllCheckbox) selectAllCheckbox.checked = false;
            updateBatchButtons();
        });
        
        // 批量删除按钮
        batchDeleteBtn.addEventListener('click', function() {
            const selectedResources = getSelectedResources();
            selectedCount.textContent = selectedResources.length;
            
            // 显示选中的资源列表
            selectedResourcesList.innerHTML = '';
            selectedResources.forEach(resource => {
                const item = document.createElement('div');
                item.className = 'mb-1';
                item.innerHTML = `<i class="fas fa-file"></i> ${resource.name}`;
                selectedResourcesList.appendChild(item);
            });
            
            batchDeleteModal.show();
        });
        
        // 确认批量删除
        confirmBatchDeleteBtn.addEventListener('click', function() {
            const selectedResources = getSelectedResources();
            const resourceIds = selectedResources.map(r => r.id);
            
            // 发送批量删除请求
            fetch('/resource/batch-delete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ resource_ids: resourceIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert(data.message || '批量删除失败');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请稍后重试');
            });
        });
        
        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const selectedCount = getSelectedCount();
            deselectAllBtn.disabled = selectedCount === 0;
            batchDeleteBtn.disabled = selectedCount === 0;
        }
        
        // 获取选中的资源数量
        function getSelectedCount() {
            return Array.from(resourceCheckboxes).filter(checkbox => checkbox.checked).length;
        }
        
        // 获取选中的资源
        function getSelectedResources() {
            return Array.from(resourceCheckboxes)
                .filter(checkbox => checkbox.checked)
                .map(checkbox => ({
                    id: checkbox.value,
                    name: checkbox.dataset.name
                }));
        }
    });
</script>
{% endblock %}