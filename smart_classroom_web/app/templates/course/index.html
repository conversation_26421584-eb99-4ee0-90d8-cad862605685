{% extends "base.html" %}

{% block title %}
    {% if current_user.is_teacher() %}
        课程管理
    {% else %}
        我的课程
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>
            {% if current_user.is_teacher() %}
                课程管理
            {% else %}
                我的课程
            {% endif %}
        </h1>
        {% if current_user.is_teacher() %}
            <a href="{{ url_for('course.create') }}" class="btn btn-primary">创建新课程</a>
        {% endif %}
    </div>

    {% if courses %}
        <div class="row">
            {% for course in courses %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ course.name }}</h5>
                            <p class="card-text">{{ course.description or '暂无描述' }}</p>
                            <p class="card-text"><small class="text-muted">状态: {{ course.status }}</small></p>
                        </div>
                        <div class="card-footer">
                             <a href="{{ url_for('course.detail', course_id=course.id) }}" class="btn btn-outline-primary">进入课程</a>
                             {% if current_user.is_teacher() %}
                                 <button class="btn btn-outline-secondary btn-sm ms-2" onclick="editCourse({{ course.id }}, '{{ course.name }}', '{{ course.description or '' }}')">编辑</button>
                                 <button class="btn btn-outline-danger btn-sm ms-1" onclick="deleteCourse({{ course.id }}, '{{ course.name }}')">删除</button>
                             {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <p class="lead">
                {% if current_user.is_teacher() %}
                    您还没有创建任何课程。
                {% else %}
                    您还没有加入任何课程。
                {% endif %}
            </p>
        </div>
    {% endif %}
</div>

<!-- 编辑课程模态框 -->
<div class="modal fade" id="editCourseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑课程</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCourseForm">
                <div class="modal-body">
                    <input type="hidden" id="editCourseId">
                    <div class="mb-3">
                        <label for="editCourseName" class="form-label">课程名称</label>
                        <input type="text" class="form-control" id="editCourseName" required>
                    </div>
                    <div class="mb-3">
                        <label for="editCourseDescription" class="form-label">课程描述</label>
                        <textarea class="form-control" id="editCourseDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editCourse(courseId, courseName, courseDescription) {
    document.getElementById('editCourseId').value = courseId;
    document.getElementById('editCourseName').value = courseName;
    document.getElementById('editCourseDescription').value = courseDescription;
    new bootstrap.Modal(document.getElementById('editCourseModal')).show();
}

function deleteCourse(courseId, courseName) {
    if (confirm(`确定要删除课程"${courseName}"吗？此操作不可撤销。`)) {
        fetch(`/course/api/courses/${courseId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('课程删除成功');
                location.reload();
            } else {
                alert('删除失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('删除失败');
        });
    }
}

document.getElementById('editCourseForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const courseId = document.getElementById('editCourseId').value;
    const courseName = document.getElementById('editCourseName').value;
    const courseDescription = document.getElementById('editCourseDescription').value;
    
    fetch(`/course/api/courses/${courseId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            name: courseName,
            description: courseDescription
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('课程更新成功');
            location.reload();
        } else {
            alert('更新失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新失败');
    });
});
</script>
{% endblock %}