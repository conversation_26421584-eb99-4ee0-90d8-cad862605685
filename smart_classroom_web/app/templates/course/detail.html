{% extends "base.html" %}

{% block title %}课程详情: {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    #qrcode img {
        max-width: 100%;
        height: auto;
        display: block;
        margin: 0 auto;
    }
    
    .session-status-card {
        border-left: 4px solid #28a745;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .session-status-card.inactive {
        border-left-color: #6c757d;
    }
    
    .duration-counter {
        font-weight: bold;
        color: #28a745;
    }
    
    .btn-start-class {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-start-class:hover {
        background: linear-gradient(45deg, #218838, #1ea085);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .btn-end-class {
        background: linear-gradient(45deg, #dc3545, #fd7e14);
        border: none;
        color: white;
        transition: all 0.3s ease;
    }
    
    .btn-end-class:hover {
        background: linear-gradient(45deg, #c82333, #e8650e);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h3>{{ course.name }}</h3>
                </div>
                <div class="card-body">
                    <p class="card-text">{{ course.description or '暂无描述' }}</p>
                    <p class="card-text"><small class="text-muted">状态: {{ course.status }} | 创建于: {{ course.created_at.strftime('%Y-%m-%d %H:%M') }}</small></p>
                </div>
            </div>

            <!-- 课程会话状态卡片 -->
            <div class="card mb-3 session-status-card {% if not active_session %}inactive{% endif %}">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            {% if active_session %}
                                <h5 class="text-success mb-1">
                                    <i class="fas fa-play-circle"></i> 正在上课
                                </h5>
                                <p class="mb-0">
                                    <strong>{{ active_session.session_name or '当前课程' }}</strong>
                                    <br>
                                    <small class="text-muted">
                                        开始时间: {{ active_session.start_time.strftime('%H:%M') }}
                                        | 已进行: <span id="duration" class="duration-counter">{{ active_session.get_duration_minutes() }}</span> 分钟
                                    </small>
                                </p>
                            {% else %}
                                <h5 class="text-muted mb-1">
                                    <i class="fas fa-pause-circle"></i> 未开始上课
                                </h5>
                                <p class="mb-0">
                                    <small class="text-muted">点击"开始上课"按钮开始本节课</small>
                                </p>
                            {% endif %}
                        </div>
                        <div class="col-md-4 text-end">
                            {% if active_session %}
                                <button class="btn btn-end-class" onclick="endClass()">
                                    <i class="fas fa-stop"></i> 结束上课
                                </button>
                            {% else %}
                                <button class="btn btn-start-class" onclick="startClass()">
                                    <i class="fas fa-play"></i> 开始上课
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a href="{{ url_for('broadcast.teacher_broadcast', course_id=course.id) }}" class="nav-link">广播</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('group.manage', course_id=course.id) }}" class="nav-link">分组</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('group.monitor', course_id=course.id) }}" class="nav-link">设备</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('group.attendance', course_id=course.id) }}" class="nav-link">考勤</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('group.share_file', course_id=course.id) }}" class="nav-link">小组发文件</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('report.reports', course_id=course.id) }}" class="nav-link">报告</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('report.activities', course_id=course.id) }}" class="nav-link">课堂活动记录</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.random_pick', course_id=course.id) }}" class="nav-link">点名</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.send_question', course_id=course.id) }}" class="nav-link">题目</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.question_sessions', course_id=course.id) }}" class="nav-link">题目列表</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.share_file', course_id=course.id) }}" class="nav-link">发送文件</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.group_discussion', course_id=course.id) }}" class="nav-link">分组讨论</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.student_score', course_id=course.id) }}" class="nav-link">学生评分</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('interaction.group_score', course_id=course.id) }}" class="nav-link">小组评分</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4>课程访问码</h4>
                </div>
                <div class="card-body text-center">
                    <p style="font-size: 2.5rem; font-weight: bold; color: #333; letter-spacing: 4px;">{{ course.access_code }}</p>
                    <button class="btn btn-primary w-100" onclick="generateQRCode()">二维码</button>
                    <div id="qrcode" class="mt-3"></div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/qrcode-generator/qrcode.js"></script>
<script>
function generateQRCode() {
    fetch("{{ url_for('course.generate_qr') }}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            course_id: {{ course.id }},
            base_url: window.location.origin
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.qr_code) {
            document.getElementById('qrcode').innerHTML = `<img src="${data.qr_code}" alt="课程二维码">`;
        } else {
            alert('二维码生成失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('二维码生成请求失败:', error);
        alert('二维码生成请求失败');
    });
}

function startClass() {
    fetch("{{ url_for('course.start_class', course_id=course.id) }}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload(); // 刷新页面以更新状态
        } else {
            alert('开始上课失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('开始上课请求失败:', error);
        alert('开始上课请求失败');
    });
}

function endClass() {
    if (confirm('确定要结束本节课吗？')) {
        fetch("{{ url_for('course.end_class', course_id=course.id) }}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload(); // 刷新页面以更新状态
            } else {
                alert('结束上课失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('结束上课请求失败:', error);
            alert('结束上课请求失败');
        });
    }
}

// 如果正在上课，每分钟更新一次时长显示
{% if active_session %}
setInterval(function() {
    fetch("{{ url_for('course.get_current_session', course_id=course.id) }}")
    .then(response => response.json())
    .then(data => {
        if (data.session && data.session.status === 'active') {
            document.getElementById('duration').textContent = data.session.duration_minutes;
        }
    })
    .catch(error => {
        console.error('获取会话信息失败:', error);
    });
}, 60000); // 每分钟更新一次
{% endif %}

</script>
{% endblock %}