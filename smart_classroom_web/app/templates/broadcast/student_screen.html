{% extends "base.html" %}

{% block title %}学生投屏 - {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .broadcast-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 200px);
        min-height: 500px;
    }
    
    .video-container {
        flex: 1;
        display: flex;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }
    
    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    
    .control-panel {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 15px;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-active {
        background-color: #28a745;
    }
    
    .status-inactive {
        background-color: #dc3545;
    }
    
    .status-connecting {
        background-color: #ffc107;
    }
    
    .self-view {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 200px;
        height: 150px;
        border: 2px solid #fff;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }
    
    .self-view video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .no-broadcast {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
        font-size: 1.5rem;
        text-align: center;
    }
    
    .btn-group {
        margin-bottom: 10px;
    }
    
    .source-selector {
        margin-bottom: 15px;
    }
    
    .source-selector .btn {
        margin-right: 5px;
    }
    
    .source-selector .btn.active {
        background-color: #007bff;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mt-4 mb-4">学生投屏 - {{ course.name }}</h1>
    
    <div class="broadcast-container">
        <div class="source-selector">
            <div class="btn-group" role="group">
                <button id="cameraSourceBtn" class="btn btn-outline-primary active">摄像头</button>
                <button id="screenSourceBtn" class="btn btn-outline-primary">屏幕</button>
                <button id="multiSourceBtn" class="btn btn-outline-primary">多画面</button>
            </div>
        </div>
        
        <div class="video-container">
            <div id="mainVideoContainer">
                <video id="mainVideo" autoplay muted playsinline></video>
            </div>
            <div id="secondaryVideoContainer" class="self-view" style="display: none;">
                <video id="secondaryVideo" autoplay muted playsinline></video>
            </div>
            <div id="noVideo" class="no-broadcast">
                <div>
                    <i class="fas fa-video-slash fa-3x mb-3"></i>
                    <p>未启动视频</p>
                </div>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <div class="status">
                        状态: <span id="connectionStatus"><span class="status-indicator status-inactive"></span> 未连接</span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group">
                        <button id="startBtn" class="btn btn-success">开始投屏</button>
                        <button id="stopBtn" class="btn btn-danger" disabled>停止投屏</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // DOM元素
        const cameraSourceBtn = document.getElementById('cameraSourceBtn');
        const screenSourceBtn = document.getElementById('screenSourceBtn');
        const multiSourceBtn = document.getElementById('multiSourceBtn');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const mainVideo = document.getElementById('mainVideo');
        const secondaryVideo = document.getElementById('secondaryVideo');
        const secondaryVideoContainer = document.getElementById('secondaryVideoContainer');
        const noVideo = document.getElementById('noVideo');
        const connectionStatus = document.getElementById('connectionStatus');
        
        // 状态变量
        let webrtcClient = null;
        let isConnected = false;
        let isBroadcasting = false;
        let currentMode = 'camera'; // 'camera', 'screen', 'multi'
        let cameraStream = null;
        let screenStream = null;
        
        // 初始化WebRTC客户端
        function initWebRTCClient() {
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
            
            webrtcClient = new WebRTCClient({
                socketUrl: window.location.origin,
                userId: '{{ current_user.id }}',
                userType: 'student'
            });
            
            // 设置事件处理器
            webrtcClient.onConnected = (clientId) => {
                updateStatus('已连接到信令服务器', 'connecting');
                
                // 加入课程房间
                webrtcClient.joinRoom('course_{{ course.id }}');
            };
            
            webrtcClient.onRoomJoined = (data) => {
                updateStatus('已加入房间', 'active');
                isConnected = true;
                updateButtons();
            };
            
            webrtcClient.onPeerJoined = (data) => {
                console.log('新对等端加入:', data);
            };
            
            webrtcClient.onPeerLeft = (data) => {
                console.log('对等端离开:', data);
            };
            
            webrtcClient.onError = (data) => {
                updateStatus(`错误: ${data.message}`, 'inactive');
            };
            
            webrtcClient.onScreenSelectedByTeacher = () => {
                updateStatus('教师已选择您的画面', 'active');
            };
            
            webrtcClient.onScreenDeselectedByTeacher = () => {
                updateStatus('教师已取消选择您的画面', 'active');
            };
        }
        
        // 更新连接状态
        function updateStatus(message, status) {
            const indicator = connectionStatus.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${status}`;
            connectionStatus.innerHTML = `<span class="status-indicator status-${status}"></span> ${message}`;
        }
        
        // 更新按钮状态
        function updateButtons() {
            startBtn.disabled = !isConnected || isBroadcasting;
            stopBtn.disabled = !isConnected || !isBroadcasting;
        }
        
        // 切换视频源模式
        function switchMode(mode) {
            currentMode = mode;
            
            // 更新按钮状态
            cameraSourceBtn.classList.remove('active');
            screenSourceBtn.classList.remove('active');
            multiSourceBtn.classList.remove('active');
            
            switch (mode) {
                case 'camera':
                    cameraSourceBtn.classList.add('active');
                    secondaryVideoContainer.style.display = 'none';
                    break;
                case 'screen':
                    screenSourceBtn.classList.add('active');
                    secondaryVideoContainer.style.display = 'none';
                    break;
                case 'multi':
                    multiSourceBtn.classList.add('active');
                    secondaryVideoContainer.style.display = 'block';
                    break;
            }
            
            // 如果正在广播，需要重新启动广播
            if (isBroadcasting) {
                stopBroadcast();
                startBroadcast();
            }
        }
        
        // 启动摄像头
        async function startCamera() {
            try {
                if (cameraStream) {
                    // 如果已经有摄像头流，直接使用
                    return cameraStream;
                }
                
                updateStatus('正在启动摄像头...', 'connecting');
                cameraStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
                return cameraStream;
            } catch (error) {
                updateStatus(`启动摄像头失败: ${error.message}`, 'inactive');
                throw error;
            }
        }
        
        // 启动屏幕共享
        async function startScreenSharing() {
            try {
                if (screenStream) {
                    // 如果已经有屏幕流，直接使用
                    return screenStream;
                }
                
                updateStatus('正在启动屏幕共享...', 'connecting');
                screenStream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        cursor: 'always',
                        displaySurface: 'monitor'
                    },
                    audio: false
                });
                
                // 监听屏幕共享停止事件
                screenStream.getVideoTracks()[0].onended = () => {
                    if (currentMode === 'screen' || (currentMode === 'multi' && isBroadcasting)) {
                        stopBroadcast();
                    }
                    releaseScreenStream();
                };
                
                return screenStream;
            } catch (error) {
                updateStatus(`屏幕共享失败: ${error.message}`, 'inactive');
                throw error;
            }
        }
        
        // 释放摄像头流
        function releaseCameraStream() {
            if (cameraStream) {
                cameraStream.getTracks().forEach(track => track.stop());
                cameraStream = null;
            }
        }
        
        // 释放屏幕流
        function releaseScreenStream() {
            if (screenStream) {
                screenStream.getTracks().forEach(track => track.stop());
                screenStream = null;
            }
        }
        
        // 开始广播
        async function startBroadcast() {
            try {
                if (!isConnected) {
                    updateStatus('未连接到服务器', 'inactive');
                    return;
                }
                
                noVideo.style.display = 'none';
                
                switch (currentMode) {
                    case 'camera':
                        const cameraStream = await startCamera();
                        mainVideo.srcObject = cameraStream;
                        webrtcClient.localStream = cameraStream;
                        break;
                        
                    case 'screen':
                        const screenStream = await startScreenSharing();
                        mainVideo.srcObject = screenStream;
                        webrtcClient.localStream = screenStream;
                        break;
                        
                    case 'multi':
                        const camera = await startCamera();
                        const screen = await startScreenSharing();
                        
                        // 主视频显示屏幕，次视频显示摄像头
                        mainVideo.srcObject = screen;
                        secondaryVideo.srcObject = camera;
                        
                        // 创建一个组合流
                        const combinedStream = new MediaStream();
                        
                        // 添加屏幕视频轨道
                        screen.getVideoTracks().forEach(track => {
                            combinedStream.addTrack(track);
                        });
                        
                        // 添加摄像头音频轨道
                        camera.getAudioTracks().forEach(track => {
                            combinedStream.addTrack(track);
                        });
                        
                        webrtcClient.localStream = combinedStream;
                        break;
                }
                
                // 开始广播
                webrtcClient.startBroadcast();
                isBroadcasting = true;
                updateStatus('正在投屏', 'active');
                updateButtons();
            } catch (error) {
                updateStatus(`启动投屏失败: ${error.message}`, 'inactive');
                console.error('启动投屏失败:', error);
            }
        }
        
        // 停止广播
        function stopBroadcast() {
            if (webrtcClient && webrtcClient.localStream) {
                webrtcClient.stopBroadcast();
                webrtcClient.stopLocalStream();
                webrtcClient.localStream = null;
            }
            
            // 清除视频源
            mainVideo.srcObject = null;
            secondaryVideo.srcObject = null;
            noVideo.style.display = 'flex';
            
            isBroadcasting = false;
            updateStatus('投屏已停止', 'connecting');
            updateButtons();
        }
        
        // 初始化
        initWebRTCClient();
        
        // 事件监听器
        cameraSourceBtn.addEventListener('click', () => switchMode('camera'));
        screenSourceBtn.addEventListener('click', () => switchMode('screen'));
        multiSourceBtn.addEventListener('click', () => switchMode('multi'));
        
        startBtn.addEventListener('click', startBroadcast);
        stopBtn.addEventListener('click', stopBroadcast);
        
        // 页面卸载时断开连接
        window.addEventListener('beforeunload', () => {
            releaseCameraStream();
            releaseScreenStream();
            if (webrtcClient) {
                webrtcClient.disconnect();
            }
        });
    });
</script>
{% endblock %}