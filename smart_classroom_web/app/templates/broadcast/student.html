{% from 'macros/chat_panel.html' import chat_panel %}

{% extends "base.html" %}

{% block title %}学生广播 - {{ course.name }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .broadcast-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 200px);
        min-height: 500px;
    }

    .video-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        position: relative;
    }

    .video-container video {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .control-panel {
        padding: 15px;
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-top: 15px;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-active {
        background-color: #28a745;
    }

    .status-inactive {
        background-color: #dc3545;
    }

    .status-connecting {
        background-color: #ffc107;
    }

    .self-view {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 200px;
        height: 150px;
        border: 2px solid #fff;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
        display: none;
    }

    .self-view video {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .no-broadcast {
        display: flex;
        justify-content: center;
        height: 100%;
        width: 100%;
        color: #6c757d;
        font-size: 1.5rem;
        text-align: center;
    }

    .btn-group {
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h1 class="mt-4 mb-4">学生广播 - {{ course.name }}</h1>

    <div class="broadcast-container">
        <div class="video-container">
            <video id="remoteVideo" autoplay playsinline></video>
            <div id="noBroadcast" class="no-broadcast">
                <div>
                    <i class="fas fa-broadcast-tower fa-3x mb-3"></i>
                    <p>等待教师开始广播...</p>
                </div>
            </div>
            <div id="selfView" class="self-view">
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
        </div>

        <div class="control-panel">
            <div class="row">
                <div class="col-md-6">
                    <div class="status">
                        状态: <span id="connectionStatus"><span class="status-indicator status-inactive"></span>
                            未连接</span>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group">
                        <button id="cameraBtn" class="btn">开启摄像头</button>
                        <button id="screenBtn" class="btn">共享屏幕</button>
                        <button id="stopBtn" class="btn" disabled>停止共享</button>
                        <button id="toggleChatBtn" class="btn">聊天</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{{ chat_panel() }}
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.socket.io/4.6.0/socket.io.min.js"></script>
<script src="{{ url_for('static', filename='js/webrtc.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const cameraBtn = document.getElementById('cameraBtn');
        const screenBtn = document.getElementById('screenBtn');
        const stopBtn = document.getElementById('stopBtn');
        const localVideo = document.getElementById('localVideo');
        const remoteVideo = document.getElementById('remoteVideo');
        const selfView = document.getElementById('selfView');
        const noBroadcast = document.getElementById('noBroadcast');
        const connectionStatus = document.getElementById('connectionStatus');
        const toggleChatBtn = document.getElementById('toggleChatBtn');
        const chatPanel = document.getElementById('chatPanel');
        const closeChatBtn = document.getElementById('closeChatBtn');
        const chatBody = document.getElementById('chatBody');
        const sendMessageBtn = document.getElementById('sendMessageBtn');
        const chatMessageInput = document.getElementById('chatMessageInput');
        const chatFileInput = document.getElementById('chatFileInput');

        let webrtcClient = null;
        let isConnected = false;
        let isBroadcastActive = false;

        // 设备状态监控变量
        let deviceId = null;
        let deviceStatusInterval = null;

        // 生成设备ID
        function generateDeviceId() {
            let storedDeviceId = localStorage.getItem('deviceId');
            if (storedDeviceId) {
                return storedDeviceId;
            }

            const userId = {{ current_user.id }};
            const timestamp = Date.now();
            const random = Math.random().toString(36).substr(2, 9);
            const newDeviceId = `student_${userId}_${timestamp}_${random}`;
            localStorage.setItem('deviceId', newDeviceId);
            return newDeviceId;
        }

        // 注册设备状态
        function registerDevice() {
            deviceId = generateDeviceId();

            const deviceData = {
                device_id: deviceId,
                type: 'student',
                owner_id: {{ current_user.id }},
                status: 'online',
                course_id: {{ course.id }}
            };

    // 通过WebRTC客户端的socket发送设备状态更新
    if (webrtcClient && webrtcClient.socket) {
        webrtcClient.socket.emit('device_status_update', deviceData);
        console.log('设备已注册:', deviceId);
    }
        }

    // 更新设备状态
    function updateDeviceStatus() {
        if (deviceId && webrtcClient && webrtcClient.socket) {
            const deviceData = {
                device_id: deviceId,
                type: 'student',
                owner_id: {{ current_user.id }},
                status: 'online'
            };

    webrtcClient.socket.emit('device_status_update', deviceData);
    console.log('设备状态已更新:', deviceId);
            }
        }

    // 设置设备状态为离线
    function setDeviceOffline() {
        if (deviceId && webrtcClient && webrtcClient.socket) {
            const deviceData = {
                device_id: deviceId,
                type: 'student',
                owner_id: {{ current_user.id }},
                status: 'offline'
            };

    webrtcClient.socket.emit('device_status_update', deviceData);
    console.log('设备状态设为离线:', deviceId);
            }
        }

    // 初始化WebRTC客户端
    function initWebRTCClient() {
        if (webrtcClient) {
            webrtcClient.disconnect();
        }

        webrtcClient = new WebRTCClient({
            socketUrl: window.location.origin,
            userId: '{{ current_user.id }}',
            userType: 'student'
        });

        // 设置事件处理器
        webrtcClient.onConnected = (clientId) => {
            updateStatus('已连接到信令服务器', 'connecting');

            // 注册设备状态
            registerDevice();

            // 设置定期更新设备状态（每30秒）
            deviceStatusInterval = setInterval(updateDeviceStatus, 30000);

            // 加入课程房间
            webrtcClient.joinRoom('course_{{ course.id }}');
        };

        webrtcClient.onRoomJoined = (data) => {
            updateStatus('已加入房间', 'active');
            isConnected = true;
            updateButtons();
        };

        webrtcClient.onPeerJoined = (data) => {
            console.log('新对等端加入:', data);

            // 如果是教师加入，可能会开始广播
            if (data.user_type === 'teacher') {
                updateStatus('教师已加入房间', 'active');
            }
            
            // 如果学生有本地流，重新发起连接
            if (webrtcClient.localStream && data.user_type === 'teacher') {
                console.log('教师加入，学生重新发起连接');
                setTimeout(() => {
                    webrtcClient.renegotiate(data.client_id);
                }, 1000);
            }
        };

        webrtcClient.onPeerLeft = (data) => {
            console.log('对等端离开:', data);
        };

        webrtcClient.onRemoteStream = (peerId, stream) => {
            console.log('接收到远程流:', peerId, stream);

            // 检查流是否有效
            if (stream && stream.getTracks().length > 0) {
                console.log('流轨道数量:', stream.getTracks().length);
                console.log('流轨道类型:', stream.getTracks().map(t => t.kind));

                // 显示远程视频
                remoteVideo.srcObject = stream;
                noBroadcast.style.display = 'none';
                isBroadcastActive = true;

                // 确保视频开始播放
                remoteVideo.play().catch(e => {
                    console.error('播放远程视频失败:', e);
                });
            } else {
                console.warn('接收到的流无效或没有轨道');
            }
        };

        webrtcClient.onBroadcastStarted = (data) => {
            console.log('广播开始:', data);
            updateStatus('教师广播已开始', 'active');
            noBroadcast.style.display = 'none';
            isBroadcastActive = true;
        };

        webrtcClient.onBroadcastStopped = (data) => {
            console.log('广播停止:', data);
            updateStatus('教师广播已停止', 'connecting');
            noBroadcast.style.display = 'flex';
            remoteVideo.srcObject = null;
            isBroadcastActive = false;
        };

        webrtcClient.onError = (data) => {
            updateStatus(`错误: ${data.message}`, 'inactive');
        };
    }

    // 更新连接状态
    function updateStatus(message, status) {
        const indicator = connectionStatus.querySelector('.status-indicator');
        indicator.className = `status-indicator status-${status}`;
        connectionStatus.innerHTML = `<span class="status-indicator status-${status}"></span> ${message}`;
    }

    // 更新按钮状态
    function updateButtons() {
        cameraBtn.disabled = !isConnected || webrtcClient.localStream;
        screenBtn.disabled = !isConnected || webrtcClient.localStream;
        stopBtn.disabled = !isConnected || !webrtcClient.localStream;
    }

    // 初始化
    initWebRTCClient();

    // 事件监听器
    cameraBtn.addEventListener('click', async () => {
        try {
            updateStatus('正在启动摄像头...', 'connecting');
            const stream = await webrtcClient.startCamera();
            localVideo.srcObject = stream;
            selfView.style.display = 'block';
            updateStatus('摄像头已启动', 'active');
            updateButtons();
            
            console.log('学生摄像头已启动，流轨道数量:', stream.getTracks().length);
            console.log('当前连接的对等端数量:', Object.keys(webrtcClient.peers).length);
            
            // 重新协商所有连接
            setTimeout(() => {
                webrtcClient.renegotiateAll();
            }, 500);
        } catch (error) {
            updateStatus(`启动摄像头失败: ${error.message}`, 'inactive');
        }
    });

    screenBtn.addEventListener('click', async () => {
        try {
            updateStatus('正在启动屏幕共享...', 'connecting');
            const stream = await webrtcClient.startScreenSharing();
            localVideo.srcObject = stream;
            selfView.style.display = 'block';
            updateStatus('屏幕共享已启动', 'active');
            updateButtons();
            
            console.log('学生屏幕共享已启动，流轨道数量:', stream.getTracks().length);
            console.log('当前连接的对等端数量:', Object.keys(webrtcClient.peers).length);
            
            // 重新协商所有连接
            setTimeout(() => {
                webrtcClient.renegotiateAll();
            }, 500);
        } catch (error) {
            updateStatus(`屏幕共享失败: ${error.message}`, 'inactive');
        }
    });

    stopBtn.addEventListener('click', () => {
        webrtcClient.stopLocalStream();
        localVideo.srcObject = null;
        selfView.style.display = 'none';
        updateStatus(isBroadcastActive ? '教师广播正在进行' : '已连接到房间', 'active');
        updateButtons();
    });

    // 聊天面板逻辑
    toggleChatBtn.addEventListener('click', () => {
        chatPanel.classList.toggle('open');
    });

    closeChatBtn.addEventListener('click', () => {
        chatPanel.classList.remove('open');
    });

    sendMessageBtn.addEventListener('click', function () {
        const content = chatMessageInput.value.trim();
        const file = chatFileInput.files[0];

        if (!content && !file) {
            alert('请输入消息或选择文件。');
            return;
        }

        const formData = new FormData();
        formData.append('course_id', '{{ course.id }}');
        formData.append('content', content);
        if (file) {
            formData.append('file', file);
        }

        this.disabled = true;
        this.textContent = '正在发送...';

        fetch("{{ url_for('interaction.send_to_teacher') }}", {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addInteractionMessage({
                        content: content,
                        resource: data.resource,
                        created_at: new Date().toISOString()
                    }, true); // Display own message
                    chatMessageInput.value = '';
                    chatFileInput.value = '';
                } else {
                    alert('发送失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发送时发生错误。');
            })
            .finally(() => {
                this.disabled = false;
                this.textContent = '发送';
            });
    });

    // 监听教师消息
    webrtcClient.socket.on('teacher_message', (data) => {
        addInteractionMessage(data, false);
    });

    function addInteractionMessage(data, isFromSelf) {
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${isFromSelf ? 'teacher' : 'student'}`;

        let fileHtml = '';
        if (data.resource) {
            fileHtml = `
                    <div class="mt-1">
                        <a href="/resource/download/${data.resource.id}" target="_blank">下载附件: ${data.resource.name}</a>
                    </div>
                `;
        }

        const senderName = isFromSelf ? '我' : data.teacher_name;

        messageElement.innerHTML = `
                <div class="message-bubble">
                    ${data.content || ''}
                    ${fileHtml}
                </div>
                <div class="message-meta">
                    ${senderName} @ ${new Date(data.created_at).toLocaleTimeString()}
                </div>
            `;

        chatBody.appendChild(messageElement);
        chatBody.scrollTop = chatBody.scrollHeight;
    }

    // 页面卸载时断开连接
    window.addEventListener('beforeunload', () => {
        setDeviceOffline();
        if (deviceStatusInterval) {
            clearInterval(deviceStatusInterval);
        }
        if (webrtcClient) {
            webrtcClient.disconnect();
        }
    });

    // 页面隐藏时设置设备为离线状态
    document.addEventListener('visibilitychange', () => {
        if (document.hidden) {
            setDeviceOffline();
            if (deviceStatusInterval) {
                clearInterval(deviceStatusInterval);
                deviceStatusInterval = null;
            }
        } else {
            // 页面重新可见时重新注册设备
            if (webrtcClient && webrtcClient.socket) {
                registerDevice();
                if (!deviceStatusInterval) {
                    deviceStatusInterval = setInterval(updateDeviceStatus, 30000);
                }
            }
        }
    });
    });
</script>
{% endblock %}