<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧教室系统{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    {% block styles %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-light">
        <div class="container-fluid">
            <a href="{{ url_for('main.index') }}" class="navbar-brand">智慧教室</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a href="{{ url_for('main.dashboard') }}" class="nav-link">首页</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('main.profile') }}" class="nav-link">个人信息</a>
                        </li>
                        
                        {% if current_user.is_teacher() %}
                            {# 教师功能 #}
                            <li class="nav-item">
                                <a href="{{ url_for('course.index') }}" class="nav-link">上课</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('interaction.course_plans') }}" class="nav-link">课程安排</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('resource.index') }}" class="nav-link">课件</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('interaction.questions') }}" class="nav-link">习题</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('interaction.exams') }}" class="nav-link">试卷</a>
                            </li>
                        {% else %}
                            {# 学生功能 #}
                            <li class="nav-item">
                                <a href="{{ url_for('course.index') }}" class="nav-link">我的课程</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('course.join_course_page') }}" class="nav-link">加入课程</a>
                            </li>
                            <li class="nav-item">
                                <a href="{{ url_for('resource.search') }}" class="nav-link">课程资料</a>
                            </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a href="{{ url_for('auth.logout') }}" class="nav-link">登出</a>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a href="{{ url_for('auth.login') }}" class="nav-link">登录</a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('auth.register') }}" class="nav-link">注册</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>