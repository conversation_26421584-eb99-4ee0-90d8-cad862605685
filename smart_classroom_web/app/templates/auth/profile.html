{% extends "base.html" %}

{% block title %}个人信息 - 智慧教室系统{% endblock %}

{% block content %}
    <div class="card" style="margin-top: 2rem;">
        <div class="card-header">个人信息</div>
        <div class="card-body">
            <p><strong>用户名：</strong>{{ current_user.username }}</p>
            <p><strong>邮箱：</strong>{{ current_user.email }}</p>
            <p><strong>角色：</strong>{{ '教师' if current_user.is_teacher() else '学生' }}</p>
            <p><strong>注册时间：</strong>{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') if current_user.created_at else '未知' }}</p>
            {% if current_user.last_login %}
                <p><strong>上次登录：</strong>{{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}</p>
            {% endif %}
        </div>
    </div>
{% endblock %}

<style>
    .card {
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 1rem;
        max-width: 500px; 
        margin: 0 auto;
    }
    .card-header {
        font-weight: bold;
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }
    .card-body p {
        margin-bottom: 0.5rem;
    }
</style>