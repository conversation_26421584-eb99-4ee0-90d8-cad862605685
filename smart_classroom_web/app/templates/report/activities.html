{% extends 'base.html' %}

{% block title %}课堂活动记录 - {{ course.name }}{% endblock %}
{%block styles %}
<style>
    .timeline {
        position: relative;
        padding: 20px 0;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 20px;
        width: 2px;
        background: #ddd;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }
    
    .timeline-date {
        position: absolute;
        left: -220px;
        top: 0;
        width: 200px;
        text-align: right;
        color: #666;
        font-size: 0.9rem;
    }
    
    .timeline-content {
        position: relative;
        margin-left: 50px;
        background: #fff;
        border-radius: 4px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    }
    
    .timeline-badge {
        position: absolute;
        left: -15px;
        top: 15px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        color: white;
    }
    
    .timeline-footer {
        margin-top: 10px;
    }
    
    @media (max-width: 767px) {
        .timeline:before {
            left: 40px;
        }
        
        .timeline-date {
            position: relative;
            left: 0;
            top: 0;
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            margin-left: 70px;
        }
        
        .timeline-badge {
            left: 5px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>课堂活动记录 - {{ course.name }}</h2>
    
    <div class="card mb-4">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">活动时间线</h5>
                <a href="{{ url_for('report.reports', course_id=course.id) }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-chart-bar"></i> 查看课堂报告
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if activities %}
                <div class="timeline">
                    {% for activity in activities %}
                        <div class="timeline-item">
                            <div class="timeline-date">
                                {{ activity.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </div>
                            <div class="timeline-content">
                                <div class="timeline-badge 
                                    {% if activity.type == 'question' %}bg-primary
                                    {% elif activity.type == 'discussion' %}bg-success
                                    {% elif activity.type == 'file_share' %}bg-info
                                    {% elif activity.type == 'random_pick' %}bg-warning
                                    {% elif activity.type == 'broadcast' %}bg-danger
                                    {% elif activity.type == 'attendance' %}bg-secondary
                                    {% else %}bg-dark{% endif %}">
                                    {% if activity.type == 'question' %}<i class="fas fa-question"></i>
                                    {% elif activity.type == 'discussion' %}<i class="fas fa-comments"></i>
                                    {% elif activity.type == 'file_share' %}<i class="fas fa-file-alt"></i>
                                    {% elif activity.type == 'random_pick' %}<i class="fas fa-random"></i>
                                    {% elif activity.type == 'broadcast' %}<i class="fas fa-broadcast-tower"></i>
                                    {% elif activity.type == 'attendance' %}<i class="fas fa-clipboard-check"></i>
                                    {% else %}<i class="fas fa-star"></i>{% endif %}
                                </div>
                                <h5>{{ activity.title }}</h5>
                                {% if activity.description %}
                                    <p>{{ activity.description }}</p>
                                {% endif %}
                                <div class="timeline-footer">
                                    <span class="badge 
                                        {% if activity.status == 'active' %}badge-success
                                        {% else %}badge-secondary{% endif %}">
                                        {{ activity.status }}
                                    </span>
                                    {% if activity.participants_count > 0 %}
                                        <span class="badge badge-info">
                                            <i class="fas fa-users"></i> {{ activity.participants_count }} 人参与
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info">
                    暂无活动记录
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}