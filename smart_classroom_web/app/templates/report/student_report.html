{% extends 'base.html' %}

{% block title %}个人学习报告 - {{ course.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>个人学习报告 - {{ course.name }}</h2>
    <p class="text-muted">学生: {{ current_user.username }}</p>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">学习概览</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <span>出勤率:</span>
                        <strong>{{ "%.1f"|format(attendance_rate) }}%</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>答题正确率:</span>
                        <strong>{{ "%.1f"|format(accuracy_rate) }}%</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>被点名次数:</span>
                        <strong>{{ random_picks|length }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>总评分:</span>
                        <strong class="{% if total_score >= 0 %}text-success{% else %}text-danger{% endif %}">
                            {{ total_score }}
                        </strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>参与讨论次数:</span>
                        <strong>{{ group_discussions|length }}</strong>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">最近课堂报告</h5>
                </div>
                <div class="card-body">
                    {% if reports %}
                        <div class="list-group">
                            {% for report in reports %}
                                <a href="{{ url_for('report.report_detail', report_id=report.id) }}" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h6 class="mb-1">{{ report.date.strftime('%Y-%m-%d') }}</h6>
                                        <small>互动: {{ report.interaction_count }}</small>
                                    </div>
                                    <small class="text-muted">签到: {{ report.attendance_count }} | 题目: {{ report.question_count }}</small>
                                </a>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            暂无课堂报告
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">考勤记录</h5>
                </div>
                <div class="card-body">
                    {% if attendances %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>状态</th>
                                        <th>签到时间</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attendance in attendances %}
                                        <tr>
                                            <td>{{ attendance.date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                <span class="badge 
                                                    {% if attendance.status == 'present' %}badge-success
                                                    {% elif attendance.status == 'late' %}badge-warning
                                                    {% else %}badge-danger{% endif %}">
                                                    {{ {'present': '出席', 'late': '迟到', 'absent': '缺席'}[attendance.status] }}
                                                </span>
                                            </td>
                                            <td>{{ attendance.check_in_time.strftime('%H:%M') if attendance.check_in_time else '-' }}</td>
                                            <td>{{ attendance.note or '-' }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            暂无考勤记录
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0">答题记录</h5>
                        </div>
                        <div class="card-body">
                            {% if answers %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>题目类型</th>
                                                <th>结果</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for answer in answers %}
                                                <tr>
                                                    <td>{{ answer.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                    <td>{{ "单选题" if answer.session_question_id else "临时题目" }}</td>
                                                    <td>
                                                        <span class="badge {% if answer.is_correct %}badge-success{% else %}badge-danger{% endif %}">
                                                            {{ '正确' if answer.is_correct else '错误' }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    暂无答题记录
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">评分记录</h5>
                        </div>
                        <div class="card-body">
                            {% if scores %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>分数</th>
                                                <th>原因</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for score in scores %}
                                                <tr>
                                                    <td>{{ score.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                                    <td>
                                                        <span class="badge {% if score.score > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                                            {{ score.score }}
                                                        </span>
                                                    </td>
                                                    <td>{{ score.reason or '-' }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    暂无评分记录
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">小组讨论参与</h5>
                </div>
                <div class="card-body">
                    {% if group_discussions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>主题</th>
                                        <th>小组</th>
                                        <th>时长</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for discussion in group_discussions %}
                                        <tr>
                                            <td>{{ discussion.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ discussion.topic }}</td>
                                            <td>{{ discussion.title }}</td>
                                            <td>{{ discussion.duration }} 分钟</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            暂无小组讨论记录
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">学习进度图表</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <canvas id="attendanceChart" width="400" height="300"></canvas>
                        </div>
                        <div class="col-md-6">
                            <canvas id="answersChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 考勤图表
        var attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        var attendanceChart = new Chart(attendanceCtx, {
            type: 'pie',
            data: {
                labels: ['出席', '迟到', '缺席'],
                datasets: [{
                    data: [
                        {{ attendances|selectattr('status', 'equalto', 'present')|list|length }},
                        {{ attendances|selectattr('status', 'equalto', 'late')|list|length }},
                        {{ attendances|selectattr('status', 'equalto', 'absent')|list|length }}
                    ],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '考勤情况'
                    }
                }
            }
        });
        
        // 答题图表
        var answersCtx = document.getElementById('answersChart').getContext('2d');
        var answersChart = new Chart(answersCtx, {
            type: 'pie',
            data: {
                labels: ['正确', '错误'],
                datasets: [{
                    data: [
                        {{ answers|selectattr('is_correct')|list|length }},
                        {{ answers|rejectattr('is_correct')|list|length }}
                    ],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '答题情况'
                    }
                }
            }
        });
    });
</script>
{% endblock %}
{% endblock %}