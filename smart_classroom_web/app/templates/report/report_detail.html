{% extends 'base.html' %}

{% block title %}报告详情 - {{ report.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ report.title }}</h2>
    <p class="text-muted">日期: {{ report.date.strftime('%Y-%m-%d') }}</p>
    
    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">基本信息</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-3">
                        <span>课程名称:</span>
                        <strong>{{ course.name }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>签到人数:</span>
                        <strong>{{ report.attendance_count }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>题目数量:</span>
                        <strong>{{ report.question_count }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>互动次数:</span>
                        <strong>{{ report.interaction_count }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <span>生成时间:</span>
                        <strong>{{ report.created_at.strftime('%Y-%m-%d %H:%M') }}</strong>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">备注</h5>
                </div>
                <div class="card-body">
                    {% if notes %}
                        <div class="notes-list">
                            {% for note in notes %}
                                <div class="note-item mb-3">
                                    <div class="note-content">{{ note.content }}</div>
                                    <div class="note-meta text-muted">
                                        <small>{{ note.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% if current_user.id == course.teacher_id %}
                                            <form action="{{ url_for('report.delete_note', note_id=note.id) }}" method="post" class="d-inline float-right">
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if not loop.last %}<hr>{% endif %}
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            暂无备注
                        </div>
                    {% endif %}
                    
                    {% if current_user.id == course.teacher_id %}
                        <form action="{{ url_for('report.add_note', report_id=report.id) }}" method="post" class="mt-3">
                            <div class="form-group">
                                <textarea name="content" class="form-control" rows="3" placeholder="添加备注..." required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> 添加备注
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">课堂活动</h5>
                </div>
                <div class="card-body">
                    {% if activities %}
                        <div class="timeline">
                            {% for activity in activities %}
                                <div class="timeline-item">
                                    <div class="timeline-date">
                                        {{ activity.created_at.strftime('%H:%M') }}
                                    </div>
                                    <div class="timeline-content">
                                        <div class="timeline-badge 
                                            {% if activity.type == 'question' %}bg-primary
                                            {% elif activity.type == 'discussion' %}bg-success
                                            {% elif activity.type == 'file_share' %}bg-info
                                            {% elif activity.type == 'random_pick' %}bg-warning
                                            {% elif activity.type == 'broadcast' %}bg-danger
                                            {% elif activity.type == 'attendance' %}bg-secondary
                                            {% else %}bg-dark{% endif %}">
                                            {% if activity.type == 'question' %}<i class="fas fa-question"></i>
                                            {% elif activity.type == 'discussion' %}<i class="fas fa-comments"></i>
                                            {% elif activity.type == 'file_share' %}<i class="fas fa-file-alt"></i>
                                            {% elif activity.type == 'random_pick' %}<i class="fas fa-random"></i>
                                            {% elif activity.type == 'broadcast' %}<i class="fas fa-broadcast-tower"></i>
                                            {% elif activity.type == 'attendance' %}<i class="fas fa-clipboard-check"></i>
                                            {% else %}<i class="fas fa-star"></i>{% endif %}
                                        </div>
                                        <h5>{{ activity.title }}</h5>
                                        {% if activity.description %}
                                            <p>{{ activity.description }}</p>
                                        {% endif %}
                                        <div class="timeline-footer">
                                            <span class="badge 
                                                {% if activity.status == 'active' %}badge-success
                                                {% else %}badge-secondary{% endif %}">
                                                {{ activity.status }}
                                            </span>
                                            {% if activity.participants_count > 0 %}
                                                <span class="badge badge-info">
                                                    <i class="fas fa-users"></i> {{ activity.participants_count }} 人参与
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            暂无活动记录
                        </div>
                    {% endif %}
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-white">
                            <h5 class="mb-0">随机点名</h5>
                        </div>
                        <div class="card-body">
                            {% if random_picks %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>类型</th>
                                                <th>评分</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for pick in random_picks %}
                                                <tr>
                                                    <td>{{ pick.picked_at.strftime('%H:%M') }}</td>
                                                    <td>{{ pick.pick_type }}</td>
                                                    <td>
                                                        {% if pick.score %}
                                                            <span class="badge {% if pick.score > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                                                {{ pick.score }}
                                                            </span>
                                                        {% else %}
                                                            -
                                                        {% endif %}
                                                    </td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    暂无随机点名记录
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header bg-danger text-white">
                            <h5 class="mb-0">评分记录</h5>
                        </div>
                        <div class="card-body">
                            {% if scores %}
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>类型</th>
                                                <th>分数</th>
                                                <th>原因</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for score in scores %}
                                                <tr>
                                                    <td>{{ score.created_at.strftime('%H:%M') }}</td>
                                                    <td>{{ score.target_type }}</td>
                                                    <td>
                                                        <span class="badge {% if score.score > 0 %}badge-success{% else %}badge-danger{% endif %}">
                                                            {{ score.score }}
                                                        </span>
                                                    </td>
                                                    <td>{{ score.reason or '-' }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    暂无评分记录
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .timeline {
        position: relative;
        padding: 20px 0;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 20px;
        width: 2px;
        background: #ddd;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
    }
    
    .timeline-date {
        position: absolute;
        left: -70px;
        top: 0;
        width: 60px;
        text-align: right;
        color: #666;
        font-size: 0.9rem;
    }
    
    .timeline-content {
        position: relative;
        margin-left: 50px;
        background: #fff;
        border-radius: 4px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    }
    
    .timeline-badge {
        position: absolute;
        left: -15px;
        top: 15px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        color: white;
    }
    
    .timeline-footer {
        margin-top: 10px;
    }
    
    .note-item {
        position: relative;
    }
    
    .note-content {
        margin-bottom: 5px;
    }
    
    .note-meta {
        font-size: 0.8rem;
    }
    
    @media (max-width: 767px) {
        .timeline:before {
            left: 40px;
        }
        
        .timeline-date {
            position: relative;
            left: 0;
            top: 0;
            width: 100%;
            text-align: left;
            margin-bottom: 5px;
        }
        
        .timeline-content {
            margin-left: 70px;
        }
        
        .timeline-badge {
            left: 5px;
        }
    }
</style>
{% endblock %}