from app import db
from app.models.device import DeviceStatus
from datetime import datetime, timedelta
from flask import request
from flask_socketio import emit, join_room, leave_room
import json
import threading
import time
from app.models.course import Course, Group, CourseStudent
from app.models.user import User

# 设备状态缓存
device_status_cache = {}

# 设备状态检查间隔（秒）
STATUS_CHECK_INTERVAL = 10

# 设备离线超时（秒）
OFFLINE_TIMEOUT = 30

def get_devices_for_course(course_id):
    """
    获取指定课程的所有设备状态

    Args:
        course_id: 课程ID

    Returns:
        设备状态列表
    """
    course = Course.query.get_or_404(course_id)
    groups = Group.query.filter_by(course_id=course_id).all()
    all_devices = get_all_device_status()
    course_devices = []

    # 教师设备
    teacher_device = next((d for d in all_devices if d['type'] == 'teacher' and d['owner_id'] == course.teacher_id), None)
    if teacher_device:
        teacher_device['role'] = 'teacher'
        teacher_device['name'] = '教师设备'
        course_devices.append(teacher_device)

    # 小组设备
    for group in groups:
        if group.device_id:
            group_device = next((d for d in all_devices if d['device_id'] == group.device_id), None)
            if group_device:
                group_device['role'] = 'group'
                group_device['name'] = f'{group.name}设备'
                group_device['group_id'] = group.id
                course_devices.append(group_device)

    # 学生设备
    student_ids = [cs.student_id for cs in CourseStudent.query.filter_by(course_id=course_id).all()]
    for device in all_devices:
        if device['type'] == 'student' and device['owner_id'] in student_ids:
            student = User.query.get(device['owner_id'])
            if student:
                device['role'] = 'student'
                device['name'] = f'{student.username}的设备'
                course_devices.append(device)

    return course_devices

def initialize_device_monitor(app, socketio):
    """
    初始化设备状态监控
    
    Args:
        app: Flask应用实例
        socketio: SocketIO实例
    """
    # 设备状态更新处理程序
    @socketio.on('device_status_update')
    def handle_device_status_update(data):
        """
        处理设备状态更新
        
        Args:
            data: 设备状态数据
        """
        device_id = data.get('device_id')
        if not device_id:
            return {'error': '缺少设备ID'}, 400
        
        device_type = data.get('type', 'student')
        owner_id = data.get('owner_id')
        course_id = data.get('course_id')
        
        # 检查是否为学生设备，并尝试通过owner_id查找
        device = DeviceStatus.query.filter_by(device_id=device_id).first()

        if not device:
            # 如果设备不存在，创建新设备
            device = DeviceStatus(
                device_id=device_id,
                type=device_type,
                owner_id=owner_id,
                status=data.get('status', 'online'),
                ip_address=request.remote_addr,
                last_ping=datetime.utcnow()
            )
            db.session.add(device)
            print(f"新设备注册: {device_id}, 类型: {device.type}, 所有者: {owner_id}")
        else:
            # 更新现有设备状态
            device.status = data.get('status', device.status)
            device.ip_address = request.remote_addr
            device.last_ping = datetime.utcnow()
            if owner_id:
                device.owner_id = owner_id
            if device_type:
                device.type = device_type
            print(f"设备状态更新: {device_id}, 状态: {device.status}")
        
        db.session.commit()
        
        # 更新缓存
        device_status_cache[device_id] = device.to_dict()
        
        # 广播设备状态更新
        if course_id:
            emit('device_status_changed', device.to_dict(), room=f'monitor_{course_id}')
        
        return {'success': True}
    
    @socketio.on('join_device_monitor')
    def handle_join_device_monitor(data):
        """
        加入设备监控房间
        """
        course_id = data.get('course_id')
        if not course_id:
            return

        join_room(f'monitor_{course_id}')
        
        # 发送当前所有设备状态
        devices = get_devices_for_course(course_id)
        emit('device_status_list', {'devices': devices})
    
    @socketio.on('leave_device_monitor')
    def handle_leave_device_monitor(data):
        """
        离开设备监控房间
        """
        course_id = data.get('course_id')
        if not course_id:
            return
        leave_room(f'monitor_{course_id}')
    
    # 启动设备状态检查线程
    def check_device_status():
        """
        定期检查设备状态，将长时间未更新的设备标记为离线
        """
        with app.app_context():
            while True:
                try:
                    # 获取所有设备
                    devices = DeviceStatus.query.all()
                    offline_threshold = datetime.utcnow() - timedelta(seconds=OFFLINE_TIMEOUT)
                    
                    for device in devices:
                        # 如果设备长时间未更新状态，标记为离线
                        if device.last_ping < offline_threshold and device.status != 'offline':
                            device.status = 'offline'
                            db.session.commit()
                            
                            # 更新缓存
                            device_status_cache[device.device_id] = device.to_dict()
                            
                            # 广播设备状态更新
                            # 获取设备所属的所有课程
                            courses = Course.query.join(CourseStudent).filter(CourseStudent.student_id == device.owner_id).all()
                            for course in courses:
                                socketio.emit('device_status_changed', device.to_dict(), room=f'monitor_{course.id}')
                    
                    # 等待下一次检查
                    time.sleep(STATUS_CHECK_INTERVAL)
                except Exception as e:
                    print(f"设备状态检查错误: {e}")
                    time.sleep(STATUS_CHECK_INTERVAL)
    
    # 启动后台线程
    status_thread = threading.Thread(target=check_device_status)
    status_thread.daemon = True
    status_thread.start()

def get_device_status(device_id):
    """
    获取设备状态
    
    Args:
        device_id: 设备ID
    
    Returns:
        设备状态字典
    """
    # 优先从缓存获取
    if device_id in device_status_cache:
        return device_status_cache[device_id]
    
    # 从数据库获取
    device = DeviceStatus.query.filter_by(device_id=device_id).first()
    if device:
        # 更新缓存
        device_status_cache[device_id] = device.to_dict()
        return device.to_dict()
    
    return None

def get_all_device_status():
    """
    获取所有设备状态
    
    Returns:
        设备状态列表
    """
    devices = DeviceStatus.query.all()
    return [device.to_dict() for device in devices]

def update_device_status_in_db(device_id, status, owner_id=None, type=None):
    """
    直接更新数据库中的设备状态
    
    Args:
        device_id: 设备ID
        status: 新状态
        owner_id: 所有者ID
        type: 设备类型
    """
    device = DeviceStatus.query.filter_by(device_id=device_id).first()
    
    if not device:
        device = DeviceStatus(
            device_id=device_id,
            status=status,
            owner_id=owner_id,
            type=type,
            ip_address=request.remote_addr,
            last_ping=datetime.utcnow()
        )
        db.session.add(device)
    else:
        device.status = status
        device.last_ping = datetime.utcnow()
        if owner_id:
            device.owner_id = owner_id
        if type:
            device.type = type
    
    db.session.commit()
    
    # 更新缓存
    device_status_cache[device_id] = device.to_dict()
    
    # 广播更新
    emit('device_status_changed', device.to_dict(), broadcast=True, room='device_monitor')
