import random
from typing import List, Dict, Any, <PERSON><PERSON>

def random_group_equal(student_ids: List[int], group_count: int) -> Dict[int, List[int]]:
    """
    均等分配随机分组算法
    
    将学生均匀分配到指定数量的小组中，每个小组的学生数量尽可能接近。
    
    Args:
        student_ids: 学生ID列表
        group_count: 分组数量
    
    Returns:
        分组结果字典，键为小组索引（从1开始），值为学生ID列表
    """
    if not student_ids:
        return {}
    
    if group_count < 1:
        group_count = 1
    
    if group_count > len(student_ids):
        group_count = len(student_ids)
    
    # 随机打乱学生顺序
    shuffled_students = student_ids.copy()
    random.shuffle(shuffled_students)
    
    # 计算每个小组的基本学生数量和有额外学生的小组数
    students_per_group = len(shuffled_students) // group_count
    remainder = len(shuffled_students) % group_count
    
    # 分配学生到小组
    groups = {}
    start_idx = 0
    
    for i in range(1, group_count + 1):
        # 计算当前小组的学生数量
        count = students_per_group + (1 if i <= remainder else 0)
        end_idx = start_idx + count
        
        # 分配学生到当前小组
        groups[i] = shuffled_students[start_idx:end_idx]
        
        start_idx = end_idx
    
    return groups

def random_group_random(student_ids: List[int], group_count: int) -> Dict[int, List[int]]:
    """
    完全随机分组算法
    
    将学生完全随机分配到指定数量的小组中，不考虑每个小组的学生数量平衡。
    
    Args:
        student_ids: 学生ID列表
        group_count: 分组数量
    
    Returns:
        分组结果字典，键为小组索引（从1开始），值为学生ID列表
    """
    if not student_ids:
        return {}
    
    if group_count < 1:
        group_count = 1
    
    if group_count > len(student_ids):
        group_count = len(student_ids)
    
    # 随机打乱学生顺序
    shuffled_students = student_ids.copy()
    random.shuffle(shuffled_students)
    
    # 初始化小组
    groups = {i: [] for i in range(1, group_count + 1)}
    
    # 分配学生到小组
    for i, student_id in enumerate(shuffled_students):
        group_idx = (i % group_count) + 1
        groups[group_idx].append(student_id)
    
    return groups

def random_group_by_attribute(student_data: List[Dict[str, Any]], 
                             group_count: int, 
                             attribute: str,
                             balanced: bool = True) -> Dict[int, List[int]]:
    """
    按属性分组算法
    
    根据学生的某个属性进行分组，可以选择是否平衡各个属性值在每个小组中的分布。
    
    Args:
        student_data: 学生数据列表，每个元素是包含学生ID和属性的字典
        group_count: 分组数量
        attribute: 用于分组的属性名
        balanced: 是否平衡各个属性值在每个小组中的分布
    
    Returns:
        分组结果字典，键为小组索引（从1开始），值为学生ID列表
    """
    if not student_data:
        return {}
    
    if group_count < 1:
        group_count = 1
    
    if group_count > len(student_data):
        group_count = len(student_data)
    
    # 按属性值对学生进行分组
    attribute_groups = {}
    for student in student_data:
        attr_value = student.get(attribute)
        if attr_value not in attribute_groups:
            attribute_groups[attr_value] = []
        attribute_groups[attr_value].append(student['id'])
    
    # 初始化分组
    groups = {i: [] for i in range(1, group_count + 1)}
    
    if balanced:
        # 平衡分配：确保每个小组中各个属性值的学生数量尽可能接近
        # 对每个属性值的学生进行分配
        for attr_value, students in attribute_groups.items():
            # 随机打乱学生顺序
            random.shuffle(students)
            
            # 计算每个小组当前的学生数量
            group_sizes = {i: len(groups[i]) for i in groups}
            
            # 按照当前学生数量从少到多排序小组
            sorted_groups = sorted(groups.keys(), key=lambda g: group_sizes[g])
            
            # 分配学生到小组，优先分配给学生数量少的小组
            for i, student_id in enumerate(students):
                group_idx = sorted_groups[i % group_count]
                groups[group_idx].append(student_id)
                # 更新小组学生数量
                group_sizes[group_idx] += 1
                # 如果排序发生变化，重新排序
                if i % group_count == group_count - 1:
                    sorted_groups = sorted(groups.keys(), key=lambda g: group_sizes[g])
    else:
        # 非平衡分配：将相同属性值的学生尽可能分到同一小组
        # 计算每个小组应该分配的学生数量
        total_students = len(student_data)
        students_per_group = total_students // group_count
        remainder = total_students % group_count
        
        target_sizes = {i: students_per_group + (1 if i <= remainder else 0) for i in range(1, group_count + 1)}
        
        # 按照属性值的学生数量从多到少排序
        sorted_attrs = sorted(attribute_groups.keys(), key=lambda a: len(attribute_groups[a]), reverse=True)
        
        # 分配学生到小组
        for attr_value in sorted_attrs:
            students = attribute_groups[attr_value]
            random.shuffle(students)
            
            # 找出当前学生数量最少的小组
            available_groups = [g for g in groups.keys() if len(groups[g]) < target_sizes[g]]
            if not available_groups:
                # 如果所有小组都已满，则平均分配剩余学生
                for i, student_id in enumerate(students):
                    group_idx = (i % group_count) + 1
                    groups[group_idx].append(student_id)
                continue
            
            # 尽可能将相同属性值的学生分到同一小组
            current_group = available_groups[0]
            for student_id in students:
                # 如果当前小组已满，选择下一个可用小组
                if len(groups[current_group]) >= target_sizes[current_group]:
                    available_groups = [g for g in groups.keys() if len(groups[g]) < target_sizes[g]]
                    if not available_groups:
                        # 如果所有小组都已满，则平均分配剩余学生
                        for i, sid in enumerate([student_id] + students[students.index(student_id)+1:]):
                            group_idx = (i % group_count) + 1
                            groups[group_idx].append(sid)
                        break
                    current_group = available_groups[0]
                
                groups[current_group].append(student_id)
    
    return groups

def get_group_algorithm(algorithm: str):
    """
    获取分组算法函数
    
    Args:
        algorithm: 算法名称，支持 'equal'（均等分配）和 'random'（完全随机）
    
    Returns:
        分组算法函数
    """
    algorithms = {
        'equal': random_group_equal,
        'random': random_group_random
    }
    
    return algorithms.get(algorithm, random_group_equal)