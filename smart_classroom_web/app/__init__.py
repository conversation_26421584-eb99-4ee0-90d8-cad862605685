from flask import Flask, request, g
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>, current_user
from flask_socketio import SocketIO

# 初始化扩展
db = SQLAlchemy()
login_manager = LoginManager()
socketio = SocketIO()

@login_manager.user_loader
def load_user(user_id):
    from app.models.user import User
    return User.query.get(int(user_id))

@login_manager.request_loader
def load_user_from_request(request):
    from app.models.user import User
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        try:
            token = auth_header.split(' ')[1]
            return User.verify_auth_token(token)
        except Exception:
            return None
    return None

def create_app(config_name='default'):
    """
    应用工厂函数
    
    Args:
        config_name: 配置名称，默认为'default'
    
    Returns:
        Flask应用实例
    """
    from config import config
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    login_manager.init_app(app)
    login_manager.session_protection = "strong"
    login_manager.login_view = 'auth.login'
    login_manager.login_message = '请先登录以访问此页面'
    login_manager.login_message_category = 'info'
    socketio.init_app(app, cors_allowed_origins="*")

    @app.before_request
    def before_request():
        g.user = current_user
    
    # 确保上传目录存在
    import os
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])
    
    # 注册蓝图
    from app.controllers.main import main as main_blueprint
    app.register_blueprint(main_blueprint)
    
    from app.controllers.auth import auth as auth_blueprint
    app.register_blueprint(auth_blueprint, url_prefix='/auth')

    from app.controllers.api_auth import api_auth as api_auth_blueprint
    app.register_blueprint(api_auth_blueprint, url_prefix='/api/auth')
    
    from app.controllers.course import course as course_blueprint
    app.register_blueprint(course_blueprint, url_prefix='/course')
    
    from app.controllers.resource import resource as resource_blueprint
    app.register_blueprint(resource_blueprint, url_prefix='/resource')
    
    from app.controllers.interaction import interaction as interaction_blueprint
    app.register_blueprint(interaction_blueprint, url_prefix='/interaction')
    
    from app.controllers.broadcast import broadcast as broadcast_blueprint
    app.register_blueprint(broadcast_blueprint, url_prefix='/broadcast')
    
    from app.controllers.group import group as group_blueprint
    app.register_blueprint(group_blueprint, url_prefix='/group')
    
    from app.controllers.report import report as report_blueprint
    app.register_blueprint(report_blueprint, url_prefix='/report')
    
    # 注册错误处理
    from app.controllers.errors import register_error_handlers
    register_error_handlers(app)
    
    # 初始化WebRTC服务
    from app.services.webrtc import initialize_webrtc_handlers
    initialize_webrtc_handlers(socketio)
    
    # 初始化设备状态监控
    from app.services.device_monitor import initialize_device_monitor
    initialize_device_monitor(app, socketio)
    
    # 初始化白板协作服务
    from app.services.whiteboard import WhiteboardNamespace
    socketio.on_namespace(WhiteboardNamespace('/whiteboard'))

    # Socket.IO事件处理
    from flask_socketio import join_room, leave_room

    @socketio.on('join')
    def on_join(data):
        room = data['room']
        join_room(room)
        print(f'Client has entered the room: {room}')

    @socketio.on('leave')
    def on_leave(data):
        room = data['room']
        leave_room(room)
        print(f'Client has left the room: {room}')

    return app
