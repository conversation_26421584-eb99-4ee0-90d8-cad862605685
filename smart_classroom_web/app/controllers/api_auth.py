from flask import Blueprint, request, jsonify
from app.models.user import User
from app import db
from flask_login import login_required, current_user

api_auth = Blueprint('api_auth', __name__)

@api_auth.route('/login', methods=['POST'])
def login():
    """API登录，返回JWT Token"""
    data = request.get_json()
    if not data or not 'email' in data or not 'password' in data:
        return jsonify({'message': '无效的凭证'}), 400

    user = User.query.filter_by(email=data['email']).first()

    if user and user.verify_password(data['password']):
        token = user.generate_auth_token()
        return jsonify({
            'token': token,
            'user': user.to_dict()
        })

    return jsonify({'message': '无效的凭证'}), 401

@api_auth.route('/user', methods=['GET'])
@login_required
def get_current_user():
    """获取当前认证用户的信息"""
    return jsonify(current_user.to_dict())
