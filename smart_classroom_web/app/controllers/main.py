from flask import Blueprint, render_template, current_app
from flask_login import login_required, current_user

main = Blueprint('main', __name__)

@main.route('/')
def index():
    """首页"""
    return render_template('index.html')

@main.route('/dashboard')
@login_required
def dashboard():
    """用户仪表盘"""
    return render_template('dashboard.html')

@main.route('/profile')
@login_required
def profile():
    """用户个人信息页面"""
    return render_template('auth/profile.html')
