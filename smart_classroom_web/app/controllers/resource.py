from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, send_from_directory
from flask_login import login_required, current_user
import os
import uuid
import hashlib
from datetime import datetime
from werkzeug.utils import secure_filename
from app.models.resource import Resource
from app import db

resource = Blueprint('resource', __name__)

def determine_file_type(extension):
    """根据文件扩展名确定文件类型"""
    document_types = {'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'rtf', 'odt', 'ods', 'odp', 'csv', 'md', 'tex'}
    image_types = {'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'tif', 'ico', 'heic', 'raw'}
    video_types = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', '3gp', 'mpeg', 'mpg', 'ts'}
    audio_types = {'mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'opus', 'aiff', 'alac'}
    archive_types = {'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'iso'}
    code_types = {'py', 'js', 'html', 'css', 'java', 'c', 'cpp', 'h', 'php', 'rb', 'go', 'json', 'xml', 'sql'}
    
    if extension in document_types:
        return 'document'
    elif extension in image_types:
        return 'image'
    elif extension in video_types:
        return 'video'
    elif extension in audio_types:
        return 'audio'
    elif extension in archive_types:
        return 'archive'
    elif extension in code_types:
        return 'code'
    else:
        return 'other'

def calculate_file_md5(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def get_allowed_extensions():
    """获取所有允许的文件扩展名"""
    return {
        # 文档类型
        'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'rtf', 'odt', 'ods', 'odp', 'csv', 'md', 'tex',
        # 图片类型
        'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'tif', 'ico', 'heic', 'raw',
        # 视频类型
        'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', '3gp', 'mpeg', 'mpg', 'ts',
        # 音频类型
        'mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'opus', 'aiff', 'alac',
        # 压缩文件类型
        'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'iso',
        # 代码文件类型
        'py', 'js', 'html', 'css', 'java', 'c', 'cpp', 'h', 'php', 'rb', 'go', 'json', 'xml', 'sql'
    }

@resource.route('/')
@login_required
def index():
    """资源列表"""
    # 获取当前用户的资源
    resources = Resource.query.filter_by(owner_id=current_user.id).all()
    
    # 如果用户是教师，也获取他们课程的资源
    if hasattr(current_user, 'is_teacher') and current_user.is_teacher():
        # 导入Course模型
        from app.models.course import Course
        # 获取用户教授的课程
        courses = Course.query.filter_by(teacher_id=current_user.id).all()
        if courses:
            course_resources = Resource.query.filter(
                Resource.course_id.in_([course.id for course in courses])
            ).all()
            # 合并资源列表，去除重复
            resources = list(set(resources + course_resources))
    
    return render_template('resource/index.html', resources=resources)

@resource.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    """上传资源"""
    if request.method == 'POST':
        # 检查是否有文件
        if 'file' not in request.files:
            flash('没有选择文件', 'error')
            return redirect(request.url)
        
        file = request.files['file']
        
        # 如果用户没有选择文件，浏览器也会提交一个没有文件名的空文件
        if file.filename == '':
            flash('没有选择文件', 'error')
            return redirect(request.url)
        
        # 检查文件类型
        allowed_extensions = get_allowed_extensions()
        file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        
        if file_ext not in allowed_extensions:
            flash(f'不支持的文件类型。允许的类型包括文档、图片、视频、音频、压缩文件等。', 'error')
            return redirect(request.url)
        
        # 检查文件大小
        max_size = current_app.config.get('MAX_CONTENT_LENGTH', 100 * 1024 * 1024)  # 默认100MB
        if request.content_length > max_size:
            flash(f'文件太大，请使用分块上传功能上传大文件。最大允许大小: {max_size/(1024*1024):.1f}MB', 'error')
            return redirect(request.url)
        
        # 确保文件名安全
        filename = secure_filename(file.filename)
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # 确定文件类型
        file_type = determine_file_type(file_ext)
        
        # 保存文件
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 计算MD5校验和
        md5_checksum = calculate_file_md5(file_path)
        
        # 获取课程ID（如果有）
        course_id = request.form.get('course_id')
        
        # 创建资源记录
        resource = Resource(
            name=filename,
            type=file_type,
            url=unique_filename,
            size=file_size,
            format=file_ext,
            owner_id=current_user.id,
            course_id=course_id if course_id else None,
            file_metadata={
                'upload_date': datetime.utcnow().isoformat(),
                'md5_checksum': md5_checksum,
                'original_name': filename
            }
        )
        
        db.session.add(resource)
        db.session.commit()
        
        flash('文件上传成功', 'success')
        return redirect(url_for('resource.index'))
    
    # 获取当前用户的课程列表，用于上传页面的课程选择
    from app.models.course import Course
    courses = []
    if hasattr(current_user, 'is_teacher') and current_user.is_teacher:
        courses = Course.query.filter_by(teacher_id=current_user.id).all()
        
    return render_template('resource/upload.html', courses=courses)

@resource.route('/<int:resource_id>')
@login_required
def detail(resource_id):
    """资源详情"""
    resource_item = Resource.query.get_or_404(resource_id)
    
    # todo: 检查权限
    if resource_item.owner_id != current_user.id and not hasattr(current_user, 'is_teacher'):
        flash('您没有权限查看此资源', 'error')
        return redirect(url_for('resource.index'))
    
    return render_template('resource/detail.html', resource=resource_item)

@resource.route('/<int:resource_id>/delete', methods=['POST'])
@login_required
def delete(resource_id):
    """删除资源"""
    resource_item = Resource.query.get_or_404(resource_id)
    
    # 检查权限
    if resource_item.owner_id != current_user.id and not current_user.is_admin:
        flash('您没有权限删除此资源', 'error')
        return redirect(url_for('resource.index'))
    
    try:
        # 删除物理文件
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], resource_item.url)
        if os.path.exists(file_path):
            os.remove(file_path)
        
        # 删除数据库记录
        db.session.delete(resource_item)
        db.session.commit()
        
        flash('资源已成功删除', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除资源时出错: {str(e)}")
        flash('删除资源时出错', 'error')
    
    return redirect(url_for('resource.index'))

@resource.route('/batch-delete', methods=['POST'])
@login_required
def batch_delete():
    """批量删除资源"""
    try:
        data = request.get_json()
        if not data or 'resource_ids' not in data:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        resource_ids = data['resource_ids']
        if not resource_ids or not isinstance(resource_ids, list):
            return jsonify({'success': False, 'message': '资源ID列表无效'}), 400
        
        # 获取要删除的资源
        resources = Resource.query.filter(Resource.id.in_(resource_ids)).all()
        
        # 检查权限（只能删除自己的资源）
        unauthorized_resources = [r for r in resources if r.owner_id != current_user.id and not current_user.is_admin]
        if unauthorized_resources:
            return jsonify({'success': False, 'message': '您没有权限删除某些资源'}), 403
        
        # 删除资源
        deleted_count = 0
        for resource in resources:
            try:
                # 删除物理文件
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], resource.url)
                if os.path.exists(file_path):
                    os.remove(file_path)
                
                # 删除数据库记录
                db.session.delete(resource)
                deleted_count += 1
            except Exception as e:
                current_app.logger.error(f"删除资源 {resource.id} 时出错: {str(e)}")
        
        # 提交事务
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': f'成功删除 {deleted_count} 个资源',
            'deleted_count': deleted_count
        })
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"批量删除资源时出错: {str(e)}")
        return jsonify({'success': False, 'message': f'批量删除资源时出错: {str(e)}'}), 500

@resource.route('/<int:resource_id>/rename', methods=['POST'])
@login_required
def rename(resource_id):
    """重命名资源"""
    resource_item = Resource.query.get_or_404(resource_id)
    
    # 检查权限
    if resource_item.owner_id != current_user.id and not current_user.is_admin:
        return jsonify({'success': False, 'message': '您没有权限重命名此资源'})
    
    new_name = request.form.get('new_name', '').strip()
    if not new_name:
        return jsonify({'success': False, 'message': '名称不能为空'})
    
    try:
        resource_item.name = new_name
        db.session.commit()
        return jsonify({'success': True, 'message': '资源已重命名'})
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"重命名资源时出错: {str(e)}")
        return jsonify({'success': False, 'message': '重命名资源时出错'})

@resource.route('/upload/chunked', methods=['POST'])
@login_required
def upload_chunked():
    """分块上传大文件"""
    try:
        # 获取分块信息
        chunk = request.files.get('file')
        chunk_number = int(request.form.get('chunkNumber'))
        total_chunks = int(request.form.get('totalChunks'))
        filename = request.form.get('filename')
        file_id = request.form.get('fileId', str(uuid.uuid4()))
        
        if not chunk or not filename:
            return jsonify({'success': False, 'message': '缺少必要参数'}), 400
        
        # 确保文件名安全
        filename = secure_filename(filename)
        
        # 检查文件类型
        file_ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        allowed_extensions = {
            # 文档类型
            'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'rtf', 'odt', 'ods', 'odp', 'csv', 'md', 'tex',
            # 图片类型
            'jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'tif', 'ico', 'heic', 'raw',
            # 视频类型
            'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v', '3gp', 'mpeg', 'mpg', 'ts',
            # 音频类型
            'mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a', 'wma', 'opus', 'aiff', 'alac',
            # 压缩文件类型
            'zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'iso',
            # 代码文件类型
            'py', 'js', 'html', 'css', 'java', 'c', 'cpp', 'h', 'php', 'rb', 'go', 'json', 'xml', 'sql'
        }
        
        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'message': '不支持的文件类型'}), 400
        
        # 生成唯一的临时文件夹用于存储分块
        temp_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'temp', file_id)
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir)
        
        # 保存当前分块
        chunk_path = os.path.join(temp_dir, f'chunk_{chunk_number}')
        chunk.save(chunk_path)
        
        # 检查是否所有分块都已上传
        all_chunks_uploaded = True
        for i in range(total_chunks):
            if not os.path.exists(os.path.join(temp_dir, f'chunk_{i}')):
                all_chunks_uploaded = False
                break
        
        if all_chunks_uploaded:
            # 合并所有分块
            final_filename = f"{uuid.uuid4().hex}_{filename}"
            final_path = os.path.join(current_app.config['UPLOAD_FOLDER'], final_filename)
            
            with open(final_path, 'wb') as outfile:
                for i in range(total_chunks):
                    chunk_file = os.path.join(temp_dir, f'chunk_{i}')
                    if os.path.exists(chunk_file):
                        with open(chunk_file, 'rb') as infile:
                            outfile.write(infile.read())
            
            # 清理临时文件
            import shutil
            shutil.rmtree(temp_dir)
            
            # 获取文件大小和扩展名
            file_size = os.path.getsize(final_path)
            
            # 确定文件类型
            file_type = determine_file_type(file_ext)
            
            # 获取课程ID（如果有）
            course_id = request.form.get('course_id')
            
            # 创建资源记录
            resource = Resource(
                name=filename,
                type=file_type,
                url=final_filename,
                size=file_size,
                format=file_ext,
                owner_id=current_user.id,
                course_id=course_id if course_id else None,
                file_metadata={
                    'chunked_upload': True,
                    'chunks': total_chunks,
                    'original_name': filename,
                    'upload_date': datetime.utcnow().isoformat(),
                    'md5_checksum': calculate_file_md5(final_path)
                }
            )
            
            db.session.add(resource)
            db.session.commit()
            
            return jsonify({
                'success': True, 
                'message': '文件上传成功',
                'resource_id': resource.id,
                'resource_name': resource.name,
                'resource_type': resource.type,
                'resource_size': resource.size
            })
        
        # 计算已上传的分块数量
        uploaded_chunks = 0
        for i in range(total_chunks):
            if os.path.exists(os.path.join(temp_dir, f'chunk_{i}')):
                uploaded_chunks += 1
        
        # 计算上传进度
        progress = int((uploaded_chunks / total_chunks) * 100)
        
        return jsonify({
            'success': True, 
            'message': f'分块 {chunk_number + 1}/{total_chunks} 上传成功',
            'fileId': file_id,
            'chunksReceived': uploaded_chunks,
            'totalChunks': total_chunks,
            'progress': progress
        })
    except Exception as e:
        current_app.logger.error(f"分块上传出错: {str(e)}")
        # 尝试清理临时文件
        try:
            if 'temp_dir' in locals() and os.path.exists(temp_dir):
                import shutil
                shutil.rmtree(temp_dir)
        except:
            pass
        return jsonify({'success': False, 'message': f'上传出错: {str(e)}'}), 500

@resource.route('/download/<int:resource_id>')
@login_required
def download(resource_id):
    """下载资源"""
    resource_item = Resource.query.get_or_404(resource_id)
    
    # 检查权限
    can_download = False
    
    if current_user.is_teacher():
        # 教师可以下载自己的资源和自己教授课程的资源
        if resource_item.owner_id == current_user.id:
            can_download = True
        elif resource_item.course_id:
            from app.models.course import Course
            course = Course.query.get(resource_item.course_id)
            if course and course.teacher_id == current_user.id:
                can_download = True
    else:
        # 学生可以下载所属课程的资源
        if resource_item.course_id:
            from app.models.course import CourseStudent
            course_student = CourseStudent.query.filter_by(
                course_id=resource_item.course_id,
                student_id=current_user.id
            ).first()
            if course_student:
                can_download = True
    
    if not can_download:
        flash('您没有权限下载此资源', 'error')
        return redirect(url_for('resource.search'))
    
    return send_from_directory(
        current_app.config['UPLOAD_FOLDER'],
        resource_item.url,
        as_attachment=True,
        download_name=resource_item.name
    )
@resource.route('/upload-chat-file', methods=['POST'])
@login_required
def upload_chat_file():
    """上传聊天文件"""
    try:
        # 检查是否有文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'message': '没有选择文件'}), 400
        
        # 检查文件大小（限制为10MB）
        max_size = 10 * 1024 * 1024
        if request.content_length > max_size:
            return jsonify({'success': False, 'message': '文件大小不能超过10MB'}), 400
        
        # 检查文件类型
        allowed_extensions = get_allowed_extensions()
        file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        
        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'message': '不支持的文件类型'}), 400
        
        # 确保文件名安全
        filename = secure_filename(file.filename)
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        
        # 确定文件类型
        file_type = determine_file_type(file_ext)
        
        # 保存文件
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        # 计算MD5校验和
        md5_checksum = calculate_file_md5(file_path)
        
        # 获取课程ID
        course_id = request.form.get('course_id')
        
        # 创建资源记录
        resource = Resource(
            name=filename,
            type=file_type,
            url=unique_filename,
            size=file_size,
            format=file_ext,
            owner_id=current_user.id,
            course_id=course_id if course_id else None,
            file_metadata={
                'upload_date': datetime.utcnow().isoformat(),
                'md5_checksum': md5_checksum,
                'original_name': filename,
                'chat_file': True  # 标记为聊天文件
            }
        )
        
        db.session.add(resource)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'resource_id': resource.id,
            'resource_name': resource.name,
            'resource_type': resource.type,
            'resource_size': resource.size
        })
        
    except Exception as e:
        current_app.logger.error(f"聊天文件上传出错: {str(e)}")
        return jsonify({'success': False, 'message': f'上传出错: {str(e)}'}), 500

@resource.route('/course/<int:course_id>')
@login_required
def course_resources(course_id):
    """获取课程相关的资源"""
    from app.models.course import Course
    
    course = Course.query.get_or_404(course_id)
    
    # 检查权限
    if course.teacher_id != current_user.id and not current_user in course.students:
        flash('您没有权限查看此课程的资源', 'error')
        return redirect(url_for('resource.index'))
    
    resources = Resource.query.filter_by(course_id=course_id).all()
    
    return render_template('resource/index.html', resources=resources, course=course)

@resource.route('/search')
@login_required
def search():
    """搜索资源"""
    query = request.args.get('q', '')
    resource_type = request.args.get('type', '')
    course_id = request.args.get('course_id', '')
    sort_by = request.args.get('sort_by', 'uploaded_at')
    sort_order = request.args.get('sort_order', 'desc')
    
    # 导入Course模型
    from app.models.course import Course, CourseStudent
    
    # 构建基本查询
    base_query = Resource.query
    
    # 添加搜索条件
    if query:
        base_query = base_query.filter(Resource.name.ilike(f'%{query}%'))
    
    if resource_type:
        base_query = base_query.filter(Resource.type == resource_type)
    
    if course_id:
        base_query = base_query.filter(Resource.course_id == course_id)
    
    # 获取用户可访问的课程
    if current_user.is_teacher():
        # 教师可以访问自己教授的课程资源
        courses = Course.query.filter_by(teacher_id=current_user.id).all()
        course_ids = [course.id for course in courses]
    else:
        # 学生可以访问已加入的课程资源
        course_students = CourseStudent.query.filter_by(student_id=current_user.id).all()
        course_ids = [cs.course_id for cs in course_students]
    
    # 权限过滤：用户可以看到自己的资源和所属课程的资源
    if current_user.is_teacher():
        # 教师可以看到自己的资源和自己教授课程的所有资源
        base_query = base_query.filter(
            (Resource.owner_id == current_user.id) | 
            (Resource.course_id.in_(course_ids)) if course_ids else (Resource.owner_id == current_user.id)
        )
    else:
        # 学生只能看到所属课程的资源（不包括自己上传的资源，除非是课程资源）
        if course_ids:
            base_query = base_query.filter(Resource.course_id.in_(course_ids))
        else:
            # 如果学生没有加入任何课程，返回空结果
            base_query = base_query.filter(Resource.id == -1)  # 永远不会匹配的条件
    
    # 排序
    if sort_by == 'name':
        if sort_order == 'asc':
            base_query = base_query.order_by(Resource.name.asc())
        else:
            base_query = base_query.order_by(Resource.name.desc())
    elif sort_by == 'type':
        if sort_order == 'asc':
            base_query = base_query.order_by(Resource.type.asc())
        else:
            base_query = base_query.order_by(Resource.type.desc())
    elif sort_by == 'size':
        if sort_order == 'asc':
            base_query = base_query.order_by(Resource.size.asc())
        else:
            base_query = base_query.order_by(Resource.size.desc())
    else:  # 默认按上传时间排序
        if sort_order == 'asc':
            base_query = base_query.order_by(Resource.uploaded_at.asc())
        else:
            base_query = base_query.order_by(Resource.uploaded_at.desc())
    
    # 执行查询
    resources = base_query.all()
    
    # 获取所有课程，用于筛选
    if current_user.is_teacher():
        courses = Course.query.filter_by(teacher_id=current_user.id).all()
    else:
        courses = Course.query.join(CourseStudent).filter(CourseStudent.student_id == current_user.id).all()
    
    return render_template('resource/search.html', 
                          resources=resources, 
                          query=query, 
                          type=resource_type,
                          course_id=course_id,
                          sort_by=sort_by,
                          sort_order=sort_order,
                          courses=courses)