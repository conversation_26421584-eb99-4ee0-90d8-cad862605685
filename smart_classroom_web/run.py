import os
import eventlet
eventlet.monkey_patch()

from app import create_app, db, socketio
from flask_migrate import Migrate

# 从环境变量获取配置，默认为开发环境
config_name = os.environ.get('FLASK_CONFIG') or 'default'
app = create_app(config_name)

# 初始化Flask-Migrate
migrate = Migrate(app, db)

@app.cli.command()
def init_db():
    """初始化数据库"""
    db.create_all()
    print('数据库初始化完成')

@app.cli.command()
def create_admin():
    """创建管理员用户"""
    from app.models.user import User
    
    admin = User.query.filter_by(email='<EMAIL>').first()
    if admin:
        print('管理员用户已存在')
        return
    
    admin = User(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        role='teacher'
    )
    db.session.add(admin)
    db.session.commit()
    print('管理员用户创建成功: <EMAIL> / admin123')

if __name__ == '__main__':
    # 确保数据库表存在
    with app.app_context():
        db.create_all()
    
    # 使用socketio运行应用，并启用HTTPS
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, 
                 allow_unsafe_werkzeug=True, certfile='cert.pem', keyfile='key.pem')